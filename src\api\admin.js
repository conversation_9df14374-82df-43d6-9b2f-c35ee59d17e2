import request from './request'

// 上传OSS对象存储
export function uploadOSS(data) {
  return request({
    url: '/junhengai/resource/oss/upload',
    method: 'post',
    data
  })
}


export function getAppList(params) {
  return request({
    url: '/junhengai/system/store/appList',
    method: 'get',
    params
  })
}

export function addApp(data) {
  return request({
    url: '/junhengai/system/store',
    method: 'post',
    data
  })
}

export function updateApp(data) {
  return request({
    url: '/junhengai/system/store',
    method: 'put',
    data
  })
}

export function deleteApp(id) {
  return request({
    url: `/junhengai/system/store/${id}`,
    method: 'delete'
  })
}


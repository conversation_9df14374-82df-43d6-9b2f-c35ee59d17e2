import request from './request'

// 获取单词章节
export function getWordChapters(params) {
  return request({
    url: '/junhengai/word/book/period/list',
    method: 'get',
    params
  })
}

// 根据章节查询单词列表
export function getWordsByChapter(params) {
  return request({
    url: '/junhengai/word/collection/list',
    method: 'get',
    params
  })
}


// 跟读设置查询
export function getFollowReadingSetting(userId, params) {
  return request({
    url: `/junhengai/word/follow/read/set/get/by/${userId}`,
    method: 'get',
    params
  })
}

// 跟读设置保存
export function saveFollowReadSetting(data) {
  return request({
    url: '/junhengai/word/follow/read/set',
    method: 'post',
    data
  })
}

// 听写设置查询
export function getDictationSetting(userId, params) {
  return request({
    url: `/junhengai/word/dictations/set/get/by/${userId}`,
    method: 'get',
    params
  })
}

// 听写设置保存
export function saveDictationSetting(data) {
  return request({
    url: '/junhengai/word/dictations/set',
    method: 'post',
    data
  })
}


// 跟读/听写记录保存
export function saveWordRecord(data) {
  return request({
    url: '/junhengai/word/record',
    method: 'post',
    data
  })
}

// 查询跟读/听写单词记录保存
export function getDictationRecordList(uid, params) {
  return request({
    url: `/junhengai/word/record/list/${uid}`,
    method: 'get',
    params
  })
}

// 查询跟读/听写单词详情
export function getDictationRecordDetail(recordId, params) {
  return request({
    url: `/junhengai/word/record/word/list/${recordId}`,
    method: 'get',
    params
  })
}


// 查询上次操作年级，学期，章节
export function getUserLastOperation(params) {
  return request({
    url: `/junhengai/word/book/period/getLatest`,
    method: 'get',
    params
  })
}
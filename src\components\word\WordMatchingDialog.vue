<template>
  <el-dialog
    v-model="dialogVisible"
    width="100%"
    :close-on-click-modal="false"
    :before-close="handleClose"
    modal-class="word-matching-dialog"
    top="0"
    fullscreen
    destroy-on-close
    :show-close="false"
  >
    <div class="word-matching-container">
      <div class="custom-dialog-header">
        <div class="dialog-title">单词消消乐</div>
      </div>
      <!-- 海洋动画层 -->
      <div class="ocean-animation-layer">
        <!-- 小鱼动画 -->
        <div class="fish fish-left-1"></div>
        <div class="fish fish-left-2"></div>
        <div class="fish fish-left-3"></div>
        <div class="fish fish-left-4"></div>
        <div class="fish fish-right-1"></div>
        <div class="fish fish-right-2"></div>
        <div class="fish fish-right-3"></div>
        <div class="fish fish-right-4"></div>
        
        <!-- 海草装饰 -->
        <div class="seaweed seaweed-1-left"></div>
        <div class="seaweed seaweed-1-right"></div>
        <div class="seaweed seaweed-2-left"></div>
        <div class="seaweed seaweed-2-right"></div>
        <div class="seaweed seaweed-1-center"></div>
        <div class="seaweed seaweed-2-center"></div>
        
        <!-- 珊瑚装饰 -->
        <div class="coral coral-1-left"></div>
        <div class="coral coral-1-right"></div>
        <div class="coral coral-1-center"></div>
      </div>

      <!-- 游戏设置和控制按钮 -->
      <div class="settings-section">
        <!-- New Group Left -->
        <div class="settings-group-left">
          <div class="setting-item">
            <span class="setting-label">游戏时间：</span>
            <div class="time-input-container">
            <el-input
              v-model.number="gameTime"
              type="number"
              :min="1"
              size="large"
              :disabled="isPlaying || isPaused"
              class="time-input"
              @input="validateGameTime"
              placeholder="120"
            />
            <span class="time-unit">秒</span>
            </div>
          </div>
          <!-- Moved Timer -->
          <div class="time-counter">
            <el-icon class="time-icon"><Timer /></el-icon>
            <span class="time-text">{{ formatTime(remainingTime) }}</span>
          </div>
        </div>

        <!-- New Group Center -->
        <div class="settings-group-center">
          <!-- Moved Progress Counter (modified) -->
          <div class="progress-counter">
            <!-- Removed progress-header containing text -->
            <el-progress
              :percentage="progressPercentage"
              :stroke-width="20"
              :show-text="false"
              :color="progressBarColor"
            ></el-progress>
          </div>
        </div>
      </div>
      <!-- 游戏区域 -->
      <div v-if="!gameOver" class="game-area">
        <!-- 第1列：中文 -->
        <div class="word-column column-1">
          <div
              v-for="(word, index) in column1Words"
              :key="word.id + '-col1'"
              class="word-card"
              :class="{ 
                'selected': selectedColumn === 1 && selectedIndex === index, 
                'error-animation': errorIndices.includes(index + '-col1'),
                'success-animation': successIndices.includes(index + '-col1'),
                'matched': word.isMatched,
                [word.colorType]: word.colorType
              }"
              @click="!word.isMatched && selectWord(1, index, word)"
            >
              <div v-if="!word.isMatched || successIndices.includes(index + '-col1')" class="word-text">{{ word.text }}</div>
            </div>
        </div>

        <!-- 第2列：中文 -->
        <div class="word-column column-2">
          <div
              v-for="(word, index) in column2Words"
              :key="word.id + '-col2'"
              class="word-card"
              :class="{ 
                'selected': selectedColumn === 2 && selectedIndex === index, 
                'error-animation': errorIndices.includes(index + '-col2'),
                'success-animation': successIndices.includes(index + '-col2'),
                'matched': word.isMatched,
                [word.colorType]: word.colorType
              }"
              @click="!word.isMatched && selectWord(2, index, word)"
            >
              <div v-if="!word.isMatched || successIndices.includes(index + '-col2')" class="word-text">{{ word.text }}</div>
            </div>
        </div>

        <!-- 第3列：英文 -->
        <div class="word-column column-3">
          <div
              v-for="(word, index) in column3Words"
              :key="word.id + '-col3'"
              class="word-card"
              :class="{ 
                'selected': selectedColumn === 3 && selectedIndex === index, 
                'error-animation': errorIndices.includes(index + '-col3'),
                'success-animation': successIndices.includes(index + '-col3'),
                'matched': word.isMatched,
                [word.colorType]: word.colorType
              }"
              @click="!word.isMatched && selectWord(3, index, word)"
            >
              <div v-if="!word.isMatched || successIndices.includes(index + '-col3')" class="word-text">{{ word.text }}</div>
            </div>
        </div>

        <!-- 第4列：英文 -->
        <div class="word-column column-4">
          <div
              v-for="(word, index) in column4Words"
              :key="word.id + '-col4'"
              class="word-card"
              :class="{ 
                'selected': selectedColumn === 4 && selectedIndex === index, 
                'error-animation': errorIndices.includes(index + '-col4'),
                'success-animation': successIndices.includes(index + '-col4'),
                'matched': word.isMatched,
                [word.colorType]: word.colorType
              }"
              @click="!word.isMatched && selectWord(4, index, word)"
            >
              <div v-if="!word.isMatched || successIndices.includes(index + '-col4')" class="word-text">{{ word.text }}</div>
            </div>
        </div>
      </div>

      <!-- 控制按钮区域 -->
      <div v-if="!gameOver" class="control-section">
        <div class="control-button-container">
          <button class="custom-exit-button" @click="handleClose">
            <div class="exit-button-content">
              <span class="exit-text">关闭</span>
            </div>
          </button>
          <button
            :class="['custom-control-button', isPlaying ? 'pause-button' : isPaused ? 'resume-button' : 'start-button']"
            @click="togglePlayPause"
          >
            <div class="button-content">
              <div class="button-icon">
                <svg v-if="isPlaying" viewBox="0 0 24 24" width="20" height="20">
                  <path fill="currentColor" d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                </svg>
                <svg v-else viewBox="0 0 24 24" width="20" height="20">
                  <path fill="currentColor" d="M8 5v14l11-7z"/>
                </svg>
              </div>
              <span class="button-text">{{ buttonText }}</span>
            </div>
          </button>
        </div>
      </div>

      <!-- 游戏结束界面 -->
      <div v-if="gameOver" class="game-over-container">
        <div class="game-over-content">
          <div class="game-over-title">游戏结束</div>
          <div class="game-over-stats">
            <div class="stat-item">
              <div class="stat-label">游戏时长</div>
              <div class="stat-value">{{ formatTime(actualGameDuration) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">单词总数</div>
              <div class="stat-value">{{ totalWords }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">未消除</div>
              <div class="stat-value unmatch-value">{{ totalWords - matchedCount }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">消除成功</div>
              <div class="stat-value success-value">{{ matchedCount }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">消除失败</div>
              <div class="stat-value error-value">{{ errorCount }}</div>
            </div>
          </div>

          <!-- 再来一次按钮 -->
          <div class="restart-button-container">
            <button class="custom-exit-button game-over-exit" @click="handleClose">
              <div class="exit-button-content">
                <span class="exit-text">关闭</span>
              </div>
            </button>
            <button class="custom-restart-button" @click="restartGame">
              <div class="restart-button-content">
                <div class="restart-icon">
                  <svg viewBox="0 0 24 24" width="24" height="24">
                    <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                  </svg>
                </div>
                <span class="restart-text">再来一次</span>
              </div>
            </button>
          </div>

          <div class="confetti-container" ref="confettiContainer"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, defineComponent, watch, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Timer } from '@element-plus/icons-vue'
import confetti from 'canvas-confetti'
import successSoundURL from '@/assets/word/success.mp3'
import failSoundURL from '@/assets/word/fail.mp3'
import startSoundURL from '@/assets/word/start.mp3'
import result0SoundURL from '@/assets/word/result-0.mp3'
import result1SoundURL from '@/assets/word/result-1.mp3'
import result2SoundURL from '@/assets/word/result-2.mp3'
import { useStore } from 'vuex'
import { saveWordRecord } from '../../api/word'

export default defineComponent({
  name: 'WordMatchingDialog',
  components: {
    Timer
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedWords: {
      type: Array,
      required: true
    },
    bookId: {
      type: String,
      default: ''
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const store = useStore()
    const dialogVisible = ref(props.visible)
    const userId = computed(() => store.getters.userId)
    // 消消乐记录是否已保存（每次打开弹窗只保存一次）
    const recordSaved = ref(false)
    const gameTime = ref(60) // 默认最小时间60秒
    const remainingTime = ref(0)
    const isPlaying = ref(false)
    const isPaused = ref(false)
    const gameOver = ref(false)
    const matchedCount = ref(0)
    const errorCount = ref(0)
    const totalWords = ref(0)
    const isAnimating = ref(false)
    const actualGameDuration = ref(0) // 实际游戏时长（秒）

    const column1Words = ref([])
    const column2Words = ref([])
    const column3Words = ref([])
    const column4Words = ref([])
    const wordPool = ref([])

    const selectedColumn = ref(null)
    const selectedIndex = ref(null)
    const errorIndices = ref([])
    const successIndices = ref([]) // 添加成功动画索引

    const confettiContainer = ref(null)

    let gameTimer = null
    let successAudio = null
    let errorAudio = null
    let startAudio = null
    let result0Audio = null
    let result1Audio = null
    let result2Audio = null

    // 为气泡生成随机样式
    const getBubbleRandomStyle = (index) => {
      return {
        left: `${Math.random() * 98}%`, // 0-98% 以防止完全贴边
        animationDelay: `${Math.random() * 15}s`, // 随机延迟，最长15s
        animationDuration: `${8 + Math.random() * 10}s`, // 动画时长 8-18s
        // 随机大小
        width: `${8 + Math.random() * 12}px`,
        height: `${8 + Math.random() * 12}px`,
      };
    };

    // 计算按钮文本
    const buttonText = computed(() => {
      if (isPlaying.value) return '暂停'
      if (isPaused.value) return '继续'
      return 'GO'
    })

    // 计算进度百分比
    const progressPercentage = computed(() => {
      if (totalWords.value === 0) return 0
      return (matchedCount.value / totalWords.value) * 100
    })

    // 3. 添加进度条颜色计算属性
    const progressBarColor = computed(() => {
      // 根据进度返回不同颜色
      const percentage = progressPercentage.value;
      if (percentage < 30) return '#e6a23c';
      if (percentage < 60) return '#409eff';
      return '#67c23a';
    });

    // 验证游戏时间输入
    const validateGameTime = (value) => {
      const numValue = parseInt(value)
      if (isNaN(numValue) || numValue < 1) {
        gameTime.value = 1
      } else {
        gameTime.value = numValue
      }
    }

    // 格式化时间
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }

    // 计算默认游戏时间
    const calculateDefaultGameTime = (wordCount) => {
      // 每10个单词增加60秒
      const baseTime = 60 // 基础时间60秒
      const timePerGroup = 60 // 每组增加时间60秒
      const wordsPerGroup = 10 // 每组单词数10个
      
      // 计算需要多少组时间（向上取整）
      const groupCount = Math.ceil(wordCount / wordsPerGroup)
      
      // 返回计算后的时间
      return baseTime + (groupCount - 1) * timePerGroup
    }

    // 监听selectedWords变化，更新默认游戏时间
    watch(() => props.selectedWords, (newWords) => {
      if (newWords && newWords.length > 0 && !isPlaying.value) {
        // 只在游戏未开始时更新默认时间
        gameTime.value = calculateDefaultGameTime(newWords.length)
      }
    }, { immediate: true })

    // 初始化游戏
    const initGame = (resetGameTime = false) => {
      // 重置游戏状态
      gameOver.value = false
      matchedCount.value = 0
      errorCount.value = 0
      actualGameDuration.value = 0 // 重置实际游戏时长
      // 只在需要重置时间或游戏时间无效时设置游戏时间
      if (resetGameTime || !gameTime.value || gameTime.value <= 0) {
        gameTime.value = calculateDefaultGameTime(props.selectedWords.length)
      }
      remainingTime.value = gameTime.value
      selectedColumn.value = null
      selectedIndex.value = null
      errorIndices.value = []
      successIndices.value = []
      column1Words.value = []  // 清空第1列单词列表
      column2Words.value = []  // 清空第2列单词列表
      column3Words.value = []  // 清空第3列单词列表
      column4Words.value = []  // 清空第4列单词列表

      // 初始化单词池 - 每次都使用最新的selectedWords
      wordPool.value = props.selectedWords.map(word => ({
        ...word,
        matched: false
      }))

      totalWords.value = wordPool.value.length

      // 初始化游戏区域单词
      shuffleGameWords()

      // 初始化音效
      initAudio()
    }

    // 初始化音效
    const initAudio = () => {
      try {
        successAudio = new Audio(successSoundURL)
        errorAudio = new Audio(failSoundURL)
        startAudio = new Audio(startSoundURL)
        result0Audio = new Audio(result0SoundURL)
        result1Audio = new Audio(result1SoundURL)
        result2Audio = new Audio(result2SoundURL)
      } catch (error) {
        console.error('初始化音效失败:', error)
      }
    }

    // 播放音效
    const playAudio = (type) => {
      try {
        if (type === 'success' && successAudio) {
          successAudio.currentTime = 0
          successAudio.play()
        } else if (type === 'error' && errorAudio) {
          errorAudio.currentTime = 0
          errorAudio.play()
        } else if (type === 'start' && startAudio) {
          startAudio.currentTime = 0
          startAudio.play()
        } else if (type === 'result') {
          // 根据正确率播放不同的结束音效
          const correctRate = totalWords.value > 0 ? matchedCount.value / totalWords.value : 0
          let audioToPlay = null
          
          if (correctRate < 0.6 && result0Audio) {
            // 正确率小于60%，播放result-0.mp3
            audioToPlay = result0Audio
          } else if (correctRate < 0.8 && result1Audio) {
            // 正确率小于80%，播放result-1.mp3
            audioToPlay = result1Audio
          } else if (result2Audio) {
            // 正确率大于等于80%，播放result-2.mp3
            audioToPlay = result2Audio
          }
          
          if (audioToPlay) {
            audioToPlay.currentTime = 0
            audioToPlay.play()
          }
        }
      } catch (error) {
        console.error('播放音效失败:', error)
      }
    }

    // 打乱游戏区域单词
    const shuffleGameWords = () => {
      // 获取未匹配的单词
      const availableWords = wordPool.value.filter(word => !word.matched)

      // 如果没有可用单词，游戏结束
      if (availableWords.length === 0) {
        endGame(true)
        return
      }

      // 每次显示10个单词对（每个单词对包含英文和中文两个卡片，总共20个卡片）
      const displayCount = Math.min(10, availableWords.length)
      const selectedWords = availableWords.slice(0, displayCount)

      // 定义4种背景颜色类型
      const colorTypes = ['color-1', 'color-2', 'color-3', 'color-4']

      // 创建中文和英文单词数组
      const chineseWords = []
      const englishWords = []
      
      // 为每个单词创建两个版本（英文和中文），分别放入对应数组
      selectedWords.forEach(word => {
        // 为每个单词对随机分配两种不同的颜色
        const availableColors = [...colorTypes]
        const chineseColor = availableColors[Math.floor(Math.random() * availableColors.length)]
        availableColors.splice(availableColors.indexOf(chineseColor), 1) // 移除已选择的颜色
        const englishColor = availableColors[Math.floor(Math.random() * availableColors.length)]

        // 中文版本
        chineseWords.push({
          id: word.id,
          text: word.chinese,
          isEnglish: false,
          originalWord: word,
          isMatched: false,
          colorType: chineseColor
        })
        
        // 英文版本
        englishWords.push({
          id: word.id,
          text: word.english,
          isEnglish: true,
          originalWord: word,
          isMatched: false,
          colorType: englishColor
        })
      })

      // 打乱中文和英文单词数组
      for (let i = chineseWords.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        ;[chineseWords[i], chineseWords[j]] = [chineseWords[j], chineseWords[i]]
      }

      for (let i = englishWords.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        ;[englishWords[i], englishWords[j]] = [englishWords[j], englishWords[i]]
      }

      // 将中文单词分配到第1、2列，英文单词分配到第3、4列
      const wordsPerColumn = Math.ceil(chineseWords.length / 2)
      
      // 第1、2列显示中文
      column1Words.value = chineseWords.slice(0, wordsPerColumn)
      column2Words.value = chineseWords.slice(wordsPerColumn)
      
      // 第3、4列显示英文
      column3Words.value = englishWords.slice(0, wordsPerColumn)
      column4Words.value = englishWords.slice(wordsPerColumn)
    }

    // 选择单词
    const selectWord = (column, index, word) => {
      // 如果正在播放动画，禁止点击
      if (isAnimating.value) {
        return
      }

      if (!isPlaying.value) {
        ElMessage.warning('请先开始游戏');
        return;
      }

      // 如果点击的是已选中的单词，取消选择
      if (selectedColumn.value === column && selectedIndex.value === index) {
        selectedColumn.value = null
        selectedIndex.value = null
        return
      }

      // 如果已经有选中的单词
      if (selectedColumn.value !== null && selectedIndex.value !== null) {
        // 获取之前选中的单词
        const previousWord = getWordByColumnAndIndex(selectedColumn.value, selectedIndex.value)
        
        // 检查是否匹配
        if (previousWord && previousWord.originalWord.id === word.originalWord.id && 
            previousWord.isEnglish !== word.isEnglish) {
          // 匹配成功
          handleMatchSuccess(word.originalWord, selectedColumn.value, selectedIndex.value, column, index)
        } else {
          // 匹配失败
          handleMatchError(selectedColumn.value, selectedIndex.value, column, index)
        }
      } else {
        // 选择当前单词
        selectedColumn.value = column
        selectedIndex.value = index
      }
    }

    // 根据列和索引获取单词
    const getWordByColumnAndIndex = (column, index) => {
      switch (column) {
        case 1: return column1Words.value[index]
        case 2: return column2Words.value[index]
        case 3: return column3Words.value[index]
        case 4: return column4Words.value[index]
        default: return null
      }
    }

    // 从指定列移除单词
    const removeWordFromColumn = (column, index) => {
      switch (column) {
        case 1: column1Words.value.splice(index, 1); break
        case 2: column2Words.value.splice(index, 1); break
        case 3: column3Words.value.splice(index, 1); break
        case 4: column4Words.value.splice(index, 1); break
      }
    }

    // 将指定位置的单词标记为已消除
    const markWordAsMatched = (column, index) => {
      let targetArray
      switch (column) {
        case 1: targetArray = column1Words.value; break
        case 2: targetArray = column2Words.value; break
        case 3: targetArray = column3Words.value; break
        case 4: targetArray = column4Words.value; break
        default: return
      }
      
      if (targetArray[index]) {
        targetArray[index].isMatched = true
      }
    }

    // 检查是否所有列都为空或已匹配
    const areAllColumnsEmpty = () => {
      const allWords = [
        ...column1Words.value,
        ...column2Words.value,
        ...column3Words.value,
        ...column4Words.value
      ]
      return allWords.length === 0 || allWords.every(word => word.isMatched)
    }

    // 处理匹配成功
    const handleMatchSuccess = (matchedWord, prevColumn, prevIndex, currentColumn, currentIndex) => {
      // 设置动画状态为true
      isAnimating.value = true

      // 播放成功音效
      playAudio('success')

      // 添加成功动画类，确保两个单词同时开始动画
      successIndices.value = [`${prevIndex}-col${prevColumn}`, `${currentIndex}-col${currentColumn}`]

      // 更新单词池中的匹配状态
      const wordIndex = wordPool.value.findIndex(word => word.id === matchedWord.id)
      if (wordIndex !== -1) {
        wordPool.value[wordIndex].matched = true
      }

      // 增加匹配成功计数
      matchedCount.value++

      // 同时标记两个单词为已消除，保持位置不变动
      markWordAsMatched(prevColumn, prevIndex)
      markWordAsMatched(currentColumn, currentIndex)

      // 延迟等待动画完成，与CSS动画时间保持一致
      setTimeout(() => {
        // 重置选择状态和动画状态
        selectedColumn.value = null
        selectedIndex.value = null
        successIndices.value = []

        // 检查当前这轮10个单词对是否全部消除完成
        if (areAllColumnsEmpty()) {
          // 当前轮次完成，检查是否还有未匹配的单词
          const availableWords = wordPool.value.filter(word => !word.matched)
          
          if (availableWords.length > 0) {
            // 还有未匹配的单词，加载新的10个单词对
            setTimeout(() => {
              shuffleGameWords()
              // 重置动画状态
              isAnimating.value = false
            }, 300) // 稍微延迟一下，让用户看到清空效果
          } else {
            // 所有单词都匹配完成，游戏结束
            endGame(true)
            // 重置动画状态
            isAnimating.value = false
          }
        } else {
          // 当前轮次还没完成，继续当前轮次
          // 重置动画状态
          isAnimating.value = false
        }
      }, 800) // 与CSS动画时间保持一致（0.8s）
    }

    // 处理匹配失败
    const handleMatchError = (prevColumn, prevIndex, currentColumn, currentIndex) => {
      // 设置动画状态为true
      isAnimating.value = true

      // 播放失败音效
      playAudio('error')

      // 增加错误计数
      errorCount.value++

      // 添加错误动画类
      errorIndices.value = [`${prevIndex}-col${prevColumn}`, `${currentIndex}-col${currentColumn}`]

      // 延迟时间增加，让用户充分看到失败动画效果
      setTimeout(() => {
        // 重置选择状态
        selectedColumn.value = null
        selectedIndex.value = null
        errorIndices.value = []
        // 重置动画状态
        isAnimating.value = false
      }, 1000) // 增加延迟时间到1秒
    }

    // 保存游戏记录
    const saveGameRecord = async () => {
      // 如果记录已经保存过，则不再保存
      if (recordSaved.value) {
        console.log('游戏记录已保存，本次会话不再重复保存')
        return
      }

      if (!userId.value || wordPool.value.length === 0) {
        return
      }

      try {
        // 获取所有单词的章节ID，去重
        const periodIds = [...new Set(wordPool.value.map(word => word.periodId))].join(',')

        // 构建单词列表
        const itemList = wordPool.value.map(word => ({
          wordId: word.id,
          periodId: word.periodId
        }))

        // 构建请求数据
        const data = {
          uid: userId.value,
          bookId: props.bookId,
          periodIds: periodIds,
          type: 'game',
          itemList: itemList
        }

        // 调用API保存游戏记录
        await saveWordRecord(data)
        recordSaved.value = true
      } catch (error) {
        console.error('保存游戏记录错误:', error)
      }
    }

    // 开始游戏
    const startGame = () => {
      initGame()
      isPlaying.value = true
      isPaused.value = false

      // 播放开始音效
      playAudio('start')

      // 保存游戏记录
      saveGameRecord()

      // 启动游戏计时器
      gameTimer = setInterval(() => {
        if (remainingTime.value > 0) {
          remainingTime.value--
        } else {
          endGame(false)
        }
      }, 1000)
    }

    // 暂停游戏
    const pauseGame = () => {
      isPlaying.value = false
      isPaused.value = true

      if (gameTimer) {
        clearInterval(gameTimer)
        gameTimer = null
      }
    }

    // 继续游戏
    const resumeGame = () => {
      isPlaying.value = true
      isPaused.value = false

      // 重新启动游戏计时器
      gameTimer = setInterval(() => {
        if (remainingTime.value > 0) {
          remainingTime.value--
        } else {
          endGame(false)
        }
      }, 1000)
    }

    // 切换游戏状态（开始/暂停/继续）
    const togglePlayPause = () => {
      if (isPlaying.value) {
        pauseGame()
      } else if (isPaused.value) {
        resumeGame()
      } else {
        startGame()
      }
    }

    // 结束游戏
    const endGame = (completed) => {
      // 计算并记录实际游戏时长
      actualGameDuration.value = gameTime.value - remainingTime.value
      
      isPlaying.value = false
      isPaused.value = false
      gameOver.value = true

      // 播放结束音效
      playAudio('result')

      if (gameTimer) {
        clearInterval(gameTimer)
        gameTimer = null
      }

      // 如果游戏完成，显示庆祝效果
      if (completed) {
        setTimeout(() => {
          if (confettiContainer.value) {
            const myConfetti = confetti.create(confettiContainer.value, {
              resize: true,
              useWorker: true
            })

            myConfetti({
              particleCount: 100,
              spread: 70,
              origin: { y: 0.6 }
            })
          }
        }, 300)
      }
    }

    // 重新开始游戏
    const restartGame = () => {
      // 重置记录保存状态，允许新游戏保存记录
      recordSaved.value = false
      
      // 初始化游戏，不重置游戏时间（保持用户设置的时间）
      initGame(false)
      
      // 自动开始游戏
      startGame()
    }

    // 关闭对话框
    const handleClose = () => {
      if (isPlaying.value) {
        ElMessageBox.confirm(
          '游戏进行中，确定要退出吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          cleanupAndClose()
        }).catch(() => {
          // 取消关闭
        })
      } else {
        cleanupAndClose()
      }
    }

    // 清理并关闭
    const cleanupAndClose = () => {
      if (gameTimer) {
        clearInterval(gameTimer)
        gameTimer = null
      }

      isPlaying.value = false
      isPaused.value = false
      gameOver.value = false

      // 重置记录保存状态，以便下次打开弹窗时可以再次保存
      recordSaved.value = false

      // 移除事件监听器
      window.removeEventListener('popstate', handlePopState)

      dialogVisible.value = false
      emit('update:visible', false)
    }

    // 处理浏览器回退按钮
    const handlePopState = (event) => {
      if (dialogVisible.value) {
        handleClose()
        event.preventDefault()
        return false
      }
    }

    // 监听对话框可见性变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal

      if (newVal) {
        // 当对话框打开时，重置游戏状态并初始化，重置游戏时间到默认值
        isPlaying.value = false
        isPaused.value = false
        gameOver.value = false
        initGame(true)

        // 添加历史记录状态，用于处理浏览器回退
        window.history.pushState({ wordMatchingDialog: true }, '')

        // 添加popstate事件监听器
        window.addEventListener('popstate', handlePopState)
      } else {
        // 对话框关闭时，移除事件监听器
        window.removeEventListener('popstate', handlePopState)
      }
    })

    watch(() => dialogVisible.value, (newVal) => {
      emit('update:visible', newVal)
    })

    // 组件挂载时初始化
    onMounted(() => {
      if (dialogVisible.value) {
        initGame(true)
      }
    })

    // 组件卸载前清理
    onBeforeUnmount(() => {
      if (gameTimer) {
        clearInterval(gameTimer)
        gameTimer = null
      }

      window.removeEventListener('popstate', handlePopState)
    })

    return {
      dialogVisible,
      gameTime,
      remainingTime,
      isPlaying,
      isPaused,
      gameOver,
      matchedCount,
      errorCount,
      totalWords,
      actualGameDuration,
      column1Words,
      column2Words,
      column3Words,
      column4Words,
      selectedColumn,
      selectedIndex,
      errorIndices,
      successIndices,
      buttonText,
      progressPercentage,
      confettiContainer,
      formatTime,
      togglePlayPause,
      selectWord,
      handleClose,
      progressBarColor,
      getBubbleRandomStyle,
      validateGameTime,
      restartGame,
    }
  }
})
</script>

<style lang="scss">
.word-matching-dialog {
  .is-fullscreen {
    padding: 0;
    background: linear-gradient(135deg, #a5d8ff 0%, #0074d9 100%) !important;
  }
  .el-dialog__header {
    display: none;
  }
}
</style>
<style scoped lang="scss">
.word-matching-dialog {
  display: flex;
  flex-direction: column;
}

.word-matching-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  position: relative; /* 添加相对定位 */
  overflow: hidden; 
}

.ocean-animation-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  pointer-events: none; /* 确保动画层不干扰用户交互 */
  z-index: 0; /* 确保在背景层 */
}

/* 小鱼动画 */
.fish {
  position: absolute;
  width: 80px;
  height: 60px;
  background-size: contain;
  background-repeat: no-repeat;
  animation: swim linear infinite;
  z-index: 1; /* 在背景上，但在内容下方 */
}

/* 鱼头朝左的鱼（从右向左游） */
.fish-left-1 {
  background-image: url('@/assets/word/left-1.png');
  top: 15%;
  right: -100px;
  animation-duration: 15s;
  animation-delay: 0s;
  animation-name: swim-left;
}

.fish-left-2 {
  background-image: url('@/assets/word/left-2.png');
  top: 45%;
  right: -100px;
  animation-duration: 18s;
  animation-delay: 3s;
  animation-name: swim-left;
}

.fish-left-3 {
  background-image: url('@/assets/word/left-3.png');
  top: 65%;
  right: -100px;
  animation-duration: 16s;
  animation-delay: 6s;
  animation-name: swim-left;
}

.fish-left-4 {
  background-image: url('@/assets/word/left-4.png');
  top: 25%;
  right: -100px;
  animation-duration: 20s;
  animation-delay: 9s;
  animation-name: swim-left;
}

/* 鱼头朝右的鱼（从左向右游） */
.fish-right-1 {
  background-image: url('@/assets/word/right-1.png');
  top: 35%;
  left: -100px;
  animation-duration: 15s;
  animation-delay: 2s;
  animation-name: swim-right;
}

.fish-right-2 {
  background-image: url('@/assets/word/right-2.png');
  top: 55%;
  left: -100px;
  animation-duration: 17s;
  animation-delay: 5s;
  animation-name: swim-right;
}

.fish-right-3 {
  background-image: url('@/assets/word/right-3.png');
  top: 75%;
  left: -100px;
  animation-duration: 16s;
  animation-delay: 8s;
  animation-name: swim-right;
}

.fish-right-4 {
  background-image: url('@/assets/word/right-4.png');
  top: 10%;
  left: -100px;
  animation-duration: 19s;
  animation-delay: 11s;
  animation-name: swim-right;
}

/* 鱼头朝左游动动画（从右向左） */
@keyframes swim-left {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(-30vw) translateY(3vh) rotate(2deg);
  }
  50% {
    transform: translateX(-60vw) translateY(0vh) rotate(0deg);
  }
  75% {
    transform: translateX(-90vw) translateY(-3vh) rotate(-2deg);
  }
  100% {
    transform: translateX(-120vw) translateY(0vh) rotate(0deg);
  }
}

/* 鱼头朝右游动动画（从左向右） */
@keyframes swim-right {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(30vw) translateY(-3vh) rotate(-2deg);
  }
  50% {
    transform: translateX(60vw) translateY(0vh) rotate(0deg);
  }
  75% {
    transform: translateX(90vw) translateY(3vh) rotate(2deg);
  }
  100% {
    transform: translateX(120vw) translateY(0vh) rotate(0deg);
  }
}

/* 游戏区域 - 优化基础样式以更好适配1700-1920px重点区间 */
.game-area {
  display: flex;
  justify-content: center;
  flex: 1;
  gap: clamp(1.8rem, 2.5vw, 2.8rem);
  padding: 0rem 1rem 1rem;
  overflow: hidden;
  position: relative; /* 确保在动画层之上 */
  z-index: 10; /* 确保在动画层之上 */
  /* 确保游戏区域有固定高度，避免布局跳动，适应10组单词 */
  min-height: clamp(600px, 70vh, 800px);
  max-height: 80vh; /* 进一步提高最大高度限制 */
  align-items: flex-start;
  /* 优化宽度以更好适配重点屏幕尺寸 */
  width: min(1800px, 90vw);
  margin: 0 auto 1rem; /* 添加底部间距 */
}

/* 修改游戏区域样式，使其半透明以显示背景 */
.word-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: clamp(1.3rem, 2.1vh, 2.1rem);
  padding: 1.3rem;
  max-height: 100%;
  align-items: center;
  /* 添加最小高度，防止布局跳动，适应5个单词 */
  min-height: clamp(520px, 66vh, 700px);
  /* 确保容器有足够空间容纳动画 */
  overflow: visible;
  /* 优化列宽以更好适配重点屏幕尺寸 */
  max-width: clamp(270px, 25vw, 350px);
  min-width: clamp(250px, 23vw, 310px);
}

.word-card {
  background: linear-gradient(135deg, 
    #ffab40 0%, 
    #ff9800 25%, 
    #ff8f00 60%, 
    #ff6f00 100%);
  border-radius: clamp(1.3rem, 2.9vw, 2.1rem);
  padding: clamp(1.1rem, 1.9vh, 1.6rem) clamp(1.3rem, 2.3vw, 1.9rem);
  box-shadow:
    0 8px 25px rgba(255, 111, 0, 0.4),
    0 4px 15px rgba(255, 152, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: clamp(70px, 7.5vh, 90px);
  width: 100%;
  max-width: 100%;
  border: none;
  position: relative;
  overflow: hidden;
  /* 添加will-change优化动画性能 */
  will-change: transform, opacity;
  /* 确保卡片有固定的空间占用 */
  flex-shrink: 0;

  /* 添加顶部高光效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 45%;
    background: linear-gradient(180deg, 
      rgba(255, 255, 255, 0.5) 0%, 
      rgba(255, 255, 255, 0.25) 50%, 
      transparent 100%);
    border-radius: clamp(1rem, 2.5vw, 1.8rem) clamp(1rem, 2.5vw, 1.8rem) 0 0;
    pointer-events: none;
  }

  /* 添加底部阴影效果 */
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 35%;
    background: linear-gradient(180deg, 
      transparent 0%, 
      rgba(0, 0, 0, 0.2) 100%);
    border-radius: 0 0 clamp(1rem, 2.5vw, 1.8rem) clamp(1rem, 2.5vw, 1.8rem);
    pointer-events: none;
  }

  /* 第一种颜色：浅紫色渐变 */
  &.color-1 {
    background: linear-gradient(135deg, 
      #e1bee7 0%, 
      #ce93d8 25%, 
      #ba68c8 60%, 
      #ab47bc 100%);
    box-shadow: 
      0 8px 25px rgba(171, 71, 188, 0.4),
      0 4px 15px rgba(186, 104, 200, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.4),
      inset 0 -2px 0 rgba(0, 0, 0, 0.15);
  }

  /* 第二种颜色：浅绿色渐变 */
  &.color-2 {
    background: linear-gradient(135deg, 
      #80cbc4 0%, 
      #4db6ac 25%, 
      #26a69a 60%, 
      #00897b 100%);
    box-shadow: 
      0 8px 25px rgba(0, 137, 123, 0.4),
      0 4px 15px rgba(38, 166, 154, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.4),
      inset 0 -2px 0 rgba(0, 0, 0, 0.15);
  }

  /* 第三种颜色：浅橙色渐变 */
  &.color-3 {
    background: linear-gradient(135deg, 
      #ffcc80 0%, 
      #ffb74d 25%, 
      #ffa726 60%, 
      #ff9800 100%);
    box-shadow: 
      0 8px 25px rgba(255, 152, 0, 0.4),
      0 4px 15px rgba(255, 167, 38, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.4),
      inset 0 -2px 0 rgba(0, 0, 0, 0.15);
  }

  /* 第四种颜色：浅蓝色渐变 */
  &.color-4 {
    background: linear-gradient(135deg, 
      #90caf9 0%, 
      #64b5f6 25%, 
      #42a5f5 60%, 
      #2196f3 100%);
    box-shadow: 
      0 8px 25px rgba(33, 150, 243, 0.4),
      0 4px 15px rgba(66, 165, 245, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.4),
      inset 0 -2px 0 rgba(0, 0, 0, 0.15);
  }

  .word-text {
    font-size: clamp(1.3rem, 2.3vw, 1.7rem);
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    letter-spacing: 0.9px;
    position: relative;
    z-index: 1;
    line-height: 1.2;
    word-break: break-word;
    hyphens: auto;
  }

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 12px 35px rgba(255, 111, 0, 0.5),
      0 6px 20px rgba(255, 152, 0, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.5),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg,
      #ffb74d 0%,
      #ffa726 25%,
      #ff9800 60%,
      #ff8f00 100%);

    .word-text {
      text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
      font-size: clamp(1.35rem, 2.4vw, 1.75rem);
    }
  }

  /* 不同颜色的hover效果 */
  &.color-1:hover {
    background: linear-gradient(135deg, 
      #f3e5f5 0%, 
      #e1bee7 25%, 
      #ce93d8 60%, 
      #ba68c8 100%);
    box-shadow: 
      0 12px 35px rgba(186, 104, 200, 0.5),
      0 6px 20px rgba(206, 147, 216, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.5),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2);
  }

  &.color-2:hover {
    background: linear-gradient(135deg, 
      #b2dfdb 0%, 
      #80cbc4 25%, 
      #4db6ac 60%, 
      #26a69a 100%);
    box-shadow: 
      0 12px 35px rgba(38, 166, 154, 0.5),
      0 6px 20px rgba(77, 182, 172, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.5),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2);
  }

  &.color-3:hover {
    background: linear-gradient(135deg, 
      #ffe0b2 0%, 
      #ffcc80 25%, 
      #ffb74d 60%, 
      #ffa726 100%);
    box-shadow: 
      0 12px 35px rgba(255, 167, 38, 0.5),
      0 6px 20px rgba(255, 183, 77, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.5),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2);
  }

  &.color-4:hover {
    background: linear-gradient(135deg, 
      #e3f2fd 0%, 
      #90caf9 25%, 
      #64b5f6 60%, 
      #42a5f5 100%);
    box-shadow: 
      0 12px 35px rgba(66, 165, 245, 0.5),
      0 6px 20px rgba(100, 181, 246, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.5),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 
      0 6px 20px rgba(255, 111, 0, 0.4),
      0 3px 12px rgba(255, 152, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.25);
  }

  &.selected {
    /* 不改变背景色，只通过边框和阴影突出显示 */
    transform: translateY(-8px) scale(1.08);
    box-shadow: 
      inset 0 2px 0 rgba(255, 255, 255, 0.5), /* 保持内部高光 */
      inset 0 -2px 0 rgba(0, 0, 0, 0.2),    /* 保持内部阴影 */
      0 0 0 3px #ffffff,                     /* 3px 白色实心边框 */
      0 0 0 7px #ffd700;                     /* 4px 金色实心边框 (3+4) */

    .word-text {
      font-size: clamp(1.4rem, 2.5vw, 1.8rem);
      color: #ffffff;
      text-shadow: 0 3px 8px rgba(0, 0, 0, 0.5);
      font-weight: 900;
    }

    /* 选中时的脉冲动画 */
    animation: pulse-selected 1.5s infinite ease-in-out;
  }

  &.matched {
    opacity: 0;
    pointer-events: none;
    /* 保持空间占用，不改变transform，避免与成功动画冲突 */
    /* transform: scale(0); */
    transition: opacity 0.3s ease;
    /* 确保元素仍然占据空间，保持布局稳定 */
    visibility: hidden;
  }
}

@keyframes pulse-selected {
  0%, 100% {
    transform: translateY(-8px) scale(1.08);
    box-shadow: 
      inset 0 2px 0 rgba(255, 255, 255, 0.5),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2),
      0 0 0 3px #ffffff,
      0 0 0 7px #ffd700,
      0 0 20px 10px rgba(255, 215, 0, 0.6); /* 金色辉光 */
  }
  50% {
    transform: translateY(-10px) scale(1.12);
    box-shadow: 
      inset 0 2px 0 rgba(255, 255, 255, 0.5),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2),
      0 0 0 3px #ffffff,
      0 0 0 7px #ffd700,
      0 0 35px 20px rgba(255, 215, 0, 0.8); /* 更强的金色辉光 */
  }
}

/* 错误动画 */
.error-animation {
  animation: shake-error 1s cubic-bezier(.36,.07,.19,.97) both;
  background: linear-gradient(135deg, 
    #f44336 0%, 
    #e53935 30%, 
    #d32f2f 70%, 
    #b71c1c 100%) !important;
  box-shadow: 
    0 12px 35px rgba(183, 28, 28, 0.5),
    0 6px 20px rgba(211, 47, 47, 0.4),
    inset 0 3px 0 rgba(255, 255, 255, 0.3),
    inset 0 -3px 0 rgba(0, 0, 0, 0.25) !important;
  
  .word-text {
    color: #ffffff !important;
    text-shadow: 0 3px 8px rgba(0, 0, 0, 0.5) !important;
    font-weight: 900 !important;
  }
}

@keyframes shake-error {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(1);
  }
  5%, 15%, 25%, 35%, 45%, 55%, 65%, 75%, 85%, 95% {
    transform: translate3d(-8px, 0, 0) scale(1.02) rotate(-1deg);
  }
  10%, 20%, 30%, 40%, 50%, 60%, 70%, 80%, 90% {
    transform: translate3d(8px, 0, 0) scale(1.02) rotate(1deg);
  }
}

/* 成功动画 */
.success-animation {
  animation: success-bounce-out 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
  background: linear-gradient(135deg, 
    #4caf50 0%, 
    #43a047 25%, 
    #388e3c 60%, 
    #2e7d32 100%) !important;
  box-shadow: 
    0 15px 40px rgba(46, 125, 50, 0.6),
    0 8px 25px rgba(56, 142, 60, 0.5),
    inset 0 4px 0 rgba(255, 255, 255, 0.4),
    inset 0 -4px 0 rgba(0, 0, 0, 0.2),
    0 0 0 4px rgba(76, 175, 80, 0.3) !important;
  
  .word-text {
    color: #ffffff !important;
    text-shadow: 0 3px 8px rgba(0, 0, 0, 0.4) !important;
    font-weight: 900 !important;
  }
}

@keyframes success-bounce-out {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  15% {
    transform: scale(1.1) translateY(-8px);
    opacity: 1;
  }
  30% {
    transform: scale(0.95) translateY(3px);
    opacity: 1;
  }
  45% {
    transform: scale(1.05) translateY(-3px);
    opacity: 0.9;
  }
  60% {
    transform: scale(0.98) translateY(1px);
    opacity: 0.7;
  }
  75% {
    transform: scale(1.02) translateY(-1px);
    opacity: 0.4;
  }
  90% {
    transform: scale(0.1) translateY(0px);
    opacity: 0.1;
  }
  100% {
    transform: scale(0) translateY(0px);
    opacity: 0;
  }
}

/* 卡片动画优化 */
.word-card-enter-active {
  animation: bounce-in 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保动画期间保持空间占用 */
  transition-property: opacity, transform;
  /* 使用GPU加速 */
  will-change: transform, opacity;
}

.word-card-leave-active {
  animation: bounce-out 0.5s cubic-bezier(0.4, 0, 1, 1);
  /* 离开时保持在文档流中，避免其他元素突然移动 */
  position: relative;
  transition-property: opacity, transform;
  /* 使用GPU加速 */
  will-change: transform, opacity;
}

@keyframes bounce-in {
  0% {
    transform: scale(0) translateY(20px);
  opacity: 0;
  }
  50% {
    transform: scale(1.05) translateY(-5px);
    opacity: 0.7;
  }
  75% {
    transform: scale(0.95) translateY(2px);
    opacity: 0.9;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes bounce-out {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  25% {
    transform: scale(1.1) translateY(-10px);
    opacity: 0.9;
  }
  50% {
    transform: scale(0.9) translateY(5px);
    opacity: 0.6;
  }
  75% {
    transform: scale(0.95) translateY(-2px);
    opacity: 0.3;
  }
  100% {
    transform: scale(0) translateY(20px);
  opacity: 0;
  }
}

/* 游戏结束界面 */
.game-over-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70vh;
  padding: 2rem;
  animation: fadeIn 1s ease;
  position: relative; /* 确保在动画层之上 */
  z-index: 10; /* 确保在动画层之上 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.game-over-content {
  background-color: rgba(255, 255, 255, 0.7); /* 半透明背景 */
  backdrop-filter: blur(8px); /* 更强的模糊效果 */
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.2);
  text-align: center;
  width: 100%;
  max-width: 900px;
  position: relative;
  animation: scaleIn 0.5s ease;
  border: 2px solid rgba(255, 255, 255, 0.8); /* 添加白色边框 */
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
  }
  to {
    transform: scale(1);
  }
}

.game-over-title {
  font-size: 4rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #1E90FF, #00BFFF); /* 更适合海洋主题的渐变色 */
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

  .game-over-stats {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    margin-bottom: 3rem;
    justify-items: center;
    
    /* 小屏幕适配：2行3列+2列布局 */
    @media (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 1.5rem;
    }
    
    /* 中等屏幕适配：保持5列但调整间距 */
    @media (min-width: 769px) and (max-width: 1200px) {
      gap: 0.8rem;
    }
  }

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 1.6rem;
  color: #666;
  margin-bottom: 0.8rem;
  font-weight: 600;
}

.stat-value {
  font-size: 3.5rem;
  font-weight: 700;
  color: #333;
}

.success-value {
  color: #67c23a;
}

.unmatch-value {
  color: #e6a23c;
}

.error-value {
  color: #f56c6c;
}

/* 再来一次按钮样式 */
.restart-button-container {
  display: flex;
  justify-content: center;
  align-items: center;

  .custom-exit-button,
  .custom-restart-button {
    width: 220px;
    height: 70px;
    border-radius: 35px;
  }

  .exit-text,
  .restart-text {
    font-size: 1.8rem;
  }
}

/* 自定义重新开始按钮样式 */
.custom-restart-button {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  border: none;
  border-radius: 2.5rem;
  padding: 0;
  width: 180px;
  height: 55px;
  cursor: pointer;
  box-shadow: 
    0 8px 25px rgba(64, 158, 255, 0.3),
    0 4px 15px rgba(102, 177, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, 
      rgba(255, 255, 255, 0.3) 0%, 
      rgba(255, 255, 255, 0.1) 50%, 
      transparent 100%);
    border-radius: 2.5rem 2.5rem 0 0;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 
      0 12px 35px rgba(64, 158, 255, 0.4),
      0 6px 20px rgba(102, 177, 255, 0.3);
    background: linear-gradient(135deg, #66b1ff 0%, #82c5ff 100%);

    .restart-icon {
      animation: rotate-continuous 1s linear infinite;
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 
      0 6px 20px rgba(64, 158, 255, 0.3),
      0 3px 12px rgba(102, 177, 255, 0.2);
  }
}

.restart-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  height: 100%;
  width: 100%;
}

.restart-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.restart-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

@keyframes rotate-continuous {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.confetti-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 自定义对话框头部样式 */
.custom-dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding-top: 0.5rem;
}

.dialog-title {
  font-size: 2.8rem;
  font-weight: 800;
  color: #2d3748;
  text-align: center;
  line-height: 1.2;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #2d3748 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 8px rgba(45, 55, 72, 0.3);
  letter-spacing: 2px;
  position: relative;
  
  /* 添加装饰性下划线 */
  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 3px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      #4a5568 20%, 
      #2d3748 50%, 
      #4a5568 80%, 
      transparent 100%);
    border-radius: 2px;
  }
}



:deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

/* 响应式设计 - 专为大屏幕优化 */

/* 中等大屏幕适配 (1500px - 1900px) - 重点优化区间 */
@media (min-width: 1500px) and (max-width: 1900px) {
  .game-area {
    gap: clamp(1.5rem, 2.5vw, 2.5rem);
    width: min(1700px, 92vw);
    padding: 0rem 1rem 1rem;
    min-height: clamp(600px, 68vh, 750px);
    max-height: 78vh;
  }

  .word-column {
    max-width: clamp(240px, 24vw, 320px);
    min-width: clamp(220px, 22vw, 280px);
    gap: clamp(1.2rem, 2vh, 2rem);
    min-height: clamp(500px, 65vh, 650px);
    padding: 1.2rem;
  }

  .word-card {
    min-height: clamp(65px, 7vh, 85px);
    padding: clamp(1rem, 1.8vh, 1.5rem) clamp(1.2rem, 2.2vw, 1.8rem);
    border-radius: clamp(1.2rem, 2.8vw, 2rem);

    .word-text {
      font-size: clamp(1.2rem, 2.2vw, 1.6rem);
      font-weight: 800;
      letter-spacing: 0.8px;
    }

    &:hover .word-text {
      font-size: clamp(1.25rem, 2.3vw, 1.65rem);
    }

    &.selected .word-text {
      font-size: clamp(1.3rem, 2.4vw, 1.7rem);
    }
  }

  /* 优化设置区域在中等屏幕的显示 */
  .settings-section {
    padding: 0rem 2.5rem 0;
    margin: 15px;
  }

  .setting-label {
    font-size: 1.3rem;
  }

  .time-text {
    font-size: 1.6rem;
  }

  .time-icon {
    font-size: 1.6rem;
  }

  /* 控制按钮优化 */
  .custom-control-button {
    width: 160px;
    height: 60px;

    .button-text {
      font-size: 1.6rem;
    }
  }

  .custom-exit-button {
    width: 160px;
    height: 60px;

    .exit-text {
      font-size: 1.6rem;
    }
  }

  .custom-restart-button {
    width: 160px;
    height: 50px;

    .restart-text {
      font-size: 1.4rem;
    }
  }
}

/* 较小大屏幕适配 (1400px - 1499px) */
@media (min-width: 1400px) and (max-width: 1499px) {
  .game-area {
    gap: clamp(1.2rem, 2vw, 2rem);
    width: min(1500px, 94vw);
    padding: 0rem 1rem 1rem;
    min-height: clamp(550px, 66vh, 700px);
    max-height: 76vh;
  }

  .word-column {
    max-width: clamp(220px, 23vw, 300px);
    min-width: clamp(200px, 21vw, 260px);
    gap: clamp(1rem, 1.8vh, 1.8rem);
    min-height: clamp(450px, 62vh, 600px);
    padding: 1.1rem;
  }

  .word-card {
    min-height: clamp(60px, 6.5vh, 80px);
    padding: clamp(0.9rem, 1.6vh, 1.3rem) clamp(1.1rem, 2vw, 1.6rem);
    border-radius: clamp(1.1rem, 2.6vw, 1.9rem);

    .word-text {
      font-size: clamp(1.1rem, 2.1vw, 1.5rem);
      font-weight: 800;
      letter-spacing: 0.6px;
    }

    &:hover .word-text {
      font-size: clamp(1.15rem, 2.2vw, 1.55rem);
    }

    &.selected .word-text {
      font-size: clamp(1.2rem, 2.3vw, 1.6rem);
    }
  }

  .custom-control-button {
    width: 160px;
    height: 60px;

    .button-text {
      font-size: 1.6rem;
    }
  }

  .custom-exit-button {
    width: 160px;
    height: 60px;

    .exit-text {
      font-size: 1.6rem;
    }
  }

  .custom-restart-button {
    width: 150px;
    height: 48px;

    .restart-text {
      font-size: 1.3rem;
    }
  }
}

/* 修改设置区域样式 */
.settings-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0rem 2rem 0;
  position: relative; /* 确保在动画层之上 */
  z-index: 10; /* 确保在动画层之上 */
  margin: 10px;
}

/* 控制按钮区域样式 */
.control-section {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 100;
}

.control-button-container {
  display: flex;
  flex-direction: row;
  gap: 5rem;
  align-items: center;
  justify-content: flex-end;
}

/* 自定义控制按钮样式 */
.custom-control-button {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  border-radius: 2.5rem;
  padding: 0;
  width: 160px;
  height: 60px;
  cursor: pointer;
  box-shadow: 
    0 8px 25px rgba(103, 194, 58, 0.3),
    0 4px 15px rgba(133, 206, 97, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, 
      rgba(255, 255, 255, 0.3) 0%, 
      rgba(255, 255, 255, 0.1) 50%, 
      transparent 100%);
    border-radius: 2.5rem 2.5rem 0 0;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 
      0 12px 35px rgba(103, 194, 58, 0.4),
      0 6px 20px rgba(133, 206, 97, 0.3);
    background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
  }

  &:active {
    transform: translateY(-1px) scale(1);
    box-shadow: 
      0 6px 20px rgba(103, 194, 58, 0.3),
      0 3px 12px rgba(133, 206, 97, 0.2);
  }

  &.pause-button {
    background: linear-gradient(135deg, #e6a23c 0%, #f0b90b 100%);
    box-shadow: 
      0 8px 25px rgba(230, 162, 60, 0.3),
      0 4px 15px rgba(240, 185, 11, 0.2);

    &:hover {
      background: linear-gradient(135deg, #f0b90b 0%, #f7d794 100%);
      box-shadow: 
        0 12px 35px rgba(230, 162, 60, 0.4),
        0 6px 20px rgba(240, 185, 11, 0.3);
    }
  }

  &.resume-button {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    box-shadow: 
      0 8px 25px rgba(64, 158, 255, 0.3),
      0 4px 15px rgba(102, 177, 255, 0.2);

    &:hover {
      background: linear-gradient(135deg, #66b1ff 0%, #82c5ff 100%);
      box-shadow: 
        0 12px 35px rgba(64, 158, 255, 0.4),
        0 6px 20px rgba(102, 177, 255, 0.3);
    }
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  height: 100%;
  width: 100%;
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.button-text {
  font-size: 1.6rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

/* 自定义退出按钮样式 */
.custom-exit-button {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  border: none;
  border-radius: 2rem;
  padding: 0;
  width: 160px;
  height: 60px;
  cursor: pointer;
  box-shadow: 
    0 6px 20px rgba(245, 108, 108, 0.3),
    0 3px 12px rgba(247, 137, 137, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, 
      rgba(255, 255, 255, 0.3) 0%, 
      rgba(255, 255, 255, 0.1) 50%, 
      transparent 100%);
    border-radius: 2rem 2rem 0 0;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 
      0 8px 25px rgba(245, 108, 108, 0.4),
      0 4px 15px rgba(247, 137, 137, 0.3);
    background: linear-gradient(135deg, #f78989 0%, #f9a3a3 100%);
  }

  &:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 
      0 4px 15px rgba(245, 108, 108, 0.3),
      0 2px 8px rgba(247, 137, 137, 0.2);
  }

  &.game-over-exit {
    margin-right: 5rem;
  }
}

.exit-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  height: 100%;
  width: 100%;
}

.exit-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.exit-text {
  font-size: 1.6rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

.settings-group-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.setting-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.time-input-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 248, 255, 0.9) 100%);
  border-radius: 1.5rem;
  padding: 0.3rem 1rem 0.3rem 0.3rem;
  box-shadow: 
    0 4px 15px rgba(0, 123, 255, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    inset 0 -2px 0 rgba(0, 0, 0, 0.05);
  border: 2px solid rgba(64, 169, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 
      0 6px 20px rgba(0, 123, 255, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.9),
      inset 0 -2px 0 rgba(0, 0, 0, 0.08);
    border-color: rgba(64, 169, 255, 0.5);
    transform: translateY(-1px);
  }
}

.time-unit {
  font-size: 1.1rem;
  font-weight: 600;
  color: #409eff;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

/* 自定义时间输入框样式 */
:deep(.time-input) {
  .el-input__wrapper {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 1.2rem;
    padding: 0.5rem 1rem;
    width: 80px;
    
    .el-input__inner {
      width: 80px !important;
      color: #2d3748 !important;
      font-weight: 700 !important;
      font-size: 1.2rem !important;
      text-align: center !important;
      background: transparent !important;
      border: none !important;
      
      &::placeholder {
        color: rgba(45, 55, 72, 0.5) !important;
        font-weight: 500 !important;
      }
      
      &:focus {
        color: #409eff !important;
        font-weight: 800 !important;
      }
    }
  }
  
  &.is-disabled {
    .el-input__wrapper {
      background: rgba(0, 0, 0, 0.05) !important;
      
      .el-input__inner {
        color: rgba(45, 55, 72, 0.6) !important;
      }
    }
  }
}

.settings-group-center {
  display: flex;
  align-items: center;
  flex-grow: 1;
  margin: 0 2rem;
}

.progress-counter {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}



/* 海草装饰样式 */
.seaweed {
  position: absolute;
  bottom: 0;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom;
  z-index: 1;
  animation: sway linear infinite;
}

/* 海草1（cao-1.png）- 左侧 */
.seaweed-1-left {
  background-image: url('@/assets/word/cao-1.png');
  left: 3%;
  width: 100px;
  height: 150px;
  animation-duration: 4.5s;
  animation-delay: 0s;
}

/* 海草1（cao-1.png）- 右侧 */
.seaweed-1-right {
  background-image: url('@/assets/word/cao-1.png');
  right: 5%;
  width: 105px;
  height: 160px;
  animation-duration: 4.8s;
  animation-delay: 2s;
}

/* 海草2（cao-2.png）- 左中 */
.seaweed-2-left {
  background-image: url('@/assets/word/cao-2.png');
  left: 18%;
  width: 90px;
  height: 140px;
  animation-duration: 4.2s;
  animation-delay: 1.5s;
}

/* 海草2（cao-2.png）- 右中 */
.seaweed-2-right {
  background-image: url('@/assets/word/cao-2.png');
  right: 22%;
  width: 95px;
  height: 145px;
  animation-duration: 4.6s;
  animation-delay: 0.8s;
}

/* 海草1（cao-1.png）- 中间左 */
.seaweed-1-center {
  background-image: url('@/assets/word/cao-1.png');
  left: 42%;
  width: 98px;
  height: 155px;
  animation-duration: 4.3s;
  animation-delay: 2.8s;
}

/* 海草2（cao-2.png）- 中间右 */
.seaweed-2-center {
  background-image: url('@/assets/word/cao-2.png');
  left: 68%;
  width: 92px;
  height: 148px;
  animation-duration: 4.1s;
  animation-delay: 1.2s;
}

/* 海草摆动动画 */
@keyframes sway {
  0%, 100% {
    transform: rotate(0deg);
    transform-origin: bottom center;
  }
  25% {
    transform: rotate(2.5deg);
    transform-origin: bottom center;
  }
  50% {
    transform: rotate(0deg);
    transform-origin: bottom center;
  }
  75% {
    transform: rotate(-2.5deg);
    transform-origin: bottom center;
  }
}

/* 珊瑚装饰样式 */
.coral {
  position: absolute;
  bottom: 0;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom;
  z-index: 2; /* 比海草层级稍高 */
  animation: coral-sway linear infinite;
}

/* 珊瑚1（shanhu-1.png）- 左侧 */
.coral-1-left {
  background-image: url('@/assets/word/shanhu-1.png');
  left: 12%;
  width: 75px;
  height: 105px;
  animation-duration: 5.5s;
  animation-delay: 0.5s;
}

/* 珊瑚1（shanhu-1.png）- 右侧 */
.coral-1-right {
  background-image: url('@/assets/word/shanhu-1.png');
  right: 15%;
  width: 80px;
  height: 110px;
  animation-duration: 5.8s;
  animation-delay: 2.3s;
}

/* 珊瑚1（shanhu-1.png）- 中间 */
.coral-1-center {
  background-image: url('@/assets/word/shanhu-1.png');
  left: 58%;
  width: 85px;
  height: 115px;
  animation-duration: 5.2s;
  animation-delay: 1.8s;
}

/* 珊瑚摆动动画（比海草更轻微） */
@keyframes coral-sway {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    transform-origin: bottom center;
  }
  25% {
    transform: rotate(1.5deg) scale(1.03);
    transform-origin: bottom center;
  }
  50% {
    transform: rotate(0deg) scale(1);
    transform-origin: bottom center;
  }
  75% {
    transform: rotate(-1.5deg) scale(1.03);
    transform-origin: bottom center;
  }
}

.time-counter {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 248, 255, 0.9) 100%);
  border-radius: 1.5rem;
  padding: 0.8rem 1.2rem;
  box-shadow: 
    0 4px 15px rgba(0, 123, 255, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    inset 0 -2px 0 rgba(0, 0, 0, 0.05);
  border: 2px solid rgba(64, 169, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 
      0 6px 20px rgba(0, 123, 255, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.9),
      inset 0 -2px 0 rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }
}

.time-icon {
  font-size: 1.5rem;
  color: #409eff;
  margin-right: 0.6rem;
  filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.8));
}

.time-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 重点优化区间 (1700px - 1920px) - 主要使用屏幕尺寸 */
@media (min-width: 1700px) and (max-width: 1920px) {
  .game-area {
    gap: clamp(2rem, 2.8vw, 3rem);
    width: min(1800px, 90vw);
    padding: 0rem 1rem 1rem;
    min-height: clamp(650px, 70vh, 800px);
    max-height: 80vh;
  }

  .word-column {
    max-width: clamp(280px, 26vw, 360px);
    min-width: clamp(260px, 24vw, 320px);
    gap: clamp(1.4rem, 2.2vh, 2.2rem);
    min-height: clamp(550px, 67vh, 700px);
    padding: 1.4rem;
  }

  .word-card {
    min-height: clamp(75px, 8vh, 95px);
    padding: clamp(1.2rem, 2vh, 1.6rem) clamp(1.4rem, 2.4vw, 2rem);
    border-radius: clamp(1.4rem, 3vw, 2.2rem);

    .word-text {
      font-size: clamp(1.4rem, 2.4vw, 1.8rem);
      font-weight: 800;
      letter-spacing: 1px;
      line-height: 1.2;
    }

    &:hover .word-text {
      font-size: clamp(1.45rem, 2.5vw, 1.85rem);
    }

    &.selected .word-text {
      font-size: clamp(1.5rem, 2.6vw, 1.9rem);
    }
  }

  /* 优化设置区域在重点屏幕的显示 */
  .settings-section {
    padding: 0rem 3rem 0;
    margin: 18px;
  }

  .setting-label {
    font-size: 1.4rem;
  }

  .time-text {
    font-size: 1.7rem;
  }

  .time-icon {
    font-size: 1.7rem;
  }

  /* 控制按钮优化 */
  .custom-control-button {
    width: 160px;
    height: 60px;

    .button-text {
      font-size: 1.6rem;
    }
  }

  .custom-exit-button {
    width: 160px;
    height: 60px;

    .exit-text {
      font-size: 1.6rem;
    }
  }

  .custom-restart-button {
    width: 170px;
    height: 52px;

    .restart-text {
      font-size: 1.45rem;
    }
  }
}

/* 大屏幕适配 (1920px及以上) - 保持最佳效果 */
@media (min-width: 1920px) {
  .game-area {
    width: min(1850px, 88vw);
    gap: clamp(2.2rem, 3.2vw, 3.5rem);
    min-height: clamp(700px, 72vh, 900px);
    max-height: 80vh;
    padding: 0 1rem 1rem;
  }

  .word-column {
    max-width: clamp(300px, 26vw, 380px);
    min-width: clamp(260px, 24vw, 320px);
    gap: clamp(1.4rem, 2.4vh, 2.4rem);
    min-height: clamp(600px, 68vh, 800px);
    padding: 1.4rem;
  }

  .word-card {
    min-height: clamp(75px, 8.5vh, 95px);
    padding: clamp(1.2rem, 2vh, 1.6rem) clamp(1.4rem, 2.4vw, 2rem);
    border-radius: clamp(1.4rem, 3vw, 2.2rem);

    .word-text {
      font-size: clamp(1.4rem, 2.4vw, 1.7rem);
      font-weight: 800;
      letter-spacing: 1px;
    }

    &:hover .word-text {
      font-size: clamp(1.45rem, 2.5vw, 1.75rem);
    }

    &.selected .word-text {
      font-size: clamp(1.5rem, 2.6vw, 1.8rem);
    }
  }

  /* 优化大屏幕设置区域 */
  .settings-section {
    padding: 0rem 3rem 0;
    margin: 20px;
  }

  .setting-label {
    font-size: 1.4rem;
  }

  .time-text {
    font-size: 1.7rem;
  }

  .time-icon {
    font-size: 1.7rem;
  }

  /* 大屏幕控制按钮 */
  .custom-control-button {
    width: 160px;
    height: 60px;

    .button-text {
      font-size: 1.6rem;
    }
  }

  .custom-exit-button {
    width: 160px;
    height: 60px;

    .exit-text {
      font-size: 1.6rem;
    }
  }

  .custom-restart-button {
    width: 180px;
    height: 55px;

    .restart-text {
      font-size: 1.5rem;
    }
  }
}

/* 移动端和小屏幕适配 */
@media (max-width: 768px) {
  .control-section {
    bottom: 0.5rem;
    right: 0.5rem;
  }

  .custom-control-button {
    width: 130px;
    height: 45px;

    .button-text {
      font-size: 1.2rem;
    }
  }

  .custom-exit-button {
    width: 130px;
    height: 45px;

    .exit-text {
      font-size: 1.2rem;
    }
  }

  .custom-restart-button {
    width: 120px;
    height: 40px;

    .restart-text {
      font-size: 1.1rem;
    }
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1399px) {
  .control-section {
    bottom: 0.5rem;
    right: 0.5rem;
  }

  .custom-control-button {
    width: 140px;
    height: 50px;

    .button-text {
      font-size: 1.3rem;
    }
  }

  .custom-exit-button {
    width: 140px;
    height: 50px;

    .exit-text {
      font-size: 1.3rem;
    }
  }

  .custom-restart-button {
    width: 140px;
    height: 45px;

    .restart-text {
      font-size: 1.2rem;
    }
  }
}

/* 超大屏幕适配 (2560px及以上) - 4K显示器优化 */
@media (min-width: 2560px) {
  .game-area {
    width: min(2200px, 82vw);
    gap: clamp(3rem, 4vw, 4.5rem);
    min-height: clamp(800px, 65vh, 1000px);
    max-height: 75vh;
    padding: 0rem 1.5rem 1rem;
  }

  .word-column {
    max-width: clamp(380px, 30vw, 450px);
    min-width: clamp(320px, 28vw, 400px);
    gap: clamp(1.8rem, 3vh, 3rem);
    min-height: clamp(700px, 62vh, 900px);
    padding: 1.6rem;
  }

  .word-card {
    min-height: clamp(90px, 10vh, 120px);
    padding: clamp(1.4rem, 2.2vh, 1.8rem) clamp(1.6rem, 2.6vw, 2.2rem);
    border-radius: clamp(1.6rem, 3.2vw, 2.4rem);

    .word-text {
      font-size: clamp(1.6rem, 2.6vw, 2rem);
      font-weight: 800;
      letter-spacing: 1.2px;
    }

    &:hover .word-text {
      font-size: clamp(1.65rem, 2.7vw, 2.05rem);
    }

    &.selected .word-text {
      font-size: clamp(1.7rem, 2.8vw, 2.1rem);
    }
  }

  /* 超大屏幕设置区域 */
  .settings-section {
    padding: 0rem 4rem 0;
    margin: 25px;
  }

  .setting-label {
    font-size: 1.6rem;
  }

  .time-text {
    font-size: 1.9rem;
  }

  .time-icon {
    font-size: 1.9rem;
  }

  /* 超大屏幕控制按钮 */
  .custom-control-button {
    width: 180px;
    height: 70px;

    .button-text {
      font-size: 1.8rem;
    }
  }

  .custom-exit-button {
    width: 180px;
    height: 70px;

    .exit-text {
      font-size: 1.8rem;
    }
  }

  .custom-restart-button {
    width: 200px;
    height: 60px;

    .restart-text {
      font-size: 1.7rem;
    }
  }
}
</style>
<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="60%"
    @update:model-value="$emit('update:visible', $event)"
    append-to-body
    class="agreement-dialog"
    :close-on-click-modal="false"
  >
    <div class="agreement-content">
      <component :is="agreementComponent" />
    </div>
    <template #footer>
      <el-button class="dialog-exit-button" @click="$emit('update:visible', false)">
        关闭
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, defineAsyncComponent } from 'vue'
import { ElDialog, ElButton } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agreementType: {
    type: String, // 'user' or 'privacy'
    required: true
  }
})

defineEmits(['update:visible'])

const dialogTitle = computed(() => {
  return props.agreementType === 'user' ? '用户服务协议' : '隐私政策'
})

const agreementComponent = ref(null)

// Dynamically load the component based on agreementType
watch(
  () => props.agreementType,
  async (newType) => {
    if (newType === 'user') {
      agreementComponent.value = defineAsyncComponent(() =>
        import('./UserAgreement.vue')
      )
    } else if (newType === 'privacy') {
      agreementComponent.value = defineAsyncComponent(() =>
        import('./privacyPolicy.vue')
      )
    } else {
      agreementComponent.value = null
    }
  },
  { immediate: true } // Load immediately when the component mounts
)
</script>

<style lang="scss" scoped>
.agreement-dialog {
  .agreement-content {
    max-height: 70vh;
    overflow-y: auto;
  }
}
</style> 
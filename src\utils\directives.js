// 防抖点击指令
const clickThrottle = {
  mounted(el, binding) {
    if (typeof binding.value !== 'function') {
      console.warn('v-click-throttle 指令需要绑定一个函数');
      return;
    }

    const delay = parseInt(binding.arg, 10) || 1000;

    // 将状态和处理器存储在元素上，避免闭包陷阱
    el.__throttle_state = {
      isThrottling: false,
      handler: binding.value,
      delay: delay,
      timer: null
    };

    const listener = (e) => {
      const state = el.__throttle_state;
      if (state.isThrottling) {
        // 在节流期间，立即停止事件传播，防止其他监听器执行
        e.stopImmediatePropagation();
        return;
      }

      // 执行最新的处理器
      if (typeof state.handler === 'function') {
        state.handler(e);
      }

      state.isThrottling = true;
      state.timer = setTimeout(() => {
        state.isThrottling = false;
      }, state.delay);
    };

    el.__throttle_listener = listener;
    // 使用捕获模式确保监听器在内部监听器之前执行
    el.addEventListener('click', listener, true);
  },

  updated(el, binding) {
    if (typeof binding.value !== 'function') {
      if (el.__throttle_state) {
        el.__throttle_state.handler = null;
      }
      return;
    }

    const delay = parseInt(binding.arg, 10) || 1000;

    // 动态更新处理器和延迟时间
    if (el.__throttle_state) {
      el.__throttle_state.handler = binding.value;
      el.__throttle_state.delay = delay;
    }
  },

  unmounted(el) {
    // 清理资源
    if (el.__throttle_listener) {
      el.removeEventListener('click', el.__throttle_listener, true);
    }
    if (el.__throttle_state && el.__throttle_state.timer) {
      clearTimeout(el.__throttle_state.timer);
    }
    delete el.__throttle_state;
    delete el.__throttle_listener;
  }
};

// 导出指令
export { clickThrottle }

// 注册全局指令的函数
export function registerDirectives(app) {
  app.directive('click-throttle', clickThrottle)
} 
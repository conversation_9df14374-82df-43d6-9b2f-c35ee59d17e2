<template>
  <el-dialog
    title="产品意见反馈"
    v-model="dialogVisible"
    width="800px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form size="large" :model="form" ref="formRef" :rules="rules" label-width="120px">
      <el-form-item label="反馈类型" prop="type">
        <el-radio-group v-model="form.type" size="large">
          <el-radio 
            v-for="option in feedbackTypes" 
            :key="option.value" 
            :label="option.value"
            :disabled="feedbackTypesLoading"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="反馈内容" prop="content">
        <el-input
          type="textarea"
          v-model="form.content"
          placeholder="请详细描述您的意见或建议..."
          :rows="4"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item :label="`截图(${fileList.length}/4)`" prop="screenshots">
        <div class="upload-container" :class="{ 'is-hidden': fileList.length >= 4 }">
          <el-upload
            v-model:file-list="fileList"
            action="#"
            list-type="picture-card"
            :http-request="handleUpload"
            :before-upload="handleBeforeUpload"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :limit="4"
            :on-exceed="handleExceed"
            :auto-upload="true"
            accept=".jpeg,.png,.jpg,.gif"
            multiple
          >
           <el-icon><Plus /></el-icon>
          </el-upload>
        </div>
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input v-model="form.contact" placeholder="请输入您的联系方式"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="large" @click="handleClose">取 消</el-button>
        <el-button size="large" type="primary" @click="handleSubmit" :loading="isSubmitting">提 交</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="previewVisible" :destroy-on-close="true" class="image-preview-dialog">
    <template #header>
      <span>图片预览</span>
    </template>
    <div class="image-preview-container">
      <img :src="previewImage" alt="Preview Image" class="preview-image" />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { ElDialog, ElForm, ElFormItem, ElRadioGroup, ElRadio, ElInput, ElUpload, ElButton, ElIcon, ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { uploadImage } from '@/api/upload';
import { getDictData, addUserFeedback } from '@/api/feedback';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible']);

const store = useStore();
const userInfo = computed(() => store.state.user.userInfo);

const dialogVisible = ref(props.visible);
const form = ref({
  type: '',
  content: '',
  contact: '',
});
const fileList = ref([]);
const formRef = ref(null);
const isSubmitting = ref(false);
const feedbackTypes = ref([]);
const feedbackTypesLoading = ref(false);

const rules = {
  content: [
       { required: true, message: '请输入反馈内容', trigger: 'blur' }
  ],
};

const previewVisible = ref(false);
const previewImage = ref('');

// 加载反馈类型字典数据
const loadFeedbackTypes = async () => {
  feedbackTypesLoading.value = true;
  try {
    const response = await getDictData();
    if (response && response.data) {
      feedbackTypes.value = response.data.map(item => ({
        value: item.code,
        label: item.name
      }));
    }
  } catch (error) {
    console.error('加载反馈类型失败:', error);
  } finally {
    feedbackTypesLoading.value = false;
  }
};

// 组件挂载时加载反馈类型
onMounted(() => {
  loadFeedbackTypes();
});

watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    form.value.contact = userInfo.value?.phonenumber || '';
  }
  resetForm();
});

const handleClose = () => {
  emit('update:visible', false);
};

const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.value = {
        type: '',
        content: '',
        contact: userInfo.value?.phonenumber || '',
    };
    fileList.value = [];
    isSubmitting.value = false;
};

const handleBeforeUpload = (rawFile) => {
    const isImage = /^image\//.test(rawFile.type);
    if (!isImage) {
        ElMessage.error('只能上传图片文件!');
        return false;
    }
    return true;
};

const handleUpload = async (options) => {
    try {
        const res = await uploadImage(options.file);
        console.log('handleUpload', res);
        if (res.cdn_url) {
            // 找到当前正在上传的文件对象
            const fileIndex = fileList.value.findIndex(file => file.uid === options.file.uid);
            if (fileIndex > -1) {
                // 更新现有文件对象的URL
                fileList.value[fileIndex].url = res.cdn_url;
                fileList.value[fileIndex].status = 'success';
                fileList.value[fileIndex].response = res;
            }
        }
    } catch (error) {
        console.error('Upload failed:', error);
        // 上传失败时移除文件
        const fileIndex = fileList.value.findIndex(file => file.uid === options.file.uid);
        if (fileIndex > -1) {
            fileList.value.splice(fileIndex, 1);
        }
    }
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      isSubmitting.value = true;

      const fileList_new = fileList.value.map(f => ({
        fileName: f.name,
        path: f.url,
        wpsKey: f.uid,
        fileExt: 'png',
        fileSize: f.size
      }));
      const feedbackData = {
        platformSource: 'junheng_ai',
        content: form.value.content,
        typeCode: form.value.type || 'other',
        mobile: form.value.contact,
        realMobile: userInfo.value?.phonenumber,
        uid: userInfo.value?.userId,
        teacherName: userInfo.value?.nickName,  
        fileList: fileList_new,
        type: 2,
        schoolId: userInfo.value?.organizationId,
        schoolName: userInfo.value?.organizationName,
      };
      try {
        await addUserFeedback(feedbackData);
        ElMessage.success('反馈已提交，感谢您的支持！');
        handleClose();
      } catch (error) {
        console.error('Feedback submission failed:', error);
      } finally {
        isSubmitting.value = false;
      }
    }
  });
};

const handleRemove = (file) => {
    console.log('handleRemove', file);
    const index = fileList.value.findIndex(f => f.uid === file.uid);
    if (index > -1) {
        fileList.value.splice(index, 1);
    }
};

const handlePictureCardPreview = (uploadFile) => {
  previewImage.value = uploadFile.url;
  previewVisible.value = true;
};

const handleExceed = (files) => {
  ElMessage.warning(`最多只能上传4张图片`);
};
</script>

<style scoped lang="scss">
.el-upload__tip {
    font-size: 14px;
    color: #999;
    margin-top: 12px;
    display: block;
    text-align: left;
}

.upload-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.upload-container :deep(.el-upload--picture-card) {
    margin: 0 8px 0 0;
}
.upload-container {
  :deep(.el-upload-list__item.is-success:focus:not(:hover)) {
    display: none !important;
  }
}
.el-upload {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.is-hidden {
  :deep(.el-upload) {
    display: none !important;
  }
}

.image-preview-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: 70vh;
    overflow: hidden;
  }

  .image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
  }

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-preview-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 0 auto;
    }
    
    .preview-image {
      max-height: 60vh;
    }
  }
}
</style> 
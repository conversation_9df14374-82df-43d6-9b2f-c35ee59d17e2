import { loginBySms, getUserInfo, authThirdLogin} from '@/api/user'

const local_token = localStorage.getItem('aiToken') || ''
const local_userInfo = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {}

// 用户状态管理模块
export default {
  namespaced: true,
  state: {
    aiToken: local_token,
    userInfo: local_userInfo,
  },
  getters: {
    isLoggedIn: state => !!(state.userInfo && state.userInfo.userId),
    userId: state => state.userInfo.userId,
    aiToken: state => state.aiToken,
    roles: state => state.userInfo.roles,
    userInfo: state => state.userInfo,
  },
  mutations: {
    setToken(state, aiToken) {
      state.aiToken = aiToken
      localStorage.setItem('aiToken', aiToken)
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    }
  },
  actions: {
    login({ commit }, loginData) {
      return new Promise(async (resolve, reject) => {
        try {
          let res1 = await loginBySms({
            phonenumber: loginData.phoneNumber,
            smsCode: loginData.verifyCode
          })
          commit('setToken', res1.data.token)
          let res2 = await getUserInfo()
          commit('setUserInfo', res2.data.user || {})
          resolve(true)
        } catch (error) {
          console.error('登录失败', error)
          reject(error)
        }
      })
    },
    loginByQrCode({ commit }, token) {
      return new Promise(async (resolve, reject) => {
        try {
          commit('setToken', token)
          let res2 = await getUserInfo()
          commit('setUserInfo', res2.data.user || {})
          resolve(true)
        } catch (error) {
          console.error('登录失败', error)
          reject(error)
        }
      })
    },
    loginByThirdToken({ commit }, params) {
      return new Promise(async (resolve, reject) => {
        try {
          const { thirdToken, os } = params
          // 生产环境为true，开发和测试环境为false
          const isProduction = import.meta.env.MODE === 'production'
          let res = await authThirdLogin({ thirdToken, os, isLine: isProduction })
          const { token } = res.data
          commit('setToken', token)
          let res2 = await getUserInfo()
          commit('setUserInfo', res2.data.user || {})
          resolve(true)
        } catch (error) {
          console.error('授权登录失败', error)
          reject(error)
        }
      })
    },
    refreshUserInfo({ commit }) {
      return new Promise(async (resolve, reject) => {
        try {
          let res = await getUserInfo()
          commit('setUserInfo', res.data.user || {})
          resolve(true)
        } catch (error) {
          console.error('刷新用户信息失败', error)
          reject(error)
        }
      })
    },
    logout({ commit }) {
      return new Promise((resolve) => {
        commit('setToken', '')
        commit('setUserInfo', {})
        resolve(true)
      })
    }
  }
}
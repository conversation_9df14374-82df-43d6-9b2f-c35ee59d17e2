<template>
  <div class="app-container">
    <!-- 左侧导航栏 -->
    <div class="sidebar" :class="{ collapsed: isCollapsed }">
      <!-- 头部：Logo和标题 -->
      <div class="sidebar-header">
        <div class="logo-container" @click="handleLogoClick">
          <img src="@/assets/logo.png" alt="Logo" class="logo"/>
          <span v-if="!isCollapsed" class="app-title">均衡AI</span>
        </div>
        <el-button
            v-if="!isCollapsed"
            class="collapse-btn"
            @click="toggleCollapse"
            :icon="isCollapsed ? DArrowRight : DArrowLeft"
            circle
        />
      </div>

      <!-- 导航菜单 -->
      <custom-menu
          :menu-data="menuData"
          :is-collapsed="isCollapsed"
          :active-category="activeCategory"
          @select="handleMenuSelect"
          class="sidebar-menu"
      />

      <!-- 回到首页按钮 -->
      <div class="home-button-container" @click="handleHomeClick" v-if="$route.path !== '/'">
        <el-icon class="home-icon">
          <HomeFilled />
        </el-icon>
        <span v-if="!isCollapsed" class="home-text">回到首页</span>
      </div>

      <!-- 客服电话和意见反馈 -->
      <div class="service-container" v-if="!isCollapsed && $route.path === '/'">
        <!-- 客服电话 -->
        <div class="service-item service-phone" @click="handleServiceCall">
          <div class="service-content">
            <el-icon class="service-icon">
              <Phone />
            </el-icon>
            <div class="service-info">
              <span class="service-text">客服电话</span>
              <span class="service-number">************ 转0</span>
            </div>
          </div>
        </div>
        
        <!-- 意见反馈 -->
        <div class="service-item service-feedback" @click="handleFeedback">
          <div class="service-content">
            <el-icon class="service-icon">
              <EditPen />
            </el-icon>
            <div class="service-info">
              <span class="service-text">意见反馈</span>
              <span class="service-number">您的建议，我们的方向</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户信息 -->
      <template v-if="isLoggedIn">
        <div class="user-info-container logged-in">
          <div class="user-info-left" @click="handleUserClick">
            <img :src="userAvatar" alt="User Avatar" class="user-avatar"/>
            <span v-if="!isCollapsed" class="user-nickname">{{ userNickname }}</span>
          </div>
          <el-dropdown
              v-if="!isCollapsed"
              trigger="click"
              size="large"
              @command="handleCommand"
              @visible-change="onDropdownVisibleChange"
              :hide-on-click="true"
              popper-class="user-dropdown-popper"
              placement="top"
          >
            <div class="settings-icon">
              <el-icon :size="24">
                <Setting/>
              </el-icon>
              <el-badge v-if="feedbackCount > 0" is-dot></el-badge>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="admin" v-if="isAdmin">
                  <el-icon>
                    <Monitor/>
                  </el-icon>
                  点击统计
                </el-dropdown-item>
                <el-dropdown-item command="profile">
                  <el-icon>
                    <User/>
                  </el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="feedback" style="position: relative;">
                  <el-icon>
                    <ChatDotSquare/>
                  </el-icon>
                  我的反馈
                  <el-badge v-if="feedbackCount > 0" :value="feedbackCount" class="feedback-badge"></el-badge>
                </el-dropdown-item>
                <el-dropdown-item command="logout">
                  <el-icon>
                    <SwitchButton/>
                  </el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
      <template v-else>
        <div class="user-info-container" @click="handleUserClick">
          <img :src="userAvatar" alt="User Avatar" class="user-avatar"/>
          <span v-if="!isCollapsed" class="user-nickname">{{ userNickname }}</span>
        </div>
      </template>
    </div>

    <!-- 右侧主内容区 -->
    <div class="main-content">
      <div class="content-wrapper">
        <!-- 路由视图 -->
        <router-view />
      </div>
    </div>

    <!-- 登录弹窗 -->
    <LoginDialog
        v-model:visible="loginDialogVisible"
        @login-success="handleLoginSuccess"
    />

    <!-- 个人中心弹窗 -->
    <ProfileDialog
        v-model:visible="profileDialogVisible"
        @login-success="handleLoginSuccess"
        @logout="handleCommand('logout')"
    />

    <!-- 意见反馈弹窗 -->
    <FeedbackDialog
        v-model:visible="feedbackDialogVisible"
    />

     <!-- 反馈抽屉组件 -->
     <FeedbackDrawer v-model:visible="showFeedbackDrawer" />
  </div>
</template>

<script setup>
import {ref, onMounted, nextTick, computed, watch} from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import {useRouter} from 'vue-router';
import {useStore} from 'vuex';
import LoginDialog from '../login/LoginDialog.vue';
import ProfileDialog from '../profile/ProfileDialog.vue';
import FeedbackDialog from '../feedback/FeedbackDialog.vue';
import FeedbackDrawer from '../feedback/FeedbackDrawer.vue';
import CustomMenu from './CustomMenu.vue';
import {School, DArrowRight, DArrowLeft, Document, Monitor, User, SwitchButton, Setting, HomeFilled, Phone, EditPen, ChatDotSquare} from '@element-plus/icons-vue';
import { getUserFeedbackCount } from '@/api/feedback';

const router = useRouter();
const store = useStore();

// 响应式数据
const isCollapsed = ref(false);
const activeCategory = computed(() => store.getters.activeCategory);
const loginDialogVisible = ref(false);
const profileDialogVisible = ref(false); // 新增：控制个人中心弹窗显示
const feedbackDialogVisible = ref(false); // 新增：控制意见反馈弹窗显示
const showFeedbackDrawer = ref(false); // 新增：控制意见反馈抽屉显示
const feedbackCount = ref(0);

// 用户信息相关
const isLoggedIn = computed(() => store.getters.isLoggedIn);
const userInfo = computed(() => store.state.user.userInfo);
const roles = computed(() => store.getters.roles);
const isAdmin = computed(() => {
  const roleKeys = roles.value.map(role => role.roleKey);
  return roleKeys.includes('admin');
});


const userAvatar = computed(() => {
  if (isLoggedIn.value && userInfo.value && userInfo.value.avatar) {
    return userInfo.value.avatar;
  }
  return new URL('@/assets/user.png', import.meta.url).href; // 确保路径正确
});

const userNickname = computed(() => {
  if (isLoggedIn.value && userInfo.value && userInfo.value.nickName) {
    return userInfo.value.nickName;
  }
  return '未登录';
});

// 折叠/展开侧边栏
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 处理菜单选择
const handleMenuSelect = (index) => {
  store.commit('ui/setActiveCategory', index);
  // 如果不在首页，先跳转到首页
  if (router.currentRoute.value.path !== '/') {
    router.push('/').then(() => {
      // 等待路由跳转完成后再滚动
      nextTick(() => {
        const element = document.getElementById(index);
        if (element) {
          element.scrollIntoView({behavior: 'smooth'});
        }
      });
    });
  } else {
    // 已经在首页，直接滚动
    nextTick(() => {
      const element = document.getElementById(index);
      if (element) {
        element.scrollIntoView({behavior: 'smooth'});
      }
    });
  }
};

// 菜单数据 - 从 store 获取
const menuData = computed(() => store.getters.menuData);

// 确保数据已加载（如果还未加载则触发加载）
const ensureAppDataLoaded = () => {
  if (!store.getters.isAppDataLoaded && !store.getters.isAppDataLoading) {
    store.dispatch('app/loadAppData');
  }
};

// 处理登录成功
const handleLoginSuccess = () => {
  loginDialogVisible.value = false;
  onDropdownVisibleChange(true);
};

// 处理Logo点击
const handleLogoClick = () => {
  if (isCollapsed.value) {
    isCollapsed.value = false;
  } else {
    // 点击Logo跳转到首页
    router.push('/');
  }
};

// 处理回到首页按钮点击
const handleHomeClick = () => {
  router.push('/');
};

// 处理客服电话点击
const handleServiceCall = () => {
  // 复制客服电话到剪贴板并显示提示
  const phoneNumber = '************ 转0';
  navigator.clipboard.writeText(phoneNumber).then(() => {
    ElMessage.success(`客服电话 ${phoneNumber} 已复制`);
  }).catch(() => {
    ElMessage.info(`客服电话：${phoneNumber}`);
  });
};

// 处理意见反馈点击
const handleFeedback = () => {
  if(!isLoggedIn.value) {
    loginDialogVisible.value = true;
  } else {
    feedbackDialogVisible.value = true;
  }
};

// 处理用户区域点击
const handleUserClick = () => {
  if (!isLoggedIn.value) {
    loginDialogVisible.value = true;
  } else {
    // 已登录用户点击头像和名称时，打开个人中心弹窗
    profileDialogVisible.value = true;
  }
};

// 处理下拉菜单命令
const handleCommand = (command) => {
  if (command === 'profile') {
    profileDialogVisible.value = true; // 打开个人中心弹窗
  } else if (command === 'logout') {
    ElMessageBox.confirm(
        '确定要退出登录吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
    ).then(() => {
      profileDialogVisible.value = false;
      store.dispatch('user/logout').then(() => {
        ElMessage.success('已退出登录');
        router.replace('/')
      });
    }).catch(() => {
    });
  } else if (command === 'admin') {
    router.push('/admin');
  } else if (command === 'feedback') {
    showFeedbackDrawer.value = true;
  }
};

const onDropdownVisibleChange = (visible) => {
  if (visible) {
    getUserFeedbackCount({
      uid: userInfo.value.userId,
      platformSource: 'junheng_ai'
    }).then(res => {
      feedbackCount.value = res.data || 0;
    });
  }
};

// 监听路由变化，在特定页面自动折叠侧边栏
watch(() => router.currentRoute.value.path, (newPath) => {
  // 当进入 aiTalk 页面时，自动折叠侧边栏
  if (newPath === '/aiTalk') {
    isCollapsed.value = true;
  }
}, { immediate: true });

watch(showFeedbackDrawer, (newVal) => {
  onDropdownVisibleChange(true);
});

onMounted(() => {
  ensureAppDataLoaded();
  onDropdownVisibleChange(true);
});
</script>

<style scoped lang="scss">

:deep(.feedback-badge) {
  top: 3px;
  right: -2px;
}

.app-container {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 侧边栏样式 */
.sidebar {
  width: 250px;
  background-color: #eef5ff; /* 修改为淡蓝色背景 */
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: fixed;
  height: 100vh;
  /* overflow-y: auto; */
  /* 移除，确保用户信息区固定在底部 */
  z-index: 10;
  display: flex; /* 新增 */
  flex-direction: column; /* 新增 */
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #d8e6fd; /* 更改边框颜色，与淡蓝色背景搭配 */
  height: 72px;
  box-sizing: border-box;
  background-color: #eef5ff; /* 与侧边栏背景色一致 */
}

.logo-container {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
}

.logo {
  width: 40px;
  height: 40px;
}

.app-title {
  margin-left: 12px;
  font-size: 22px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#00c6fb), to(#005bea));
  background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.collapse-btn {
  color: #409EFF; /* 直接使用主题蓝色 */
  border: 2px solid #409EFF; /* 加粗边框并使用主题色 */
  font-size: 20px; /* 增大图标尺寸 */
  background-color: white; /* 使用纯白背景 */
  padding: 10px; /* 增大内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

.collapse-btn:hover {
  background-color: #409EFF; /* 悬停时背景变为主题色 */
  color: white; /* 悬停时图标变为白色 */
  border-color: #409EFF; /* 保持边框颜色 */
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4); /* 增强阴影效果 */
  transform: translateY(-1px); /* 轻微上浮效果 */
}

.sidebar-menu {
  margin-top: 20px;
  border: none;
  flex: 1;
  overflow-y: auto; /* 新增，让菜单内容可滚动 */
}

/* 优化折叠动画效果 */
.sidebar-menu :deep(.el-menu--collapse) {
  width: 64px;
}

.sidebar-menu :deep(.el-menu--collapse .el-menu-item span),
.sidebar-menu :deep(.el-menu--collapse .el-sub-menu__title span) {
  opacity: 0;
  transition: opacity 0.2s ease-in;
}

.sidebar-menu :deep(.el-menu-item span),
.sidebar-menu :deep(.el-sub-menu__title span) {
  opacity: 1;
  transition: opacity 0.3s ease-out 0.1s;
}

.sidebar-menu :deep(.el-menu--collapse .el-sub-menu__icon-arrow) {
  display: none !important;
}

.sidebar-menu :deep(.el-sub-menu__icon-arrow) {
  display: none !important;
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background-color: rgba(24, 144, 255, .1);
  color: #1890ff;
  font-weight: 600;
}

.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background-color: rgba(24, 144, 255, .05);
}

.sidebar-menu :deep(.el-icon) {
  font-size: 20px;
  margin-right: 12px;
}

/* 主内容区样式 */
.main-content {
  flex: 1;
  margin-left: 250px;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  position: relative;
  background:url('@/assets/index/bg-b.png') 0 0 no-repeat;
  background-size: cover;
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.1);
  z-index: 0;
}

.sidebar.collapsed + .main-content {
  margin-left: 64px;
}

.content-wrapper {
  position: relative;
  z-index: 10;
  min-height: 100vh;
}
@media (max-width: 768px) {
  .sidebar.collapsed + .main-content {
    margin-left: 0 !important;
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    margin-left: 0;
  }

  .sidebar {
    position: fixed;
    z-index: 2000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }
}

.logo-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.sidebar.collapsed .logo-container {
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-radius: 4px;
  padding: 4px;
  margin: -4px;
}

.sidebar.collapsed .logo-container:hover {
  background-color: #d8e6fd; /* 更改为更深一点的蓝色 */
}

/* 回到首页按钮样式 */
.home-button-container {
  padding: 12px 30px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: auto; /* 将回到首页按钮推到底部 */
  background-color: #eef5ff;
  position: relative;
  overflow: hidden;
}

.home-button-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.home-button-container:hover {
  background-color: #d8e6fd;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.home-button-container:hover::before {
  left: 100%;
}

.home-button-container:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.1);
}

.sidebar.collapsed .home-button-container {
  justify-content: center; /* 折叠时图标居中 */
  padding: 16px 12px;
}

/* 客服和意见反馈容器样式 */
.service-container {
}

.service-item {
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.service-item:last-child {
  border-bottom: none;
}

.service-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 16px;
}

.sidebar.collapsed .service-content {
  justify-content: center;
  padding: 16px 12px;
}

.service-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.service-item:hover {
  background-color: #d8e6fd;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.service-item:hover::before {
  left: 100%;
}

.service-item:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.1);
}

.service-icon {
  font-size: 24px;
  color: #409EFF;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(64, 158, 255, 0.2));
  margin-right: 12px;
  flex-shrink: 0; /* 防止图标在动画期间被压缩 */
}

.service-item:hover .service-icon {
  color: #1890ff;
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(64, 158, 255, 0.3));
  font-size: 24px;
}

.sidebar.collapsed .service-icon {
  margin-right: 0;
  font-size: 24px;
}

.service-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
  /* 新增：优化折叠/展开动画的关键样式 */
  white-space: nowrap;
  overflow: hidden;
  opacity: 1;
  transition: opacity 0.2s ease-out 0.1s, width 0.2s ease-out 0.1s;
}

.sidebar.collapsed .service-info {
  opacity: 0;
  width: 0;
  transition: opacity 0.1s, width 0.2s ease-in;
}

.service-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
  line-height: 1.2;
}

.service-item:hover .service-text {
  color: #1890ff;
  text-shadow: 0 1px 2px rgba(64, 158, 255, 0.1);
}

.service-number {
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.service-item:hover .service-number {
  color: #409EFF;
}

.home-icon {
  font-size: 25px;
  color: #409EFF;
  margin-right: 20px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(64, 158, 255, 0.2));
}

.home-button-container:hover .home-icon {
  color: #1890ff;
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(64, 158, 255, 0.3));
}

.sidebar.collapsed .home-icon {
  margin-right: 0;
  font-size: 25px;
}

.home-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
  position: relative;
}

.home-button-container:hover .home-text {
  color: #1890ff;
  text-shadow: 0 1px 2px rgba(64, 158, 255, 0.1);
}

/* 用户信息区样式 */
.user-info-container {
  padding: 16px;
  border-top: 1px solid #d8e6fd; /* 更改边框颜色，与淡蓝色背景搭配 */
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 0; /* 移除自动推到底部，因为现在回到首页按钮在底部 */
  background-color: #eef5ff; /* 与侧边栏背景色一致 */
}

.user-info-container.logged-in {
  justify-content: space-between; /* 在登录状态下，用于分隔用户信息和设置图标 */
  cursor: default; /* 整体不再是可点击的 */
}

.user-info-left {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.settings-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: relative;
  :deep(.el-badge) {
    position: absolute;
    top: 0;
    right: -4px;
  }
  :deep(.el-badge__content.is-dot) {
    width: 10px;
    height: 10px;
  }
}

.settings-icon:hover {
  background-color: #d8e6fd; /* 更改为更深一点的蓝色 */
}

.settings-icon .el-icon {
  font-size: 18px;
  color: #606266;
}

.user-info-container:hover {
  background-color: #d8e6fd; /* 更改为更深一点的蓝色 */
}

.sidebar.collapsed .user-info-container {
  justify-content: center; /* 折叠时头像居中 */
}

.sidebar.collapsed .user-info-container.logged-in {
  justify-content: center; /* 折叠时登录状态下也居中 */
}

.sidebar.collapsed .user-info-left {
  justify-content: center;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
}

.sidebar.collapsed .user-avatar {
  margin-right: 0;
}

.user-nickname {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.full-width-dropdown {
  display: block;
  width: 100%;
}
</style>
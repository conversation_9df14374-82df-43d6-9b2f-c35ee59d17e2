<template>
  <div class="app-container">
    <router-view />
  </div>
</template>

<style lang="scss">
@use 'element-plus/dist/index.css';

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

.app-container {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

.katex-html {
  display: none;
}

/* 智能体页面全屏样式 */
.agent-page {
  min-height: 100vh;
  background-color: #fff;
}
</style>

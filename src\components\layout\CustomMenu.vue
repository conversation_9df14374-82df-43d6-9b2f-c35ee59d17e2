<template>
  <nav class="custom-menu" :class="{ 'is-collapsed': isCollapsed }">
    <ul>
      <li
        v-for="item in menuData"
        :key="item.index"
        class="menu-item-level1"
        :class="{ 'has-children': item.children && item.children.length > 0, 'active': isActive(item) }"
      >
        <div
          class="menu-item-level1-content"
          @click="handleLevel1Click(item)"
          :style="{ cursor: 'pointer' }"
        >
          <img v-if="isExternalIcon(item.icon)" :src="item.icon" alt="" class="menu-custom-icon" />
          <el-icon v-else-if="item.icon" class="menu-icon">
            <component :is="getIconComponent(item.icon)" />
          </el-icon>
          <span class="menu-title">{{ item.title }}</span>
        </div>
        <ul v-if="!isCollapsed && item.children && item.children.length > 0" class="submenu-level2">
          <li
            v-for="child in item.children"
            :key="child.index"
            class="menu-item-level2"
            :class="{ 'active': activeCategory === child.index }"
            @click.stop="handleLevel2Click(child)"
          >
            <img v-if="isExternalIcon(child.icon)" :src="child.icon" alt="" class="submenu-custom-icon" />
            <el-icon v-else-if="child.icon" class="submenu-icon">
              <component :is="getIconComponent(child.icon)" />
            </el-icon>
            <span class="menu-title">{{ child.title }}</span>
          </li>
        </ul>
      </li>
    </ul>
  </nav>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue';
import { School, Document, Monitor } from '@element-plus/icons-vue';

const props = defineProps({
  menuData: {
    type: Array,
    required: true,
  },
  isCollapsed: {
    type: Boolean,
    default: false,
  },
  activeCategory: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['select']);

const iconComponents = {
  School,
  Document,
  Monitor,
};

const isExternalIcon = (icon) => {
  return typeof icon === 'string' && (icon.startsWith('http') || icon.startsWith('https'));
};

const getIconComponent = (iconName) => {
  return iconComponents[iconName] || null;
};

const handleLevel1Click = (item) => {
  if (props.isCollapsed) {
    if (item.children && item.children.length > 0) {
      emit('select', item.children[0].index);
    } else {
      emit('select', item.index);
    }
  } else {
    if (item.children && item.children.length > 0) {
      emit('select', item.children[0].index);
    } else {
      emit('select', item.index);
    }
  }
};

const handleLevel2Click = (childItem) => {
  emit('select', childItem.index);
};

const isActive = (item) => {
  if (item.children && item.children.length > 0) {
    return item.children.some(child => child.index === props.activeCategory);
  }
  return props.activeCategory === item.index;
};
</script>

<style scoped>
.custom-menu {
  width: 100%;
  background-color: #eef5ff;
  color: #333;
}

.custom-menu.is-collapsed {
  width: 64px; /* Adjust to your collapsed width */
}

.custom-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item-level1 {
  /* Styles for level 1 menu items */
}

.menu-item-level1-content {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.custom-menu.is-collapsed .menu-item-level1-content {
  padding: 15px;
  justify-content: center;
}

.menu-custom-icon {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  object-fit: contain;
}

.custom-menu.is-collapsed .menu-custom-icon {
  margin-right: 0;
  width: 40px;
  height: 40px;
}

.submenu-custom-icon {
  width: 40px;
  height: 40px;
  margin-right: 8px;
  object-fit: contain;
}

.menu-icon {
  font-size: 26px;
  margin-right: 12px;
}

.custom-menu.is-collapsed .menu-icon {
  margin-right: 0;
  font-size: 28px;
}

.submenu-icon {
  font-size: 20px;
  margin-right: 8px;
}

.menu-title { /* Applies to L1 and L2 titles via shared class or direct styling if needed */
  font-size: 18px; /* Updated: Font size increased */
  font-weight: 500;
  transition: opacity 0.3s ease, width 0.3s ease; 
  white-space: nowrap; 
  overflow: hidden;    
  display: inline-block; 
  vertical-align: middle; 
}

.custom-menu.is-collapsed .menu-title {
  opacity: 0;
  width: 0;
  margin-left: 0; 
}

/* Default L1 hover and active (for L1 items without children) */
.menu-item-level1:not(.has-children) > .menu-item-level1-content:hover,
.menu-item-level1.active:not(.has-children) > .menu-item-level1-content {
  background-color: #d8e6fd;
  color: #1890ff;
  font-weight: bold; /* Added font-weight */
}

/* L1 with children: hover effect (only icon/title color change, no background) */
.menu-item-level1.has-children > .menu-item-level1-content:hover .menu-icon,
.menu-item-level1.has-children > .menu-item-level1-content:hover .menu-title {
  color: #1890ff; 
}
.menu-item-level1.has-children > .menu-item-level1-content:hover {
   background-color: #eef5ff;
}


/* L1 with children: active state (when a child is active) */
.menu-item-level1.has-children.active > .menu-item-level1-content {
   background-color: #eef5ff;
   /* color: #1890ff; */ /* Removed direct color setting here */
}
.menu-item-level1.has-children.active > .menu-item-level1-content .menu-icon {
  color: #333; /* Updated: Icon is default color when child is active */
}
.menu-item-level1.has-children.active > .menu-item-level1-content .menu-title {
  color: #333; /* Title is default color */
  font-weight: normal; /* Ensure L1 title is not bold if child is active */
}


.submenu-level2 {
  list-style: none;
  padding-left: 0;
  background-color: #eef5ff;
}

.menu-item-level2 {
  padding: 12px 20px 12px 40px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  display: flex;
  align-items: center;
  position: relative;
}

.menu-item-level2:hover {
  background-color: #d8e6fd;
  color: #1890ff;
}

.menu-item-level2.active {
  color: #1890ff;
  background-color: #d8e6fd;
  font-weight: bold; /* Kept font-weight bold for active L2 */
}
</style> 
<template>
  <div class="translate-container" ref="containerRef">
    <agent-history-entry
        category="TranslateAI"
        @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">中英互译助手</h1>
      <p class="subtitle">智能中英互译，实时对话，精准理解语境与专业术语</p>
    </div>

    <div class="input-container">
      <div class="input-box">
        <div class="difficulty-selector">
          <span class="difficulty-label">翻译难度：</span>
          <el-radio-group v-model="difficultyLevel" size="large">
            <el-radio-button label="elementary">小学</el-radio-button>
            <el-radio-button label="junior">初中</el-radio-button>
            <el-radio-button label="senior">高中</el-radio-button>
          </el-radio-group>
        </div>

        <el-input
            v-model="userInput"
            type="textarea"
            :rows="4"
            placeholder="输入中文将翻译成英文，输入英文将翻译成中文..."
            @keydown.enter.exact.prevent="sendMessage"
            class="user-input"
        />

        <div class="action-buttons">
          <div class="hint-text"></div>
          <el-button
              v-if="!loading"
              :type="loading ? 'warning' : 'primary'"
              size="large"
              circle
              :disabled="!loading && !userInput.trim()"
              class="submit-btn"
              v-click-throttle="sendMessage"
          >
            {{ loading ? '中止翻译' : '开始翻译' }}
          </el-button>
        </div>
      </div>
    </div>

    <ai-loading :visible="showAiLoading"/>

    <!-- 翻译结果展示区域 -->
    <div v-if="translationResult" class="translation-display">
      <div class="translation-card">
        <!-- 复制按钮 -->
        <div class="action-icons">
          <el-icon class="copy-icon" style="cursor: pointer;" @click="copyTranslation">
            <CopyDocument/>
          </el-icon>
        </div>

        <!-- 原文 -->
        <div class="original-text">
          <div class="text-label">原文</div>
          <div class="text-content">{{ lastUserInput }}</div>
        </div>

        <!-- 分隔符 -->
        <div class="divider">
          <div class="divider-line"></div>
          <div class="divider-icon">→</div>
          <div class="divider-line"></div>
        </div>

        <!-- 译文 -->
        <div class="translated-text">
          <div class="text-label">译文</div>
          <div class="text-content">{{ translationResult }}</div>
        </div>

        <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;"
             v-if="loading && translationResult">
          <el-button
              type="warning"
              size="large"
              v-click-throttle="sendMessage"
              round
          >中止翻译
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {ref, computed, nextTick, watch, onMounted, inject} from 'vue'
import {useRouter} from 'vue-router'
import {CopyDocument} from '@element-plus/icons-vue'
import {ElMessage} from 'element-plus'
import {fetchEventSource} from '@microsoft/fetch-event-source'
import {useStore} from 'vuex'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import AiLoading from '@/components/common/AiLoading.vue'

export default {
  name: 'Translate',
  components: {
    CopyDocument,
    AgentHistoryEntry,
    AiLoading
  },
  setup() {
    const trackingService = inject('trackingService');
    const store = useStore()
    const router = useRouter()

    const userInput = ref('')
    const loading = ref(false)
    const reader = ref(null)
    const containerRef = ref(null)
    const difficultyLevel = ref('junior')
    const chatService = ref(null)
    const translationResult = ref(null)
    const lastUserInput = ref('')

    const showAiLoading = computed(() => loading.value && !translationResult.value)

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('TranslateAI', '中英互译助手会话')
          console.log('中英互译助手重新初始化完成')
        }
      } catch (error) {
        console.error('中英互译助手重新初始化错误:', error)
      }
    }

    onMounted(async () => {
      try {
        chatService.value = new AgentChatService(store)
        await chatService.value.initialize('TranslateAI', '中英互译助手会话')
        console.log('中英互译助手初始化完成')
      } catch (error) {
        console.error('中英互译助手初始化错误:', error)
      }
    })

    // 根据难度级别获取系统提示词
    const getSystemPrompt = (level) => {
      const basePrompt = ``

      // 根据不同难度级别添加特定的指导
      const levelSpecificPrompts = {
        elementary: `
6. 请将翻译调整为小学难度：
   - 使用简单的词汇和短句
   - 避免复杂从句和被动语态
   - 使用基础常见词汇，避免高级或专业词汇
   - 适合小学生理解的翻译，简单直白
   - 如果原文太复杂，可以适当简化，但保持核心意思`,

        junior: `
6. 请将翻译调整为初中难度：
   - 使用适中难度的词汇和语法结构
   - 可以使用一些简单的从句和短语
   - 使用初中生词汇表范围内的常见词汇
   - 适合初中生理解的翻译难度
   - 准确翻译日常交流中的常见表达`,

        senior: `
6. 请将翻译调整为高中难度：
   - 使用准确专业的词汇和丰富的语法结构
   - 可以使用复杂从句、被动语态和高级表达
   - 翻译时保留原文的语言风格和修辞特点
   - 准确翻译专业术语和习惯表达
   - 体现高中及以上英语水平，适当使用高级词汇和表达方式`
      }

      return basePrompt + levelSpecificPrompts[level]
    }

    // 系统角色提示词，根据难度级别动态获取
    const roleSystem = computed(() => {
      return getSystemPrompt(difficultyLevel.value)
    })

    // 自动滚动到底部
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };

    // 监听翻译结果变化，自动滚动
    watch(translationResult, () => {
      scrollToBottom();
    });

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          // reader.value 现在是 AbortController
          reader.value.abort('用户取消了操作');
          console.log('Abort signal sent.');
          // AbortController 没有 close 方法，abort() 会触发 onerror
        } catch (error) {
          console.error('取消流式输出错误:', error)
        } finally {
          // 立即重置状态，确保按钮恢复
          loading.value = false;
          reader.value = null; // 清理 controller 引用
        }
      }
    }

    // 发送消息
    const sendMessage = async () => {
      if (loading.value) {
        await cancelStream()
        return
      }
      if (!userInput.value.trim()) {
        return
      }

      const userMessageContent = userInput.value.trim()
      lastUserInput.value = userMessageContent
      loading.value = true
      translationResult.value = null

      try {
        let requestMessages = [
          {role: 'user', content: userMessageContent}
        ]
        let requestBody = {
          messages: requestMessages,
          stream: true,
          modelType: 'open',
          formType: 'TransAI',
          dynamicPrompt:roleSystem.value
        }
        const controller = new AbortController();
        reader.value = controller;
        let finalAssistantMessage = ''
        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`
        fetchEventSource(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal,
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
              loading.value = false;
              reader.value = null;
              throw new Error(`HTTP error! status: ${response.status}`);
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              loading.value = false;
              reader.value = null;
              throw new Error(msg.data);
            }

            const data = msg.data;

            try {
              const jsonData = JSON.parse(data);
              if (jsonData.error) {
                loading.value = false;
                reader.value = null;
                translationResult.value = `翻译出错: ${jsonData.error.message}`;
                return;
              }

              if (jsonData.choices && jsonData.choices[0]) {
                const content = jsonData.choices[0].message.content
                if (content) {
                  finalAssistantMessage += content;
                  translationResult.value = finalAssistantMessage;
                  scrollToBottom();
                }
              }
            } catch (error) {
              console.error('JSON parse error:', error);
              // 尝试继续处理后续消息
            }

          },
          async onclose() {
            console.log('连接关闭');
            if (loading.value) {
              loading.value = false;
              reader.value = null;
            }
            if (!finalAssistantMessage) {
              translationResult.value = '(翻译中断或无内容返回)';
            }
            if (lastUserInput.value && finalAssistantMessage && chatService.value) {
              console.log("Saving conversation:", lastUserInput.value, finalAssistantMessage);
              await chatService.value.saveMessages(lastUserInput.value, finalAssistantMessage);
            }
            userInput.value = '';
          },
          onerror(err) {
            console.error('Stream error:', err);
            if (err.name !== 'AbortError') {
              translationResult.value = '抱歉，翻译流传输过程中出现错误。';
            } else {
              console.log('Stream aborted by user.');
            }
            loading.value = false;
            reader.value = null;
          }
        });
      } catch (error) {
        console.error(error);
        translationResult.value = '抱歉，启动翻译时出错。';
        loading.value = false;
        reader.value = null;
      }
      // 不需要 finally 块了，状态管理在 onclose 和 onerror 中完成
      trackingService.trackEvent('TransAI')
    }

    // 复制消息内容
    const copyMessage = (content) => {
      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = content
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 尝试复制到剪贴板
        document.execCommand('copy')
        ElMessage.success('已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }

    // 复制翻译对话
    const copyTranslation = () => {
      const conversationText = `原文：${lastUserInput.value}\n译文：${translationResult.value}`
      copyMessage(conversationText)
    }

    return {
      userInput,
      loading,
      difficultyLevel,
      containerRef,
      sendMessage,
      copyMessage,
      reinitializeChatSession,
      translationResult,
      lastUserInput,
      copyTranslation,
      chatService,
      showAiLoading
    }
  }
}
</script>

<style scoped>
.translate-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "✨";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.input-container {
  margin: 3rem 0;
}

.input-box {
  border: 2px solid #e0e0ff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 0 25px rgba(0, 0, 255, 0.08);
  background-color: #fff;
  position: relative;
}

.difficulty-selector {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.difficulty-label {
  margin-right: 0.5rem;
  color: #606266;
  font-size: 0.9rem;
}

.user-input {
  margin-bottom: 1.5rem;
}

:deep(.el-textarea__inner) {
  padding: 1.2rem;
  line-height: 1.7;
  border-radius: 1rem;
  border-color: #e0e0ff;
}

:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
  border-color: #409eff;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
}

.hint-text {
  color: #999;
  font-size: 0.9rem;
}

:deep(.submit-btn.el-button) {
  width: 80px;
  height: 80px;
}

/* 响应式设计 */
@media (max-width: 1250px) {
  .translate-container {
    width: 95%;
  }
}

@media (max-width: 768px) {
  .translate-container {
    width: 100%;
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .input-box {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  :deep(.el-textarea__inner) {
    padding: 0.8rem;
  }

  :deep(.submit-btn.el-button) {
    width: 56px;
    height: 56px;
  }

  .translation-card {
    padding: 2rem;
    margin: 0 1rem;
  }

  .text-content {
    font-size: 1.1rem;
    padding: 1.2rem;
  }

  .divider {
    margin: 1.5rem 0;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .input-container {
    margin: 2rem 0;
  }

  .input-box {
    padding: 1rem;
  }

  :deep(.submit-btn.el-button) {
    width: 50px;
    height: 50px;
  }

  .translation-card {
    padding: 1.5rem;
    margin: 0 0.5rem;
    border-radius: 16px;
  }

  .text-label {
    font-size: 1rem;
  }

  .text-content {
    font-size: 1rem;
    padding: 1rem;
  }

  .action-icons {
    top: 1rem;
    right: 1rem;
  }

  .copy-icon {
    font-size: 1rem;
  }

  .divider {
    margin: 1rem 0;
  }

  .divider-icon {
    width: 32px;
    height: 32px;
    font-size: 1.2rem;
  }
}

.translation-display {
  margin: 2rem 0;
}

.translation-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 2rem;
  width: 100%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  transition: all 0.3s ease;
}

.translation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
  0 20px 20px -5px rgba(0, 0, 0, 0.08);
}

.action-icons {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  display: flex;
  align-items: center;
}

.copy-icon {
  margin-right: 10px;
  font-size: 1.1rem;
}

.original-text, .translated-text {
  text-align: left;
}

.text-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 1rem;
}

.text-content {
  font-size: 1.25rem;
  line-height: 1.7;
  color: #1e293b;
  font-weight: 500;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  min-height: 60px;
  word-break: break-word;
  transition: all 0.2s ease;
  text-align: left;
}

.original-text .text-content {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
  border-color: rgba(59, 130, 246, 0.2);
}

.translated-text .text-content {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
  border-color: rgba(16, 185, 129, 0.2);
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2rem 0;
  position: relative;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, #e2e8f0, transparent);
}

.divider-icon {
  margin: 0 1rem;
  font-size: 1.5rem;
  color: #3b82f6;
  font-weight: bold;
  background: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}


</style>
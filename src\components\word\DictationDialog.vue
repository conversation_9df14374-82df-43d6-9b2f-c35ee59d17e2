<template>
  <el-dialog
    v-model="dialogVisible"
    width="100%"
    :close-on-click-modal="false"
    :before-close="handleClose"
    custom-class="dictation-dialog"
    top="0"
    fullscreen
    destroy-on-close
    :show-close="false"
  >
    <template #header>
      <div class="custom-dialog-header">
        <div class="dialog-title">单词听写</div>
      </div>
    </template>

    <div class="dictation-container">
      <!-- 听写卡片 -->
      <div class="word-card-container">
        <div
          class="navigation-button prev-button"
          @click="currentIndex > 0 ? prevWord() : null"
          v-if="words.length > 0"
          :class="{ 'disabled': currentIndex === 0 }"
        >
          <el-icon><ArrowLeft /></el-icon>
        </div>

        <div class="dictation-card" v-if="words.length > 0">
          <div class="word-content">
            <div class="word-chinese">{{ words[currentIndex].chinese }}</div>
          </div>
          <div v-if="isSpeaking" class="playing-indicator">
            <el-icon class="speaker-icon"><Microphone /></el-icon>
          </div>
        </div>

        <div
          class="navigation-button next-button"
          @click="currentIndex < words.length - 1 ? nextWord() : null"
          v-if="words.length > 0"
          :class="{ 'disabled': currentIndex === words.length - 1 }"
        >
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>

      <!-- 底部控制栏 -->
      <div class="bottom-controls">
        <!-- 听写设置 -->
        <div class="setting-item">
          <span class="setting-label">播报间隔：</span>
          <el-radio-group v-model="dictationInterval" size="large">
            <el-radio-button :label="3">3秒</el-radio-button>
            <el-radio-button :label="5">5秒</el-radio-button>
            <el-radio-button :label="10">10秒</el-radio-button>
          </el-radio-group>
        </div>

        <div class="setting-item">
          <span class="setting-label">播报模式：</span>
          <el-radio-group :model-value="playbackMode" @update:model-value="handlePlaybackModeChange" size="large">
            <el-radio-button label="order">顺序播报</el-radio-button>
            <el-radio-button label="random">随机播报</el-radio-button>
          </el-radio-group>
        </div>

        <div class="setting-item">
          <span class="setting-label">播报方式：</span>
          <el-radio-group v-model="announcementMethod" size="large">
            <el-radio-button label="ch">中文播报</el-radio-button>
            <el-radio-button label="en">英文播报</el-radio-button>
            <el-radio-button label="ch_en">中英文播报</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 控制按钮 -->
        <div class="control-buttons">
          <el-button
            size="large"
            @click="handleClose"
            class="close-button"
            round
          >
            关闭
          </el-button>
          
          <el-button
            :type="isPlaying ? 'warning' : 'primary'"
            size="large"
            @click="togglePlayPause"
            class="control-button"
            :class="{ 'pause-button': isPlaying }"
            round
          >
            <el-icon v-if="isPlaying" class="button-icon"><VideoPause /></el-icon>
            <el-icon v-else class="button-icon"><VideoPlay /></el-icon>
            {{ isPlaying ? '暂停听写' : (isPaused ? '继续听写' : '开始听写') }}
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 听写结果弹窗框组件 -->
  <DictationWordListDialog
    v-model:visible="wordListDialogVisible"
    :words="words"
    :playback-mode="playbackMode"
  />
</template>

<script>
import { ref, defineComponent, watch, computed, onMounted, onBeforeUnmount } from 'vue'
import { ElMessageBox } from 'element-plus'
import { Microphone, VideoPlay, VideoPause, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { getDictationSetting, saveDictationSetting, saveWordRecord } from '../../api/word'
import { useStore } from 'vuex'
import DictationWordListDialog from './DictationWordListDialog.vue'

export default defineComponent({
  name: 'DictationDialog',
  components: {
    Microphone,
    VideoPlay,
    VideoPause,
    ArrowLeft,
    ArrowRight,
    DictationWordListDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedWords: {
      type: Array,
      required: true
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const store = useStore()
    const dialogVisible = ref(props.visible)
    const userId = computed(() => store.getters.userId)

    // 设置默认值，后续会从API获取
    const dictationInterval = ref()
    const playbackMode = ref()
    const announcementMethod = ref()
    const settingsLoading = ref(false)
    const settingsSaving = ref(false)
    const isSettingFromApi = ref(false) // 标记是否是从API加载设置

    const isPlaying = ref(false)
    const isPaused = ref(false)
    const isSpeaking = ref(false)
    const currentIndex = ref(0)

    // 听写记录是否已保存（每次打开弹窗只保存一次）
    const recordSaved = ref(false)

    let dictationTimer = null

    // 音频播放元素
    let audioElement = null

    // 从API获取用户听写设置
    const fetchUserSettings = async () => {
      if (!userId.value) return

      settingsLoading.value = true
      try {
        const response = await getDictationSetting(userId.value)
        if (response && response.data) {
          // 标记为从API加载设置，避免触发保存
          isSettingFromApi.value = true

          // 设置听写间隔、播报模式和播报方式
          dictationInterval.value = response.data.timeInterval
          playbackMode.value = response.data.mode
          announcementMethod.value = response.data.ways

          // 延迟重置标记，确保watch不会触发保存
          setTimeout(() => {
            isSettingFromApi.value = false
          }, 100)
        }
      } catch (error) {
        console.error('获取听写设置失败:', error)
      } finally {
        settingsLoading.value = false
      }
    }

    // 保存用户听写设置
    const saveUserSettings = async () => {
      if (!userId.value) return

      settingsSaving.value = true
      try {
        const data = {
          uid: userId.value,
          timeInterval: dictationInterval.value,
          mode: playbackMode.value,
          ways: announcementMethod.value
        }

        await saveDictationSetting(data)
      } catch (error) {
        console.error('保存听写设置失败:', error)
      } finally {
        settingsSaving.value = false
      }
    }

    // 原始单词列表
    const originalWords = computed(() => props.selectedWords)

    // 随机排序的单词列表
    const shuffleWords = () => {
      const shuffled = [...originalWords.value]
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        // 交换元素
        ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
      }
      return shuffled
    }

    // 根据播放模式获取单词列表
    const words = computed(() => {
      if (playbackMode.value === 'random') {
        return shuffleWords()
      }
      return originalWords.value
    })

    const initSpeech = () => {
      try {
        // 创建音频元素
        audioElement = new Audio()

        // 设置音频事件监听
        audioElement.onended = () => {
          console.log('Audio playback ended')
          isSpeaking.value = false
          handleSpeechEnd()
        }

        audioElement.onerror = (event) => {
          console.error('Audio playback error:', event)
          handleSpeechEnd()
        }

        return true
      } catch (error) {
        console.error('初始化音频播放失败:', error)
        return false
      }
    }

    const speakWord = () => {
      if (!audioElement) {
        console.error('音频播放元素未初始化')
        setTimeout(handleSpeechEnd, dictationInterval.value * 1000)
        return false
      }

      try {
        // 停止当前正在播放的音频并清除所有事件监听器
        audioElement.pause()
        audioElement.currentTime = 0
        audioElement.onended = null
        audioElement.onerror = null
      } catch (e) {
        console.error('停止音频播放失败:', e)
      }

      const currentWord = words.value[currentIndex.value]
      let audioUrl = null

      // 根据播报方式选择播放的音频
      switch (announcementMethod.value) {
        case 'ch':
          audioUrl = currentWord.chVoicePath
          if (!audioUrl) {
            console.error('单词没有中文音频路径:', currentWord.chinese)
            setTimeout(handleSpeechEnd, dictationInterval.value * 1000)
            return false
          }
          break
        case 'en':
          audioUrl = currentWord.enVoicePath
          if (!audioUrl) {
            console.error('单词没有英文音频路径:', currentWord.english)
            setTimeout(handleSpeechEnd, dictationInterval.value * 1000)
            return false
          }
          break
        case 'ch_en':
          // 对于中英文播报，先播放中文，然后在onended事件中播放英文
          audioUrl = currentWord.chVoicePath
          if (!audioUrl) {
            console.error('单词没有中文音频路径:', currentWord.chinese)

            // 尝试播放英文
            audioUrl = currentWord.enVoicePath
            if (!audioUrl) {
              console.error('单词没有英文音频路径:', currentWord.english)
              setTimeout(handleSpeechEnd, dictationInterval.value * 1000)
              return false
            }
          }
          break
        default:
          audioUrl = currentWord.chVoicePath
          if (!audioUrl) {
            console.error('单词没有中文音频路径:', currentWord.chinese)
            setTimeout(handleSpeechEnd, dictationInterval.value * 1000)
            return false
          }
      }

      console.log('播放单词音频:', announcementMethod.value === 'en' ? currentWord.english : currentWord.chinese, audioUrl)
      isSpeaking.value = true

      try {
        // 设置音频源
        audioElement.src = audioUrl

        // 对于中英文播报模式，需要特殊处理
        if (announcementMethod.value === 'ch_en') {
          // 设置新的onended处理函数，先播放中文，然后播放英文
          if (audioUrl === currentWord.chVoicePath && currentWord.enVoicePath) {
            const playEnglish = () => {
              // 移除当前的事件监听器
              audioElement.onended = null
              audioElement.onerror = null
              
              // 设置英文音频
              audioElement.src = currentWord.enVoicePath
              
              // 设置英文播放完成的处理函数
              audioElement.onended = () => {
                isSpeaking.value = false
                handleSpeechEnd()
              }
              
              // 设置错误处理
              audioElement.onerror = (event) => {
                console.error('播放英文音频失败:', event)
                isSpeaking.value = false
                handleSpeechEnd()
              }
              
              // 播放英文
              audioElement.play().catch(error => {
                console.error('播放英文音频失败:', error)
                isSpeaking.value = false
                handleSpeechEnd()
              })
            }
            
            audioElement.onended = playEnglish
          } else {
            // 如果没有中文音频，直接设置基本的onended处理
            audioElement.onended = () => {
              isSpeaking.value = false
              handleSpeechEnd()
            }
          }
        } else {
          // 非中英文播报模式，直接设置基本的onended处理
          audioElement.onended = () => {
            isSpeaking.value = false
            handleSpeechEnd()
          }
        }

        // 设置音频播放错误事件
        audioElement.onerror = (event) => {
          console.error('音频播放错误:', event)
          isSpeaking.value = false
          handleSpeechEnd()
        }

        // 播放音频
        audioElement.play().catch(error => {
          console.error('音频播放失败:', error)
          isSpeaking.value = false
          setTimeout(handleSpeechEnd, dictationInterval.value * 1000)
        })

        return true
      } catch (error) {
        console.error('播放单词音频失败:', error)
        isSpeaking.value = false
        setTimeout(handleSpeechEnd, dictationInterval.value * 1000)
        return false
      }
    }

    // 保存听写记录
    const saveDictationRecord = async () => {
      // 如果记录已经保存过，则不再保存
      if (recordSaved.value) {
        console.log('听写记录已保存，本次会话不再重复保存')
        return
      }

      if (!userId.value || words.value.length === 0) {
        console.error('保存听写记录失败: 缺少必要参数')
        return
      }

      try {
        // 获取所有单词的章节ID，去重
        const periodIds = [...new Set(words.value.map(word => word.periodId))].join(',')

        // 构建单词列表
        const itemList = words.value.map(word => ({
          wordId: word.id,
          periodId: word.periodId
        }))

        // 构建请求数据
        const data = {
          uid: userId.value,
          bookId: words.value[0].bookId,
          periodIds: periodIds,
          type: 'write',  // 听写类型为write
          itemList: itemList
        }

        // 调用API保存听写记录
        await saveWordRecord(data)
        recordSaved.value = true
      } catch (error) {
        console.error('保存听写记录错误:', error)
      }
    }

    const startDictation = () => {
      if (!initSpeech()) {
        console.error('初始化音频播放失败')
        return
      }

      // 保存听写记录（只在开始听写时调用，不在继续听写时调用）
      if (!isPaused.value) {
        saveDictationRecord()
      }

      isPlaying.value = true
      isPaused.value = false

      if (currentIndex.value >= words.value.length) {
        currentIndex.value = 0
      }

      speakCurrentWord()
    }

    const speakCurrentWord = () => {
      if (currentIndex.value < words.value.length && isPlaying.value) {

        setTimeout(() => {
          if (!isPlaying.value) return

          const success = speakWord()

          if (!success) {
            setTimeout(moveToNextWord, dictationInterval.value * 1000)
          }
        }, 100)
      }
    }

    const handleSpeechEnd = () => {
      if (!isPlaying.value) return

      if (dictationTimer) {
        clearTimeout(dictationTimer)
        dictationTimer = null
      }

      dictationTimer = setTimeout(() => {
        moveToNextWord()
      }, dictationInterval.value * 1000)
    }

    // 单词列表显示对话框可见性
    const wordListDialogVisible = ref(false)

    // 显示单词听写完成确认对话框
    const showCompletionConfirmation = () => {
      ElMessageBox.confirm(
        '单词听写完毕，是否展示全部听写单词？',
        '听写完成',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      ).then(() => {
        // 用户确认，显示单词列表
        wordListDialogVisible.value = true
      }).catch(() => {
        // 用户取消，不做任何操作
      })
    }

    const moveToNextWord = () => {
      if (!isPlaying.value) return

      if (dictationTimer) {
        clearTimeout(dictationTimer)
        dictationTimer = null
      }

      currentIndex.value++

      if (currentIndex.value >= words.value.length) {
        isPlaying.value = false
        isPaused.value = false
        isSpeaking.value = false
        currentIndex.value = 0

        // 听写完成，显示确认对话框
        showCompletionConfirmation()
      } else {
        console.log('Speaking next word')
        speakCurrentWord()
      }
    }

    const pauseDictation = () => {
      isPlaying.value = false
      isPaused.value = true
      isSpeaking.value = false

      if (dictationTimer) {
        clearTimeout(dictationTimer)
        dictationTimer = null
      }

      if (audioElement) {
        try {
          audioElement.pause()
          audioElement.currentTime = 0
        } catch (error) {
          console.error('暂停音频播放失败:', error)
        }
      }

      const highestTimeoutId = setTimeout(() => {}, 0)
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i)
      }
    }

    const togglePlayPause = () => {
      if (isPlaying.value) {
        pauseDictation()
      } else {
        startDictation()
      }
    }

    // 是否是第一个单词
    const isFirstWord = computed(() => {
      return currentIndex.value === 0
    })

    // 是否是最后一个单词
    const isLastWord = computed(() => {
      return currentIndex.value === words.value.length - 1
    })

    const prevWord = () => {
      // 如果是第一个单词，不做处理
      if (isFirstWord.value) return

      currentIndex.value--

      if (isPlaying.value) {
        if (dictationTimer) {
          clearTimeout(dictationTimer)
          dictationTimer = null
        }

        if (audioElement) {
          try {
            audioElement.pause()
            audioElement.currentTime = 0
          } catch (error) {
            console.error('停止音频播放失败:', error)
          }
        }

        speakCurrentWord()
      }
    }

    const nextWord = () => {
      // 如果是最后一个单词，不做处理
      if (isLastWord.value) return

      currentIndex.value++

      if (isPlaying.value) {
        if (dictationTimer) {
          clearTimeout(dictationTimer)
          dictationTimer = null
        }

        if (audioElement) {
          try {
            audioElement.pause()
            audioElement.currentTime = 0
          } catch (error) {
            console.error('停止音频播放失败:', error)
          }
        }

        speakCurrentWord()
      }
    }

    const handleClose = () => {
      if (isPlaying.value) {
        ElMessageBox.confirm(
          '听写进行中，确定要退出吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          cleanupAndClose()
        }).catch(() => {
        })
      } else {
        cleanupAndClose()
      }
    }

    const cleanupAndClose = () => {
      if (isPlaying.value) {
        pauseDictation()
      }

      isPlaying.value = false
      isPaused.value = false
      isSpeaking.value = false  // Ensure speaking state is reset
      currentIndex.value = 0

      // 重置记录保存状态，以便下次打开弹窗时可以再次保存
      recordSaved.value = false

      if (dictationTimer) {
        clearTimeout(dictationTimer)
        dictationTimer = null
      }

      // 清理音频元素
      if (audioElement) {
        try {
          audioElement.pause()
          audioElement.currentTime = 0
          audioElement.src = ''
          audioElement.onended = null
          audioElement.onerror = null
        } catch (error) {
          console.error('清理音频元素失败:', error)
        }
      }

      // 移除事件监听器
      window.removeEventListener('popstate', handlePopState)

      dialogVisible.value = false
      emit('update:visible', false)
    }

    // 处理浏览器回退按钮
    const handlePopState = (event) => {
      if (dialogVisible.value) {
        // 如果对话框是打开的，执行关闭操作
        handleClose()

        // 阻止默认的回退行为，因为我们已经处理了关闭操作
        event.preventDefault()
        return false
      }
    }

    // 监听对话框可见性变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal

      if (newVal) {
        currentIndex.value = 0
        isPlaying.value = false
        isPaused.value = false
        isSpeaking.value = false

        // 重置记录保存状态，以便可以再次保存
        recordSaved.value = false

        initSpeech()

        // 当对话框打开时，获取用户设置
        fetchUserSettings()

        // 添加历史记录状态，用于处理浏览器回退
        window.history.pushState({ dictationDialog: true }, '')

        // 添加popstate事件监听器
        window.addEventListener('popstate', handlePopState)
      } else {
        // 对话框关闭时，移除事件监听器
        window.removeEventListener('popstate', handlePopState)
      }
    })

    watch(() => dialogVisible.value, (newVal) => {
      emit('update:visible', newVal)
    })

    // 监听听写间隔变化，保存设置
    watch(dictationInterval, () => {
      if (dialogVisible.value && userId.value && !isSettingFromApi.value) {
        // 只有在不是从API加载设置时才保存
        saveUserSettings()
      }
    })

    // 处理播报模式变化
    const handlePlaybackModeChange = (newMode) => {
      // 如果新模式与当前模式相同，不做处理
      if (playbackMode.value === newMode) {
        console.log('播报模式未变化，不做处理')
        return
      }

      // 如果是第一个单词，直接切换模式
      if (isFirstWord.value) {
        playbackMode.value = newMode
        // 如果正在播放，则暂停并重新开始
        if (isPlaying.value) {
          pauseDictation()
          setTimeout(() => {
            startDictation()
          }, 100)
        }
        return
      }

      // 如果不是第一个单词，弹出确认对话框
      const modeText = newMode === 'random' ? '随机播报' : '顺序播报'
      ElMessageBox.confirm(
        `是否确定切换到${modeText}模式，重新听写单词？`,
        '切换播报模式',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 用户确认切换
        playbackMode.value = newMode
        // 重置到第一个单词
        currentIndex.value = 0
        // 如果正在播放，则暂停并重新开始
        if (isPlaying.value) {
          pauseDictation()
          setTimeout(() => {
            startDictation()
          }, 100)
        }
      }).catch(() => {
        // 用户取消，恢复原来的模式
      })
    }

    // 监听播报模式变化，保存设置
    watch(playbackMode, () => {
      if (dialogVisible.value && userId.value && !isSettingFromApi.value) {
        // 只有在不是从API加载设置时才保存
        saveUserSettings()
      }
    })

    // 监听播报方式变化，保存设置
    watch(announcementMethod, () => {
      if (dialogVisible.value && userId.value && !isSettingFromApi.value) {
        // 只有在不是从API加载设置时才保存
        saveUserSettings()
      }
    })

    // 组件挂载时初始化
    onMounted(() => {
      // 如果对话框已经可见，则获取用户设置
      if (dialogVisible.value) {
        fetchUserSettings()
      }
    })

    // 组件卸载前清理
    onBeforeUnmount(() => {
      // 确保移除事件监听器
      window.removeEventListener('popstate', handlePopState)
    })

    return {
      dialogVisible,
      dictationInterval,
      playbackMode,
      announcementMethod,
      isPlaying,
      isPaused,
      isSpeaking,
      currentIndex,
      words,
      isFirstWord,
      isLastWord,
      settingsLoading,
      settingsSaving,
      togglePlayPause,
      handleClose,
      prevWord,
      nextWord,
      handlePlaybackModeChange,
      wordListDialogVisible,
      isSettingFromApi
    }
  }
})
</script>

<style scoped>
.dictation-dialog {
  display: flex;
  flex-direction: column;
}

.dictation-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 6rem);
  width: 100%;
  box-sizing: border-box;
}

.word-card-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex: 1;
  padding-top: 2rem;
}

.dictation-card {
  background-color: #f0f7ff;
  background-image: linear-gradient(to bottom, #f8fbff, #e8f4ff);
  border-radius: 1.4rem;
  padding: 4rem;
  border: 1px solid #e0e9f6;
  transition: all 0.3s ease;
  width: 80%;
  height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  margin: 0;
  box-sizing: border-box;
}

.dictation-card.active {
  background-color: #ecf5ff;
  background-image: linear-gradient(to bottom, #f0f7ff, #d8ebff);
  border: 2px solid #409eff;
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.25);
  z-index: 1;
}

.word-content {
  text-align: center;
  width: 100%;
  padding: 1rem;
}

.word-chinese {
  font-size: 5.5rem;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  letter-spacing: 1px;
}

.playing-indicator {
  position: absolute;
  top: 200px;
  right: 60px;
  color: #409eff;
  animation: pulse 1.5s infinite;
}

.speaker-icon {
  font-size: 6rem;
}

.navigation-button {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.navigation-button:hover {
  background-color: #e0e9f6;
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.2);
}

.navigation-button .el-icon {
  font-size: 2.5rem;
  color: #409eff;
}

.navigation-button.disabled {
  background-color: #f5f7fa;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.6;
}

.navigation-button.disabled:hover {
  transform: none;
  background-color: #f5f7fa;
  box-shadow: none;
}

.navigation-button.disabled .el-icon {
  color: #c0c4cc;
}

.bottom-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2rem;
  padding: 1rem;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

:deep(.setting-item .el-radio-button__inner){
  font-size: 14px !important;
}

.setting-label {
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.close-button {
  padding: 2rem 2rem !important;
  height: 3.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  background-color: #909399;
  border-color: #909399;
  color: white;
}

:deep(.close-button span){
  font-size: 1.5rem;
}

.close-button:hover {
  background-color: #7d7d7d;
  border-color: #7d7d7d;
  color: white;
}

.control-button {
  padding: 2rem 1rem !important;
  height: 3.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

:deep(.control-button span) {
  font-size: 1.5rem;
  font-weight: 600;
}

.control-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.35);
}

.pause-button {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.25);
}

.pause-button:hover {
  background-color: #d39e2c;
  border-color: #d39e2c;
  color: white;
  box-shadow: 0 6px 15px rgba(230, 162, 60, 0.35);
}

.button-icon {
  margin-right: 0.5rem;
  font-size: 1.6rem;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

/* Custom dialog header styles */
.custom-dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* Override Element Plus dialog styles */
:deep(.el-dialog) {
  margin: 0 !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: auto;
  padding: 0;
}

/* Responsive design for different screen sizes */
@media (max-width: 1200px) {
  .dictation-card {
    width: 70%;
    height: 400px;
  }
  
  .word-chinese {
    font-size: 5rem;
  }
}

@media (max-width: 992px) {
  .dictation-card {
    width: 80%;
    height: 380px;
  }
  
  .word-chinese {
    font-size: 4.5rem;
  }

  .bottom-controls {
    gap: 2rem;
    padding: 1.2rem 1.5rem;
  }
  
  .setting-label {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .dictation-card {
    width: 90%;
    height: 320px;
  }

  .bottom-controls {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .setting-item {
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
    text-align: center;
  }
  
  .control-buttons {
    flex-direction: row;
    gap: 1rem;
  }

  .dialog-title {
    font-size: 2.5rem;
  }
  
  .setting-label {
    font-size: 1rem;
  }
  
  .control-button {
    padding: 0 1.5rem;
    height: 3rem;
    font-size: 1rem;
  }
  
  .close-button {
    padding: 0 1.5rem;
    height: 3rem;
    font-size: 1rem;
  }

  .dictation-card{
    padding: 20px;
    font-size:20px;
  }
  .word-chinese{
    font-size: 3.5rem;
  }
  .navigation-button{
    width: 3rem;
    height:3rem;
  }
  
  .el-radio-button--large .el-radio-button__inner {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  :deep(.setting-item .el-radio-button__inner) {
    font-size: 12px !important;
  }
}
</style>

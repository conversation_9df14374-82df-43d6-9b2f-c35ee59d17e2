<template>
  <div class="verse-integrated-container">
    <component 
      :is="currentComponent" 
      @switch-component="handleSwitchComponent"
    ></component>
    <!-- 悬浮球 -->
    <FloatingBall />
  </div>
</template>

<script setup>
import { ref, markRaw, defineAsyncComponent } from 'vue';
import FloatingBall from '../components/common/FloatingBall.vue'

// 异步加载子组件以提高性能
const Verse = defineAsyncComponent(() => import('./Verse.vue'));
const VerseQuestion = defineAsyncComponent(() => import('./VerseQuestion.vue'));

// 使用markRaw避免不必要的响应式包装
const components = {
  'verse': markRaw(Verse),
  'verseQuestion': markRaw(VerseQuestion)
};

// 当前显示的组件
const currentComponent = ref(components.verse);

// 处理组件切换
const handleSwitchComponent = (componentName) => {
  if (componentName === 'verse' || componentName === 'verseQuestion') {
    currentComponent.value = components[componentName];
  }
};
</script>

<style scoped>
.verse-integrated-container {
  width: 100%;
  height: 100%;
}
</style> 
# 数字人语音合成功能说明

## 功能概述

为数字人增加了实时语音合成和播放功能，实现了大模型流式输出文本的实时语音合成，让数字人能够"说话"并同步嘴部动画。

## 主要特性

### 1. 实时文本分割
- 支持按句号、感叹号、问号、分号、省略号、换行符、逗号等标点符号分割文本
- 流式处理：边接收文本边分割，无需等待完整回答
- 智能缓冲：未完成的句子保留在缓冲区，等待更多内容

### 2. 音频合成队列管理
- 串行合成：确保音频按顺序合成，避免并发请求
- 队列播放：音频按顺序播放，保持语音连贯性
- 错误处理：合成失败时自动跳过，不影响后续播放

### 3. 数字人嘴部动画同步
- 语音会话开始：数字人视频开始连续播放（从0.1秒开始）
- 语音会话进行：数字人视频保持连续播放，不会重复重启
- 语音会话结束：数字人视频停止并回到闭嘴状态（第0帧）
- 智能控制：基于整个语音会话而非单个音频片段控制动画

### 4. 完整的生命周期管理
- 对话开始：清空之前的音频队列
- 对话进行：实时分割文本并合成音频
- 对话结束：处理剩余文本缓冲区
- 异常处理：网络错误或用户中断时正确清理资源

### 5. 音频播放互斥控制
- 古诗朗读与数字人语音不能同时播放
- 古诗朗读时：数字人语音识别按钮禁用，显示灰色不可点击状态
- 数字人说话时：古诗朗读按钮禁用，防止冲突
- 智能切换：开始一种音频播放时自动停止另一种

### 6. 新对话即时响应
- 每次开始语音识别时立即开启新对话
- 立即停止所有音频播放（古诗朗读 + 数字人音频）
- 中断正在进行的SSE连接和音频合成队列
- 数字人立即回到第一帧（闭嘴状态）
- 防止正在合成的音频影响新的语音识别
- 确保用户可以立即开始新的交互

## 技术实现

### 核心文件修改

#### 1. `src/utils/tools.ts`
新增了三个工具函数：

```typescript
// 分割文本为句子
splitTextToSentences(text: string): { sentences: string[]; remaining: string }

// 检查文本是否包含完整句子
hasCompleteSentence(text: string): boolean

// 强制分割文本（用于处理剩余文本）
forceSplitText(text: string): string[]
```

#### 2. `src/components/verse/VerseDetailDialog.vue`
主要修改：

- 新增音频合成相关状态变量
- 实现音频合成队列管理函数
- 修改数字人对话流程，集成语音合成
- 完善资源清理和错误处理
- 添加音频播放互斥控制逻辑

#### 3. `src/views/components/ChatBar.vue`
主要修改：

- 新增 `disabled` 属性支持
- 添加禁用状态的视觉样式
- 禁用时阻止语音识别启动

### 关键函数说明

#### `enqueueSpeechSegment(sentence)`
- 将文本句子加入音频合成队列
- 调用 `getTTSLL` API 合成音频
- 合成成功后加入播放队列

#### `processAudioQueue()`
- 处理音频播放队列
- 连续播放音频片段
- 处理播放错误和队列循环

#### `startSpeechSession()` / `endSpeechSession()`
- 管理语音会话的生命周期
- 控制数字人嘴部动画的开始和结束
- 确保动画连续性，避免重复重启

#### `checkSpeechSessionEnd()`
- 检查语音会话是否应该结束
- 当队列为空且无音频播放时结束会话

#### `shouldDisableVoiceRecognition` / `shouldDisableAudioButton`
- 计算属性，判断是否应该禁用相应功能
- 实现古诗朗读与数字人语音的互斥控制

#### `handleTalkStart()`
- 语音识别开始时的处理函数
- 移除了古诗朗读音频的禁用检查，允许随时开始新对话
- 立即停止所有音频播放（古诗朗读 + 数字人音频）
- 中断当前SSE连接，重置音频合成队列
- 重置对话状态，确保新对话的即时响应

#### `stopAllAudio()` 增强
- 新增重置音频合成队列逻辑
- 防止正在合成的音频继续播放
- 确保完全停止所有音频相关活动

#### `enqueueSpeechSegment()` 保护
- 新增语音会话状态检查
- 只有在会话活跃时才播放合成的音频
- 防止已停止会话的音频意外播放

#### 会话ID机制
- `currentSessionId`: 当前会话的唯一标识符
- `startSpeechSession()`: 为新会话分配唯一ID
- `handleTalkStart()`: 新对话开始时立即生成新会话ID
- 双重检查：合成前和合成后都验证会话ID有效性

#### 队列监控机制
- `monitorAudioQueue()`: 检测队列卡住情况
- `startQueueMonitor()`: 开始定期监控（每2秒检查一次）
- `stopQueueMonitor()`: 停止监控
- 自动恢复：检测到队列卡住时自动重启播放

#### 增强的错误处理
- 详细的日志记录：追踪音频播放的每个阶段
- 异步操作保护：使用`setTimeout`避免竞态条件
- 音频状态监听：`onloadstart`、`oncanplay`等事件
- 延迟检查：给音频合成留出时间，避免过早结束会话

#### `stopAllAudio()`
- 停止当前播放的音频
- 清空所有队列
- 重置数字人状态

## API 接口

使用现有的 `getTTSLL` 接口进行音频合成：

```javascript
getTTSLL({
  text: stripMarkdown(sentence),           // 去除markdown格式的文本
  speakerId: props.verseDetail.authorInfo.speakerCode,  // 说话人ID
  speed: props.verseDetail.authorInfo.speed,            // 语速
  volume: props.verseDetail.authorInfo.volume,          // 音量
  pitch: props.verseDetail.authorInfo.pitch             // 音调
})
```

响应格式：
```javascript
{
  data: {
    fileUrl: "https://example.com/audio.mp3"  // 音频文件URL
  }
}
```

## 使用流程

1. **用户发起对话**：通过语音或文本输入与数字人交互
2. **即时响应准备**：
   - 立即停止当前音频播放
   - 中断正在进行的SSE连接
   - 数字人回到闭嘴状态
   - 重置所有相关状态
3. **开始语音合成**：系统准备新的对话会话
4. **流式文本处理**：
   - 接收大模型流式输出
   - 实时分割完整句子
   - 将句子加入合成队列
5. **音频合成播放**：
   - 串行合成音频文件
   - 按顺序连续播放音频
   - 保持数字人嘴部动画连续性
6. **对话结束**：处理剩余文本，完成最后的音频播放

## 配置要求

### 数字人资源要求
- 数字人视频文件：第0帧为闭嘴状态，后续帧为张嘴状态
- 视频格式：支持HTML5 video标签的格式（如MP4）
- 视频比例：建议9:16竖屏比例

### 音频合成配置
确保 `verseDetail.authorInfo` 包含以下配置：
- `speakerCode`: 说话人代码
- `speed`: 语速（如 "1.0"）
- `volume`: 音量（如 "1.0"）
- `pitch`: 音调（如 "1.0"）

## 错误处理

### 网络错误
- 音频合成失败时自动跳过该句子
- 不影响后续句子的合成和播放
- 在控制台输出错误日志

### 播放错误
- 音频播放失败时自动播放下一个
- 确保数字人动画状态正确
- 队列继续处理

### 用户中断
- 用户关闭对话框时停止所有音频
- 用户发起新对话时清空之前的队列
- SSE连接中断时正确清理资源

## 性能优化

### 内存管理
- 音频播放完成后自动释放Audio对象
- 及时清理队列，避免内存泄漏
- 合理控制并发合成数量

### 网络优化
- 串行合成避免并发请求过多
- 合成失败时不重试，避免阻塞队列
- 使用Promise队列确保顺序执行

## 测试建议

1. **功能测试**：
   - 测试不同长度的文本输入
   - 测试包含各种标点符号的文本
   - 测试网络异常情况

2. **性能测试**：
   - 测试长时间对话的内存使用
   - 测试快速连续对话的处理能力
   - 测试音频播放的流畅性

3. **用户体验测试**：
   - 测试数字人动画的同步性
   - 测试语音的自然度和连贯性
   - 测试各种中断场景的处理

## 问题修复记录

### 重复合成问题修复
**问题描述**：在流式文本处理中，会出现已经合成的句子的最后一个字再次被合成的情况。

**原因分析**：
1. `splitTextToSentences`函数使用正则表达式匹配时，可能导致标点符号被重复处理
2. `forceSplitText`函数使用`split(/([。！？；…\n])/)`会将分割符作为单独元素返回

**修复方案**：
1. 重写`splitTextToSentences`函数，使用字符遍历方式精确控制分割逻辑
2. 重写`forceSplitText`函数，避免分割符被单独返回
3. 确保每个句子只被合成一次，不会出现重复

**修复效果**：
- 修复前：`"彼时朝廷腐败，"` → 后续还会合成 `"败，"`
- 修复后：`"彼时朝廷腐败，"` → 不再重复合成

### 语音识别期间音频干扰问题修复
**问题描述**：开始新的语音识别时，之前正在播放或合成的音频没有完全停止，会干扰语音识别效果。

**原因分析**：
1. 语音识别开始时只停止了数字人音频，没有停止古诗朗读音频
2. 音频合成队列没有被重置，正在合成的音频仍会播放
3. 存在禁用检查阻止了用户在音频播放时开始语音识别

**修复方案**：
1. 移除语音识别的禁用逻辑，允许随时开始新对话
2. 在`handleTalkStart`中停止所有类型的音频播放
3. 重置音频合成队列，防止正在合成的音频继续播放
4. 在音频合成函数中添加会话状态检查

**修复效果**：
- 修复前：开始语音识别时可能仍有音频播放，影响识别效果
- 修复后：开始语音识别时立即停止所有音频，确保识别环境清净

### 多轮对话音频合成冲突问题优化
**问题描述**：用户发起第二轮对话时，第一轮对话的音频合成过程仍在继续，导致新旧对话的音频混合播放。

**原因分析**：
1. 音频合成是异步过程，即使重置队列，正在进行的合成请求仍会完成
2. 缺乏机制来标识和取消过期会话的音频合成任务
3. 合成完成的音频会被添加到新会话的播放队列中

**优化方案**：
1. 引入会话ID机制，为每个对话会话分配唯一标识
2. 在音频合成前后都检查会话ID的有效性
3. 过期会话的音频合成结果直接丢弃，不加入播放队列
4. 新对话开始时立即生成新的会话ID

**优化效果**：
- 优化前：第一轮对话合成的音频可能在第二轮对话中播放
- 优化后：每轮对话的音频完全独立，不会相互干扰

### 音频播放偶现停止问题修复
**问题描述**：音频合成正常，但播放过程中偶尔会停止，后续音频也不再播放。

**原因分析**：
1. 异步操作的竞态条件导致队列处理中断
2. 音频播放失败后的错误处理可能导致状态不一致
3. `checkSpeechSessionEnd`过早触发，结束了仍在进行的会话
4. 缺乏队列卡住的检测和恢复机制

**修复方案**：
1. 添加详细的日志记录，便于问题追踪
2. 使用`setTimeout`避免同步调用导致的竞态条件
3. 增强`checkSpeechSessionEnd`的检查条件
4. 实现队列监控机制，定期检测并恢复卡住的队列
5. 添加音频加载状态监听，提供更好的错误处理

**修复效果**：
- 修复前：音频播放偶尔停止，需要手动重新开始对话
- 修复后：音频播放更稳定，具备自动恢复能力

### 深度代码审查和稳定性增强
**问题描述**：尽管已经修复了明显的问题，但仍有极低概率的音频不播放情况。

**深度分析发现的潜在问题**：
1. **状态更新时序问题**：在事件回调中立即设置状态可能导致竞态条件
2. **Promise链中断风险**：音频合成失败可能导致整个Promise链中断
3. **异常处理不完整**：某些边界情况下的异常没有被正确捕获
4. **资源清理不彻底**：组件卸载时可能有残留的定时器和音频对象

**全面加固方案**：
1. **状态更新保护**：在setTimeout内部设置状态，避免立即更新导致的竞态
2. **Promise链保护**：为synthQueue添加catch处理，确保链不会中断
3. **多层错误处理**：为processAudioQueue添加try-catch包装
4. **强化队列监控**：增强monitorAudioQueue的恢复能力
5. **组件生命周期管理**：添加onUnmounted清理所有资源
6. **详细日志追踪**：为每个关键步骤添加详细日志

**加固效果**：
- 加固前：极低概率的播放停止，难以追踪原因
- 加固后：多层保护机制，即使出现异常也能自动恢复

## 未来扩展

1. **音频缓存**：缓存常用句子的音频，提高响应速度
2. **语音情感**：根据文本内容调整语音情感参数
3. **多语言支持**：支持不同语言的语音合成
4. **音频质量优化**：支持更高质量的音频格式
5. **实时语音识别**：集成语音识别，实现更自然的对话体验

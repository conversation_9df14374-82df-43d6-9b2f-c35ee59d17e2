<template>
  <div class="math-gpt-container" ref="containerRef">
    <!-- 添加历史对话入口 -->
    <agent-history-entry
      category="SolveAI"
      @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">AI解题</h1>
      <p class="subtitle">智能分析题目要点，一键获取详细解题步骤与答案，助你轻松攻克各科难题</p>
    </div>

    <div class="input-container">
    <!-- 引导提示 -->
      <div class="upload-tip-container">
        <div class="upload-tip-content">
          <div class="tip-row">
            <div class="tip-text">
              <h3 class="tip-title">
                点击 图标 <el-icon class="inline-icon"><Picture /></el-icon> 上传题目图片，或点击 图标 
                <svg class="inline-qr-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                  <path d="M4 7v-1a2 2 0 0 1 2 -2h2" />
                  <path d="M4 17v1a2 2 0 0 0 2 2h2" />
                  <path d="M16 4h2a2 2 0 0 1 2 2v1" />
                  <path d="M16 20h2a2 2 0 0 0 2 -2v-1" />
                  <line x1="5" y1="12" x2="19" y2="12" />
                </svg>
                微信扫码传图，一键智能解题
              </h3>
            </div>
            <div class="h5-qrcode-container">
              <el-image
                class="h5-qrcode"
                :src="h5QRCodeImage"
                :preview-src-list="[h5QRCodeImage]"
                fit="contain"
              />
              <p class="qrcode-tip">扫码体验移动端AI解题</p>
            </div>
          </div>
        </div>
      </div>
      <div class="input-box">
        <el-input
          v-model="problem"
          type="textarea"
          :rows="4"
          placeholder="输入您的题目或上传题目图片，支持语数英、物理化学等各学科，秒出详细解答"
          class="problem-input"
          @keydown.enter.exact.prevent="handleSolve"
        />

        <div class="action-buttons">
          <div class="left-actions">
            <el-upload
                class="upload-btn"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleFileChange"
                accept="image/*"
              >
                <el-button class="action-btn" size="large" circle>
                  <el-icon><Picture /></el-icon>
                </el-button>
              </el-upload>
            
            <el-popover
              placement="top-end"
              :width="220"
              trigger="manual"
              v-model:visible="showWeChatQRCode"
              :teleported="false"
              popper-class="wechat-qr-popover"
            >
              <template #reference>
                <el-button @click="handleWeChatUploadClick" class="action-btn" size="large" circle>
                    <svg width="45" height="45" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                      <path d="M4 7v-1a2 2 0 0 1 2 -2h2" />
                      <path d="M4 17v1a2 2 0 0 0 2 2h2" />
                      <path d="M16 4h2a2 2 0 0 1 2 2v1" />
                      <path d="M16 20h2a2 2 0 0 0 2 -2v-1" />
                      <line x1="5" y1="12" x2="19" y2="12" />
                    </svg>
                  </el-button>
              </template>
              <div style="text-align: center;">
                <img v-if="weChatQRCodeUrl" :src="weChatQRCodeUrl" alt="WeChat QR Code" style="width: 180px; height: 180px;" />
                <p style="font-size: 16px; color: #666; margin: 0;">微信扫码，上传图片</p>
              </div>
            </el-popover>

            <div v-if="imageList.length > 0" class="image-preview-container">
              <div class="image-item">
                <el-image
                  class="inline-image-preview"
                  :src="imageList[0].url"
                  :preview-src-list="[imageList[0].url]"
                  :initial-index="0"
                  fit="contain"
                  @show="isPreviewing = true"
                  @close="isPreviewing = false"
                />
                <div class="delete-icon" @click.stop="removeImage(0)">
                  <el-icon><Close /></el-icon>
                </div>
              </div>
            </div>
          </div>

          <div class="right-actions">
            <el-button
              v-if="!loading"
              :type="loading ? 'warning' : 'primary'"
              size="large"
              circle
              :disabled="!problem && imageList.length === 0"
              class="submit-btn"
              v-click-throttle="handleSolve"
            >
              {{ loading ? '中止解题' : '开始解题' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <ai-loading :visible="showAiLoading" />
    <div v-if="reasoningData || solutionData" class="combined-solution-container">
      <div class="solution-content">
          <div v-if="reasoningData" class="reasoning-section">
            <div class="section-title">思考过程：</div>
            <div class="reasoning-text" v-html="renderedMarkdown(reasoningData)"></div>
          </div>

          <div v-if="solutionData" class="solution-section">
            <div class="section-title-container">
              <div class="section-title" v-if="solutionData">解题结果：</div>
              <div class="action-icons">
                <el-icon class="copy-icon" style="cursor: pointer;" @click="copySolution"><CopyDocument /></el-icon>
                <!-- 添加保存按钮 -->
                <agent-save-button
                  v-if="!loading && solutionData"
                  category="SolveAI"
                  :user-question="savePrompt()"
                  :model-answer="solutionData"
                  :chat-service="chatService"
                  :already-saved="alreadySaved"
                  @save-success="alreadySaved = true"
                />
              </div>
            </div>
            <div class="solution-text" v-html="renderedMarkdown(solutionData)"></div>

            <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;" v-if="loading && solutionData">
              <el-button
                type="warning"
                size="large"
                v-click-throttle="handleSolve"
                round
              >中止解题</el-button>
            </div>

            <!-- AI提示信息 -->
            <div v-if="!loading && solutionData" class="ai-disclaimer">
              <el-icon class="disclaimer-icon"><Warning /></el-icon>
              <span>解题结果由AI智能生成，仅供学习参考，建议结合课本知识进行验证</span>
            </div>
          </div>
        </div>
    </div>
    <!-- 悬浮球 -->
    <FloatingBall />
  </div>
</template>

<script>
import { ref, computed, nextTick, watch, onMounted, onUnmounted, inject } from 'vue'
import { Picture, Position, Close, CopyDocument, Warning } from '@element-plus/icons-vue'
import { ElMessage, ElLoading } from 'element-plus'
import MarkdownIt from "markdown-it";
import mk from "@iktakahiro/markdown-it-katex";
import {fetchEventSource} from '@microsoft/fetch-event-source';
import { useStore } from 'vuex'
import QRCode from 'qrcode'
import AgentSaveButton from '@/components/common/AgentSaveButton.vue'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import { getScanId, getImageByScanId } from '@/api/other'
import h5QRCodeImage from '@/assets/h5_qrcode.png'
import AiLoading from '@/components/common/AiLoading.vue'
import FloatingBall from '../components/common/FloatingBall.vue'
import { getAiQuestionPrompt } from '@/api/home'

export default {
  name: 'MathGPT',
  components: {
    Picture,
    Position,
    Close,
    CopyDocument,
    Warning,
    AgentSaveButton,
    AgentHistoryEntry,
    AiLoading,
    FloatingBall
  },
  setup() {
    const trackingService = inject('trackingService');
    const store = useStore()
    const problem = ref('')
    const reasoningData = ref(null)
    const solutionData = ref(null)
    const imageList = ref([])
    const loading = ref(false)
    const reader = ref(null) // 添加reader引用以便可以中止操作
    const containerRef = ref(null) // 容器引用，用于滚动操作
    const chatService = ref(null) // 聊天服务实例
    const alreadySaved = ref(false) // 是否已保存当前对话
    const showWeChatQRCode = ref(false)
    const weChatQRCodeUrl = ref('')
    const isPreviewing = ref(false) // 用于跟踪预览状态
    const roleSystem = ref('') // 用于存储从API获取的系统提示
    let scanId = null
    let pollingInterval = null

    const showAiLoading = computed(() => loading.value && !solutionData.value)

    // 阻止滚轮事件的函数
    const preventWheel = (e) => {
      e.preventDefault();
      e.stopPropagation();
    };

    // 监听预览状态变化
    watch(isPreviewing, (newVal) => {
      if (newVal) {
        document.body.style.overflow = 'hidden';
        // 添加滚轮事件监听，阻止页面滚动
        document.addEventListener('wheel', preventWheel, { passive: false });
        document.addEventListener('touchmove', preventWheel, { passive: false });
      } else {
        document.body.style.overflow = '';
        // 移除滚轮事件监听
        document.removeEventListener('wheel', preventWheel);
        document.removeEventListener('touchmove', preventWheel);
      }
    });

    // 将文件转换为base64
    const fileToBase64 = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          const base64String = reader.result
          resolve(base64String)
        }
        reader.onerror = error => reject(error)
      })
    }

    const processWeChatImage = async (base64Data) => {
      // 替换模式，先清空
      imageList.value = []

      const loadingInstance = ElLoading.service({
        text: `处理图片中...`,
        background: 'rgba(255, 255, 255, 0.7)'
      })
      
      try {
        // 生成文件名
        const fileName = `wechat-upload-${Date.now()}.png`

        // 将base64转换为blob用于预览
        const base64String = base64Data.startsWith('data:') ? base64Data : `data:image/png;base64,${base64Data}`
        const response = await fetch(base64String)
        const blob = await response.blob()
        const objectURL = URL.createObjectURL(blob)

        // 添加到图片列表
        imageList.value.push({
          name: fileName,
          url: objectURL, // 用于预览
          base64: base64String // 用于发送给API
        })
      } catch (error) {
        console.error('Error processing WeChat image:', error)
        ElMessage.error('处理微信上传的图片失败')
      } finally {
        loadingInstance.close()
      }
    }

    const startPollingForImage = () => {
      if (pollingInterval) {
        clearInterval(pollingInterval)
      }

      pollingInterval = setInterval(async () => {
        try {
          const res = await getImageByScanId(scanId)
          if (res.data && res.data.imageBase64) {
            showWeChatQRCode.value = false
            await processWeChatImage(res.data.imageBase64)
          }
        } catch (error) {
          console.log('Polling for image...')
        }
      }, 2000) // Poll every 2 seconds
    }

    const stopPolling = () => {
      if (pollingInterval) {
        console.log('Stopping polling.')
        clearInterval(pollingInterval)
        pollingInterval = null
      }
    }

    const handleWeChatUploadClick = async () => {
      // if popover is already visible, close it
      if (showWeChatQRCode.value) {
        showWeChatQRCode.value = false
        return
      }

      // 如果没有二维码，重新生成
      if (!weChatQRCodeUrl.value) {
        await preGenerateQRCode()
      }

      // Show the popover and start polling
      showWeChatQRCode.value = true
      startPollingForImage()
    }

    // 处理文件选择
    const handleFileChange = async (file) => {
      const currentFile = file;
      
      // 替换模式，先清空
      imageList.value = []

      // 显示加载状态
      const loadingInstance = ElLoading.service({
        text: `处理图片中...`,
        background: 'rgba(255, 255, 255, 0.7)'
      })

      try {
        console.log('处理文件：', currentFile.name)

        // 将文件转换为base64
        const base64Data = await fileToBase64(currentFile.raw)

        // 添加新图片
        imageList.value.push({
          name: currentFile.name,
          url: URL.createObjectURL(currentFile.raw), // 用于预览
          base64: base64Data // 用于发送给API
        })
      } catch (error) {
        console.error('处理图片错误:', error)
      } finally {
        loadingInstance.close()
      }
    }

    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };

    // 监听推理数据和解决方案数据变化，触发滚动
    watch([reasoningData, solutionData], () => {
      scrollToBottom();
    });

    const handleSolve = async () => {
      if (!problem.value && imageList.value.length === 0) {
        ElMessage.warning('请输入问题或上传图片')
        return
      }

      // 如果当前正在加载，则执行中止操作
      if (loading.value && reader.value) {
        await cancelStream()
        return
      }

      reasoningData.value = null
      solutionData.value = null
      loading.value = true
      alreadySaved.value = false // 重置保存状态

      try {
        // let message = [{"role": "system", "content": roleSystem.value}]
        let message = []
        if (imageList.value.length > 0) {
          message.push({
            "role": "user",
            "content": [
              ...imageList.value.map(img => ({
                "type": "image_url",
                "image_url": {
                  "url": img.base64
                }
              })),
              {
                "type": "text",
                "text": problem.value  || '请解答图片中的题目'
              }
           ]
         })
        } else {
          message.push({
            "role": "user",
            "content": problem.value
          })
        }

        let requestBody = {
          messages: message,
          stream: true,
          modelType: 'open',
          formType: 'solve_ai'

        }

        // 创建AbortController用于取消请求
        const controller = new AbortController();
        reader.value = controller; // 将 controller 赋值给 reader 用于取消

        let accumulatedText = '';
        let reasoningText = '';

        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`

        // 使用fetchEventSource代替fetch
        fetchEventSource(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal, // 传递 signal 用于取消
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
               loading.value = false; // 出错时停止加载状态
               reader.value = null;
              throw new Error(`HTTP error! status: ${response.status}`)
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              loading.value = false;
              reader.value = null;
              throw new Error(msg.data);
            }

            const data = msg.data;

            try {
              const jsonData = JSON.parse(data);

              if (jsonData.error) {
                 loading.value = false;
                 reader.value = null;
                 return;
              }

              if (jsonData.choices && jsonData.choices[0]) {
                const delta_reasoning = jsonData.choices[0].message.reasoning_content;
                const delta_content = jsonData.choices[0].message.content;

                if (delta_reasoning) {
                  reasoningText += delta_reasoning;
                  reasoningData.value = reasoningText;
                  scrollToBottom();
                }

                if (delta_content) {
                  accumulatedText += delta_content;
                  solutionData.value = accumulatedText;
                  scrollToBottom();
                }
              }
            } catch (error) {
              console.error('JSON parse error:', error);
            }
          },
          async onclose() {
            console.log('连接关闭');
            if (loading.value) {
              loading.value = false;
              reader.value = null;
              await nextTick();
              scrollToBottom();
            }
          },
          onerror(err) {
            console.error('Stream error:', err);
            // 检查错误是否是用户取消操作
            if (err.name !== 'AbortError') {
            } else {
               console.log('Stream aborted by user.');
            }
            loading.value = false;
            reader.value = null;
            // 不需要再次抛出错误，onerror 本身处理错误情况
          }
        });

      } catch (error) {
        // 这个 catch 块现在主要捕获 fetchEventSource 启动前的错误
        // 或者 onopen 中抛出的同步错误
        console.error(error);
        loading.value = false;
        reader.value = null; // 确保清理
      }
      trackingService.trackEvent('SolveAI')
      // 不需要 finally 块了，因为 loading 和 reader 的状态在 onclose 和 onerror 中处理
    }

    const savePrompt = () => {
      let prompt = ``
      let imageContent = []
      if (imageList.value.length > 0) {
        imageList.value.forEach((img) => {
          imageContent.push({
            type: 'image_url',
            content: img.base64
          })
        })
        if (problem.value) {
          imageContent.push({
            type: 'text',
            content: problem.value
          })
        }
        prompt = JSON.stringify(imageContent)
      } else {
        prompt = `${problem.value}`
      }
      return prompt
    }

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          // reader.value 现在是 AbortController
          reader.value.abort('用户取消了操作');
          console.log('Abort signal sent.');
          // AbortController 没有 close 方法，abort() 会触发 onerror
        } catch (error) {
          console.error('取消流式输出错误:', error);
        } finally {
          // 立即重置状态，确保按钮恢复
          loading.value = false;
          reader.value = null; // 清理 controller 引用
        }
      }
    };

    // 从图片列表中删除图片
    const removeImage = (index) => {
      imageList.value.splice(index, 1);
    };

    const renderedMarkdown = (markdownContent) => {
      if (!markdownContent) return

      // 预处理Markdown内容，将LaTeX格式的公式转换为KaTeX兼容格式
      let processedContent = markdownContent

      // 转换 \[ \] 格式为 $$ $$，并移除首尾多余空格
      processedContent = processedContent.replace(/\\\[\s*([\s\S]*?)\s*\\\]/g, (match, p1) => `$$${p1.trim()}$$`)

      // 转换 \( \) 格式为 $ $，并移除首尾多余空格
      processedContent = processedContent.replace(/\\\(\s*([\s\S]*?)\s*\\\)/g, (match, p1) => `$${p1.trim()}$`)

      // 保存并移除所有 $$ 包裹的内容，防止处理双美元符号内容
      const doubleDollarBlocks = [];
      processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, (match, content) => {
        doubleDollarBlocks.push(match);
        return `__DOUBLE_DOLLAR_PLACEHOLDER_${doubleDollarBlocks.length - 1}__`;
      });

      // 使用更健壮的正则表达式处理所有单美元符号的情况
      // 捕获成对的$，并处理其中可能存在的各种空格情况
      processedContent = processedContent.replace(/(\$)\s*([^\$]+?)\s*(\$)/g, (match, start, content, end) => {
        // 对内容进行处理，只移除首尾空格，保留内部空格
        return `${start}${content.trim()}${end}`;
      });

      // 恢复所有 $$ 包裹的内容
      doubleDollarBlocks.forEach((block, index) => {
        processedContent = processedContent.replace(`__DOUBLE_DOLLAR_PLACEHOLDER_${index}__`, block);
      });

      const md = new MarkdownIt();
      md.use(mk);
      const htmlContent = md.render(processedContent);
      return htmlContent;
    }

    // 复制解题结果
    const copySolution = () => {
      if (!solutionData.value) return

      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div')
      tempElement.innerHTML = renderedMarkdown(solutionData.value)

      // 获取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText

      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = textContent
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 尝试复制到剪贴板
        document.execCommand('copy')
        ElMessage.success('解题结果已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('SolveAI', 'AI解题会话')
          console.log('AI解题重新初始化完成')
        }
      } catch (error) {
        console.error('AI解题重新初始化错误:', error)
      }
    }

    // 预生成二维码
    const preGenerateQRCode = async () => {
      try {
        const res = await getScanId()
        if (res.data && res.data.scanId) {
          scanId = res.data.scanId
          let url = `https://junheng.chinatiye.cn/aiH5/#/?scanId=${scanId}`
          if (import.meta.env.VITE_NODE_ENV === 'production') {
            url = `https://ai.chinatiye.cn/wechat/#/?scanId=${scanId}`
          }
          weChatQRCodeUrl.value = await QRCode.toDataURL(url)
        }
      } catch (error) {
        console.error('预生成二维码失败:', error)
      }
    }

    // 加载系统提示词
    const loadSystemPrompt = async () => {
      try {
        const res = await getAiQuestionPrompt('solve_prompt')
        if (res.data) {
          roleSystem.value = res.data.dictValue || ''
        }
      } catch (error) {
        console.error('加载AI解题系统提示时出错:', error)
      }
    }

    // 初始化聊天服务
    onMounted(async () => {
      try {
        // await loadSystemPrompt() // 加载系统提示词
        
        chatService.value = new AgentChatService(store)
        await chatService.value.initialize('SolveAI', 'AI解题会话')
        console.log('AI解题初始化完成')
        
        // 预生成二维码
        await preGenerateQRCode()
      } catch (error) {
        console.error('初始化错误:', error)
      }
    })

    // 页面销毁时清理轮询和事件监听
    onUnmounted(() => {
      console.log('Component unmounting, stopping polling.')
      stopPolling()
      // 清理滚轮事件监听器，防止内存泄漏
      document.removeEventListener('wheel', preventWheel);
      document.removeEventListener('touchmove', preventWheel);
      // 恢复页面滚动
      document.body.style.overflow = '';
    })

    return {
      problem,
      reasoningData,
      solutionData,
      handleSolve,
      handleFileChange,
      imageList,
      removeImage,
      loading,
      renderedMarkdown,
      containerRef,
      copySolution,
      CopyDocument,
      chatService,
      savePrompt,
      alreadySaved,
      reinitializeChatSession,
      showWeChatQRCode,
      weChatQRCodeUrl,
      handleWeChatUploadClick,
      isPreviewing,
      h5QRCodeImage,
      showAiLoading
    }
  }
}
</script>

<style scoped>
.math-gpt-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "✨";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.input-container {
  margin: 3rem 0;
}

.input-box {
  border: 2px solid #e0e0ff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 0 25px rgba(0, 0, 255, 0.08);
  background-color: #fff;
  position: relative;
}

.problem-input {
  margin-bottom: 1.5rem;
}

/* 自定义 textarea 样式 */
:deep(.el-textarea__inner) {
  padding: 1.2rem;
  line-height: 1.7;
  border-radius: 1rem;
  border-color: #e0e0ff;
}

:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
  border-color: #409eff;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
}

/* 按钮基础样式 */
.action-btn {
  margin-right: 1rem;
}

:deep(.action-btn.el-button) {
  width: 80px;
  height: 80px;
}

:deep(.action-btn .el-icon) {
  font-size: 45px;
}

/* 提交按钮样式 */
:deep(.submit-btn.el-button) {
  width: 80px;
  height: 80px;
}

/* 新的合并解决方案容器样式 */
.combined-solution-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.solution-content {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.reasoning-section, .solution-section {
  text-align: left;
  padding: 2rem;
  position: relative;
}

.reasoning-section {
  background-color: #f9faff;
  border-bottom: 1px solid rgba(0, 0, 255, 0.05);
}

.solution-section {
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.reasoning-text {
  font-size: 1rem;
  line-height: 1.8;
  color: #444;
  white-space: pre-line;
  margin: 0;
  padding: 1.2rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

.solution-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.2rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

/* 图片上传相关样式 */
.upload-btn {
  display: inline-block;
}

.image-preview-container {
  display: flex;
  margin-left: 15px;
  align-items: center;
  overflow-x: auto;
  max-width: calc(100% - 80px);
  scrollbar-width: none; /* Firefox */
  padding: 2px;
}

.image-preview-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.image-item {
  position: relative;
  margin-right: 10px;
  flex-shrink: 0;
}

.delete-icon {
  position: absolute;
  top: 0px;
  right: -8px;
  background-color: #f56c6c;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1;
  font-size: 18px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.delete-icon:hover {
  transform: scale(1.1);
  background-color: #e74c3c;
}

.inline-image-preview {
  border-radius: 8px;
  overflow: hidden;
  max-height: 200px;
  max-width: 500px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.left-actions {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow-x: auto;
  max-width: 80%;
  scrollbar-width: none; /* Firefox */
}

.left-actions::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

/* 响应式适配 */
@media (max-width: 768px) {
  .math-gpt-container {
    width: 100%;
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .input-box {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  :deep(.el-textarea__inner) {
    padding: 0.8rem;
  }

  :deep(.action-btn.el-button) {
    width: 48px;
    height: 48px;
  }

  :deep(.submit-btn.el-button) {
    width: 56px;
    height: 56px;
  }

  .reasoning-section, .solution-section {
    padding: 1.5rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0;
  }

  .reasoning-text {
    font-size: 0.85rem;
  }

  .solution-text {
    font-size: 1rem;
  }

  .image-preview-container {
    max-width: calc(100% - 60px);
  }

  .inline-image-preview {
    max-width: 100px;
    max-height: 100px;
  }

  .copy-btn {
    width: 34px;
    height: 34px;
  }

  .copy-icon {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .input-container {
    margin: 2rem 0;
  }

  .input-box {
    padding: 1rem;
  }

  :deep(.action-btn.el-button) {
    width: 44px;
    height: 44px;
    margin-right: 0.5rem;
  }

  :deep(.submit-btn.el-button) {
    width: 50px;
    height: 50px;
  }

  .reasoning-section, .solution-section {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .reasoning-text, .solution-text {
    padding: 1rem;
  }

  .inline-image-preview {
    max-width: 90px;
    max-height: 90px;
    margin-right: 6px;
  }

  .image-preview-container {
    max-width: calc(100% - 55px);
  }

  .copy-btn {
    width: 30px;
    height: 30px;
  }

  .copy-icon {
    font-size: 0.9rem;
  }

  .tip-row {
    gap: 0.8rem;
  }

  .h5-qrcode {
    width: 60px;
    height: 60px;
  }

  .qrcode-tip {
    font-size: 0.65rem;
  }
}

/* 上传提示区域样式 */
.upload-tip-container {
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f0fd 100%);
  border-radius: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 150, 0.08);
  border: 1px dashed #c0d8ff;
  text-align: center;
}

.upload-tip-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
}

.tip-icon {
  font-size: 2.5rem;
  margin-bottom: 0.8rem;
  animation: pulse 2s infinite ease-in-out;
}

.inline-icon {
  margin: 0 2px;
  font-size: 24px;
  color: #333;
  transform: translate(-2px, 5px);
}

.inline-qr-icon {
  margin: 0 4px;
  color: #333;
  transform: translate(-2px, 5px);
  display: inline-block;
}

.tip-title {
  font-size: 1.3rem;
  color: #1e88e5;
  font-weight: 600;
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.25s ease;
  background-color: rgba(44, 134, 255, 0.05);
  border: 1px solid rgba(44, 134, 255, 0.2);
  color: #2c86ff;
  width: 38px;
  height: 38px;
  padding: 0;
}

.copy-btn:hover {
  transform: translateY(-2px);
  background-color: rgba(44, 134, 255, 0.15);
  box-shadow: 0 4px 12px rgba(44, 134, 255, 0.2);
  color: #2c86ff;
}

.copy-btn:active {
  transform: translateY(0);
}

.copy-icon {
  font-size: 1.1rem;
}

.action-icons {
  display: flex;
  align-items: center;
}

.copy-icon {
  margin-right: 10px;
  font-size: 1.1rem;
}

/* AI提示信息样式 */
.ai-disclaimer {
  margin-top: 1.5rem;
  padding: 0.8rem 1.2rem;
  background-color: #fefce8;
  border: 1px solid #fde047;
  border-radius: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #a16207;
  box-shadow: 0 2px 8px rgba(252, 211, 77, 0.15);
}

.disclaimer-icon {
  color: #f59e0b;
  font-size: 1rem;
  flex-shrink: 0;
}

.ai-disclaimer span {
  line-height: 1.4;
}

/* 微信二维码弹窗样式 */
:deep(.wechat-qr-popover) {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
  border: 1px solid #e6f0fd !important;
}

:deep(.wechat-qr-popover .el-popover__arrow) {
  border-bottom-color: #e6f0fd !important;
}

/* 提示行布局 */
.tip-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.tip-text {
  flex: 1;
}

/* h5二维码容器样式 */
.h5-qrcode-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.h5-qrcode {
  width: 100px;
  height: 100px;
  cursor: pointer;
}

.qrcode-tip {
  font-size: 1rem;
  color: #666;
  margin: 0;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ai-disclaimer {
    font-size: 0.85rem;
    padding: 0.7rem 1rem;
    margin-top: 1.2rem;
  }
  
  .disclaimer-icon {
    font-size: 0.9rem;
  }

  :deep(.wechat-qr-popover) {
    width: 200px !important;
  }

  .tip-row {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .h5-qrcode {
    width: 70px;
    height: 70px;
  }

  .qrcode-tip {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .ai-disclaimer {
    font-size: 0.8rem;
    padding: 0.6rem 0.8rem;
    margin-top: 1rem;
  }

  :deep(.wechat-qr-popover) {
    width: 180px !important;
  }
}
</style>

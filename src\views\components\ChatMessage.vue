<template>
  <div :class="['chat-message', from]">
    <div class="bubble">{{ text }}</div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  text: string
  from: 'user' | 'ai'
}>()
</script>

<style scoped lang="scss">
.chat-message {
  margin-bottom: 10px;
  display: flex;

  &.user {
    justify-content: flex-end;

    .bubble {
      background-color: #111;
      color: #fff;
    }
  }

  &.ai {
    justify-content: flex-start;

    .bubble {
      background-color: #5c3d2e;
      color: #fdf6e3;
    }
  }

  .bubble {
    max-width: 75%;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 14px;
    line-height: 1.5;
  }
}
</style>

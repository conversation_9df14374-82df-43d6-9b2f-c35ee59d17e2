import request from './request'

// 古诗筛选条件 
export function getVerseFilter() {
  return request({
    url: '/junhengai/chat/gushiType/selectList',
    method: 'get'
  })
}

// 查询古诗
export function getVerseList(params) {
  return request({
    url: '/junhengai/chat/gushi/list',
    method: 'get',
    params
  })
}

// 查询古诗详情
export function getVerseDetail(id) {
  return request({
    url: `/junhengai/chat/gushi/${id}`,
    method: 'get'
  })
}

// 智能体访问日志统计
export function countLog(data) {
  return request({
    url: '/junhengai/ai/agentLog/log',
    method: 'post',
    data
  })
}

// 按天统计
export function countLogDay(params) {
  return request({
    url: '/junhengai/ai/agentLog/v1/getMonthResult',
    method: 'GET',
    params
  })
}

// 指定月份智能体分类统计
export function countLogClass(params) {
  return request({
    url: '/junhengai/ai/agentLog/v1/getMonthCategoryResult',
    method: 'GET',
    params
  })
}

export const generateOutlineV2 = (data) => {
  return request({
    url: '/junhengai/ppt/v2/generateOutline',
    method: 'POST',
    data
  })
}

export const createApiToken = (params) => {
  return request({
    url: '/junhengai/ppt/getApiToken',
    method: 'GET',
    params
  })
}

// AI数字人讲解
export function getTTSLL(params) {
  return request({
    url: '/junhengai/chat/voice/v1/genTts',
    method: 'POST',
    data: params
  })
}

// 琅琅语音余额查询
export function getTTSLLBalance(params) {
  return request({
    url: '/junhengai/chat/voice/v1/longGetInfo',
    method: 'GET',
    params
  })
}

// AI数字人列表
export function getAiTakls(data) {
  return request({
    url: '/junhengai/ai/digital/v1/qryList',
    method: 'post',
    data
  })
}

// 查询指定数字人
export function getAiTalkDetail(data) {
  return request({
    url: `/junhengai/ai/digital/v1/qryCategory`,
    method: 'post',
    data
  })
}

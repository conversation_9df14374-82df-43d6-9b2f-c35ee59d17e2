<template>
  <div class="verse-question-container" ref="containerRef">
    <agent-history-entry
      category="PoetAI"
      @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">古诗词出题助手</h1>
      <p class="subtitle">基于AI技术，快速生成个性化古诗词题目，支持多种题型，适合不同学段学生练习</p>

      <div class="page-navigation">
        <el-button type="primary" class="navigate-button" @click="switchToVersePage" size="large">
          前往AI古诗词
        </el-button>
      </div>
    </div>

    <div class="form-container">
      <el-form :model="formData" label-width="120px" class="question-form" size="large">
        <el-form-item label="朝代">
          <div class="tag-buttons-container">
            <el-button
              :type="formData.dynasty === '不限' ? 'primary' : ''"
              @click="selectOption('dynasty', '不限')"
              round
            >不限</el-button>
            <el-button
              v-for="dynasty in verseFilter.dynasties"
              :key="dynasty"
              :type="formData.dynasty === dynasty ? 'primary' : ''"
              @click="selectOption('dynasty', dynasty)"
              round
            >{{ dynasty }}</el-button>
          </div>
        </el-form-item>

        <el-form-item label="作者">
          <div class="tag-buttons-container">
            <el-button
              :type="formData.author === '不限' ? 'primary' : ''"
              @click="selectOption('author', '不限')"
              round
            >不限</el-button>
            <el-button
              v-for="author in verseFilter.authors"
              :key="author"
              :type="formData.author === author ? 'primary' : ''"
              @click="selectOption('author', author)"
              round
            >{{ author }}</el-button>
          </div>
        </el-form-item>

        <el-form-item label="主题">
          <div class="tag-buttons-container">
            <el-button
              :type="formData.theme === '不限' ? 'primary' : ''"
              @click="selectOption('theme', '不限')"
              round
            >不限</el-button>
            <el-button
              v-for="theme in verseFilter.themes"
              :key="theme"
              :type="formData.theme === theme ? 'primary' : ''"
              @click="selectOption('theme', theme)"
              round
            >{{ theme }}</el-button>
          </div>
        </el-form-item>

        <el-form-item label="学段/年级">
          <div class="tag-buttons-container">
            <el-button
              :type="formData.period === '不限' ? 'primary' : ''"
              @click="selectOption('period', '不限')"
              round
            >不限</el-button>
            <el-button
              v-for="period in verseFilter.periods"
              :key="period"
              :type="formData.period === period ? 'primary' : ''"
              @click="selectOption('period', period)"
              round
            >{{ period }}</el-button>
            <template v-if="formData.period !== '不限'">
              <el-button
                v-for="grade in filteredGrades"
                :key="grade"
                :type="formData.grade === grade ? 'primary' : ''"
                @click="selectOption('grade', grade)"
                round
              >{{ grade }}</el-button>
              <el-button
                v-for="term in verseFilter.terms"
                :key="term"
                :type="formData.term === term ? 'primary' : ''"
                @click="selectOption('term', term)"
                round
             >{{ term }}</el-button>
            </template>
          </div>
        </el-form-item>

        <el-form-item label="题目数量">
          <el-input-number
            v-model="formData.questionCount"
            :min="1"
            :max="10"
            :step="1"
            controls-position="right"
            class="question-count-input"
          />
        </el-form-item>

        <el-form-item label="题型">
          <div class="tag-buttons-container">
            <el-button
              :type="formData.questionType === '不限' ? 'primary' : ''"
              @click="formData.questionType = '不限'"
              round
            >不限</el-button>
            <el-button
              :type="formData.questionType === '填空题' ? 'primary' : ''"
              @click="formData.questionType = '填空题'"
              round
            >填空题</el-button>
            <el-button
              :type="formData.questionType === '选择题' ? 'primary' : ''"
              @click="formData.questionType = '选择题'"
              round
            >选择题</el-button>
            <el-button
              :type="formData.questionType === '问答题' ? 'primary' : ''"
              @click="formData.questionType = '问答题'"
              round
            >问答题</el-button>
          </div>
        </el-form-item>

        <div style="display: flex; justify-content: center; width: 100%;" v-if="!loading">
          <el-button
            :type="loading ? 'warning' : 'primary'"
            size="large"
            v-click-throttle="handleGenerate"
            round
          >
            {{ loading ? '中止生成' : '生成题目' }}
          </el-button>
        </div>
      </el-form>
    </div>

    <ai-loading :visible="showAiLoading" />

    <div v-if="questionResult" class="result-container">
      <div class="result-content">
        <div class="section-title-container">
          <div class="section-title">生成的题目：</div>
          <div class="action-icons">
            <el-icon style="cursor: pointer;" @click="copyComment"><CopyDocument /></el-icon>
            <agent-save-button
              v-if="!loading && questionResult"
              category="PoetAI"
              :user-question="savePrompt()"
              :model-answer="questionResult"
              :chat-service="chatService"
              :already-saved="alreadySaved"
              @save-success="alreadySaved = true"
            />
          </div>
        </div>
        <div class="question-text" v-html="renderedMarkdown(questionResult)"></div>
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;" v-if="loading && questionResult">
          <el-button
            type="warning"
            size="large"
            v-click-throttle="handleGenerate"
            round
          >中止生成 </el-button>
        </div>
         <div  v-if="!loading && questionResult" style="display: flex; justify-content: center; width: 100%; margin-top: 2rem;">
           <el-button 
             :type="answerLoading ? 'warning' : 'primary'" 
             size="large" 
             round
             v-click-throttle="handleGenerateAnswer"
           >
             {{ answerLoading ? '中止生成' : '题目解析' }}
           </el-button>
         </div>

         <div style="margin-top: 2rem;" v-if="answerResult" class="question-text" v-html="renderedMarkdown(answerResult)"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick, watch, onMounted ,inject} from 'vue'
import { CopyDocument } from '@element-plus/icons-vue'
import { ElMessage, ElLoading } from 'element-plus'
import MarkdownIt from "markdown-it"
import { getVerseFilter } from '@/api/verse'
import {fetchEventSource} from '@microsoft/fetch-event-source';
import { useStore } from 'vuex'
import AgentSaveButton from '@/components/common/AgentSaveButton.vue'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import AiLoading from '@/components/common/AiLoading.vue'

export default {
  name: 'VerseQuestion',
  components: {
    CopyDocument,
    AgentSaveButton,
    AgentHistoryEntry,
    AiLoading
  },
  emits: ['switch-component'],
  setup(props, { emit }) {
    const store = useStore()
    const trackingService = inject('trackingService');
    const formData = ref({
      author: '不限',
      dynasty: '不限',
      theme: '不限',
      period: '不限',
      grade: '',
      term: '',
      questionCount: 3,
      questionType: '不限'
    })

    const questionResult = ref(null)
    const loading = ref(false)
    const reader = ref(null) // 添加reader引用以便可以中止操作
    const containerRef = ref(null) // 容器引用，用于滚动操作
    const chatService = ref(null) // 聊天服务实例
    const alreadySaved = ref(false) // 是否已保存当前对话

    // 答案相关状态
    const answerResult = ref(null)
    const answerLoading = ref(false)
    const answerReader = ref(null) // 答案生成的reader引用
    const answerAlreadySaved = ref(false) // 答案是否已保存

    // 古诗筛选条件
    const verseFilter = ref({
      dynasties: [],
      authors: [],
      authorDynasty: [],
      themes: [],
      periods: [],
      gradeMap: new Map(),
      terms: []
    })

    const showAiLoading = computed(() => loading.value && !questionResult.value)

    // 根据当前选择的学段筛选年级
    const filteredGrades = computed(() => {
      if (formData.value.period === '不限') {
        return []
      }
      return verseFilter.value.gradeMap?.get(formData.value.period) || []
    })

    // 获取筛选条件
    const fetchFilters = async () => {
      try {
        const loadingInstance = ElLoading.service({
          target: '.form-container',
          text: '加载筛选项...'
        })

        const res = await getVerseFilter()
        if (res && res.code === 200) {
          const filterData = res.data || []

          // 处理筛选数据
          filterData.forEach(item => {
            switch(item.type) {
              case 'dynasty':
                verseFilter.value.dynasties = item.options.map(opt => opt.text)
                break;
              case 'author':
                verseFilter.value.authors = item.options.map(opt => opt.text)
                verseFilter.value.authorDynasty = item.options
                break;
              case 'theme':
                verseFilter.value.themes = item.options
                break;
              case 'period':
                // 处理学段和年级
                const periods = []
                const gradeMap = new Map()

                // 提取小学和初中
                item.options.forEach(opt => {
                  if (opt.text !== '不限') {
                    periods.push(opt.text)

                    // 提取各学段下的年级
                    if (opt.options) {
                      const grades = opt.options
                        .filter(grade => grade.text !== '不限')
                        .map(grade => grade.text)
                      gradeMap.set(opt.text, grades)
                    }
                  }
                })

                verseFilter.value.periods = periods
                verseFilter.value.gradeMap = gradeMap
                break;
              case 'gradeStage':
                verseFilter.value.terms = item.options
                  .filter(opt => opt.text !== '不限')
                  .map(opt => opt.text)
                break;
            }
          })
        }

        loadingInstance.close()
      } catch (error) {
        console.error('获取筛选条件错误:', error)
      }
    }

    // 选择筛选选项
    const selectOption = (field, value) => {
      formData.value[field] = value

      if (field === 'dynasty') {
        if (value === '不限') {
          verseFilter.value.authors = verseFilter.value.authorDynasty.map(opt => opt.text)
        } else {
          verseFilter.value.authors = verseFilter.value.authorDynasty.filter((a) => a.label === value).map(opt => opt.text)
        }
      }

      // 当切换学段时处理年级选择
      if (field === 'period') {
        if (value === '不限') {
          // 如果选择了"不限"，则清空年级值
          formData.value.grade = '';
          // 同时清空学期
          formData.value.term = '';
        } else {
          // 如果选择了特定学段且有对应年级，则默认选中第一个年级
          const grades = verseFilter.value.gradeMap?.get(value) || [];
          if (grades.length > 0) {
            formData.value.grade = grades[0];
          } else {
            formData.value.grade = '';
          }
          // 清空学期
          formData.value.term = '';
        }
      }

      // 当切换年级时重置学期
      if (field === 'grade') {
        formData.value.term = '';
      }
    }

    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    }

    // 监听结果变化，触发滚动
    watch(questionResult, () => {
      scrollToBottom()
    })

    // 监听答案结果变化，触发滚动
    watch(answerResult, () => {
      scrollToBottom()
    })

    // Markdown渲染
    const renderedMarkdown = (markdownContent) => {
      if (!markdownContent) return

      const md = new MarkdownIt()
      const htmlContent = md.render(markdownContent)
      return htmlContent
    }

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('PoetAI', '古诗词出题助手会话')
          console.log('古诗词出题助手重新初始化完成')
        }
      } catch (error) {
        console.error('古诗词出题助手重新初始化错误:', error)
      }
    }

    // 初始化聊天服务和获取筛选条件
    onMounted(async () => {
      try {
        // 并行执行初始化任务
        const initChatPromise = async () => {
          chatService.value = new AgentChatService(store)
          return await chatService.value.initialize('PoetAI', '古诗词出题助手会话')
        }

        // 并行加载筛选条件和初始化聊天服务
        await Promise.all([
          fetchFilters(),
          initChatPromise()
        ])

        console.log('古诗词出题助手初始化完成')
      } catch (error) {
        console.error('初始化错误:', error)
      }
    })

    // 构建提示词方法
    const buildPrompt = () => {
      let prompt = `请根据以下条件，生成${formData.value.questionCount}道古诗词题目。`

      // 添加朝代限制
      if (formData.value.dynasty !== '不限') {
        prompt += `诗词限定在${formData.value.dynasty}，`
      }

      // 添加作者限制
      if (formData.value.author !== '不限') {
        prompt += `作者为${formData.value.author}，`
      }

      // 添加主题限制
      if (formData.value.theme !== '不限') {
        prompt += `主题为${formData.value.theme}，`
      }

      // 添加学段限制
      if (formData.value.period !== '不限') {
        prompt += `学段为${formData.value.period}，`
      }

      // 添加年级限制
      if (formData.value.grade) {
        prompt += `适合${formData.value.grade}学生，`
      }

      // 添加学期限制
      if (formData.value.term) {
        prompt += `适用于${formData.value.term}，`
      }

      // 添加题型限制
      if (formData.value.questionType) {
        if (formData.value.questionType === '不限') {
          prompt += `题型不限，`
        } else {
          prompt += `题型为${formData.value.questionType}，`
        }
      }

      prompt += `请确保题目质量高，符合教学需求。`

      return prompt
    }

    const savePrompt = () => {
      let prompt = ``

      // 添加朝代限制
      if (formData.value.dynasty !== '不限') {
        prompt += `朝代：${formData.value.dynasty}，`
      }

      // 添加作者限制
      if (formData.value.author !== '不限') {
        prompt += `作者：${formData.value.author}，`
      }

      // 添加主题限制
      if (formData.value.theme !== '不限') {
        prompt += `主题：${formData.value.theme}，`
      }

      // 添加学段限制
      if (formData.value.period !== '不限') {
        prompt += `学段：${formData.value.period}，`
      }

      // 添加年级限制
      if (formData.value.grade) {
        prompt += `年级：${formData.value.grade}，`
      }

      // 添加学期限制
      if (formData.value.term) {
        prompt += `学期：${formData.value.term}，`
      }

      if (formData.value.questionCount) {
        prompt += `题目数量：${formData.value.questionCount}，`
      }

      // 添加题型限制
      if (formData.value.questionType) {
        if (formData.value.questionType === '不限') {
          prompt += `题型不限，`
        } else {
          prompt += `题型：${formData.value.questionType}，`
        }
      }

      return prompt
    }

    // 生成题目
    const handleGenerate = async () => {
      // 如果当前正在加载，则执行中止操作
      if (loading.value && reader.value) {
        await cancelStream()
        return
      }

      questionResult.value = null
      answerResult.value = null // 重置答案结果
      loading.value = true
      alreadySaved.value = false // 重置保存状态
      answerAlreadySaved.value = false // 重置答案保存状态

      try {
        // 构建提示词
        let prompt = buildPrompt()

        // 构建请求体
        let message = []
        message.push({
          "role": "user",
          "content": prompt
        })

        let requestBody = {
          formType: 'gsc',
          messages: message,
          modelType: 'agent',
        }

        // 创建AbortController用于取消请求
        const controller = new AbortController();
        reader.value = controller;

        let accumulatedText = '';

        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`
        fetchEventSource(apiUrl,{
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal,
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              throw new Error(msg.data);
            }

            try {
              // 解析返回的数据
              const data = msg.data;

              try {
                const jsonData = JSON.parse(data);

                if (jsonData.error) {
                  return;
                }

                if (jsonData.choices && jsonData.choices[0]) {
                  if (jsonData.choices[0].message) {
                    accumulatedText += jsonData.choices[0].message.content;
                    questionResult.value = accumulatedText;
                    scrollToBottom();
                  }
                }

                // 处理可能的输出格式差异
                if (jsonData.output && jsonData.output.text) {
                  accumulatedText += jsonData.output.text;
                  questionResult.value = accumulatedText;
                  scrollToBottom();
                }
              } catch (error) {
                console.error('JSON parse error:', error);
              }
            } catch (error) {
              console.error('Message processing error:', error);
            }
           
          },
          async onclose() {
            console.log('连接关闭');
            loading.value = false;
            reader.value = null;
          },
          onerror(err) {
            console.error('Stream error:', err);
            loading.value = false;
            reader.value = null;
            throw new Error(err);
          }
        });

        trackingService.trackEvent('PoetAI')
      } catch (error) {
        console.error(error);
        loading.value = false;
        reader.value = null;
      }
    };

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          reader.value.abort('用户取消了操作');
          reader.value = null;
        } catch (error) {
          console.error('取消流式输出错误:', error);
        } finally {
          loading.value = false;
        }
      }
    };

    // 复制评论内容
    const copyComment = () => {
      if (!questionResult.value) {
        ElMessage.warning('没有可复制的内容')
        return
      }

      navigator.clipboard.writeText(questionResult.value)
        .then(() => {
          ElMessage.success('已复制到剪贴板')
        })
        .catch(() => {
          ElMessage.error('复制失败，请手动复制')
        })
    }

    // 复制答案内容
    const copyAnswer = () => {
      if (!answerResult.value) {
        ElMessage.warning('没有可复制的答案内容')
        return
      }

      navigator.clipboard.writeText(answerResult.value)
        .then(() => {
          ElMessage.success('答案已复制到剪贴板')
        })
        .catch(() => {
          ElMessage.error('复制失败，请手动复制')
        })
    }

    // 切换到AI古诗词
    const switchToVersePage = () => {
      console.log('切换到AI古诗词');
      console.log('发送事件: switch-component，参数:', 'verse');
      emit('switch-component', 'verse');
    }

    // 生成答案
    const handleGenerateAnswer = async () => {
      // 如果当前正在加载，则执行中止操作
      if (answerLoading.value && answerReader.value) {
        await cancelAnswerStream()
        return
      }

      if (!questionResult.value) {
        ElMessage.warning('请先生成题目')
        return
      }

      answerResult.value = null
      answerLoading.value = true
      answerAlreadySaved.value = false // 重置保存状态

      try {
        // 构建提示词
        let prompt = `请为以下古诗词题目提供答案以及解析：\n\n${questionResult.value}`

        // 构建请求体
        let message = []
        message.push({
          "role": "user", 
          "content": prompt
        })

        let requestBody = {
          formType: 'gsc_analysis',
          messages: message,
          modelType: 'agent',
        }

        // 创建AbortController用于取消请求
        const controller = new AbortController();
        answerReader.value = controller;

        let accumulatedText = '';

        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`
        fetchEventSource(apiUrl,{
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal,
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              throw new Error(msg.data);
            }

            try {
              // 解析返回的数据
              const data = msg.data;

              try {
                const jsonData = JSON.parse(data);

                if (jsonData.error) {
                  return;
                }

                if (jsonData.choices && jsonData.choices[0]) {
                  if (jsonData.choices[0].message) {
                    accumulatedText += jsonData.choices[0].message.content;
                    answerResult.value = accumulatedText;
                    console.log( answerResult.value )
                    scrollToBottom();
                  }
                }

                // 处理可能的输出格式差异
                if (jsonData.output && jsonData.output.text) {
                  accumulatedText += jsonData.output.text;
                  answerResult.value = accumulatedText;
                  scrollToBottom();
                }
              } catch (error) {
                console.error('JSON parse error:', error);
              }
            } catch (error) {
              console.error('Message processing error:', error);
            }
           
          },
          async onclose() {
            console.log('答案生成连接关闭');
            answerLoading.value = false;
            answerReader.value = null;
          },
          onerror(err) {
            console.error('Answer stream error:', err);
            answerLoading.value = false;
            answerReader.value = null;
            throw new Error(err);
          }
        });

        trackingService.trackEvent('PoetAI_Answer')
      } catch (error) {
        console.error(error);
        answerLoading.value = false;
        answerReader.value = null;
      }
    };

    // 取消答案流式输出
    const cancelAnswerStream = async () => {
      if (answerReader.value) {
        try {
          answerReader.value.abort('用户取消了答案生成操作');
          answerReader.value = null;
        } catch (error) {
          console.error('取消答案流式输出错误:', error);
        } finally {
          answerLoading.value = false;
        }
      }
    };

    return {
      formData,
      verseFilter,
      questionResult,
      loading,
      handleGenerate,
      renderedMarkdown,
      containerRef,
      selectOption,
      filteredGrades,
      copyComment,
      copyAnswer,
      switchToVersePage,
      chatService,
      buildPrompt,
      savePrompt,
      alreadySaved,
      reinitializeChatSession,
      answerResult,
      answerLoading,
      handleGenerateAnswer,
      answerAlreadySaved,
      showAiLoading
    }
  }
}
</script>

<style scoped>
.verse-question-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "📝";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
}

.question-form {
  width: 100%;
  margin: 0 auto;
}

.grade-section {
  margin-bottom: 20px;
}

.grade-selection-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.stage-buttons {
  display: flex;
  justify-content: flex-start;
}

.grade-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 0.5rem;
}

.tag-buttons-container .el-button {
  margin-left: 0;
  margin-right: 0;
  transition: all 0.3s;
}

.tag-buttons-container .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.term-divider {
  width: 1px;
  height: 24px;
  background-color: #dcdfe6;
  margin: 0 0.8rem;
}

.difficulty-buttons {
  display: flex;
  justify-content: flex-start;
}

.question-count-input {
  width: 180px;
}

.poem-input {
  width: 100%;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-content {
  text-align: left;
  padding: 2rem;
  position: relative;
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.question-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 1.5rem;
  background-color: #fff;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
  padding: 0 15px;
}

:deep(.el-form-item) {
  margin-bottom: 25px;
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}

.option-button {
  margin-right: 0;
  transition: all 0.3s;
}

.option-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .verse-question-container {
    width: 100%;
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
  }

  .question-text {
    font-size: 1rem;
  }

  .tag-buttons-container {
    gap: 8px;
  }

  .tag-button {
    font-size: 0.9rem;
    padding: 6px 10px;
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .form-container {
    margin: 2rem 0;
    padding: 1rem;
  }

  .result-content {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .question-text {
    padding: 1rem;
  }

  .tag-button {
    font-size: 0.8rem;
    padding: 4px 8px;
    margin-bottom: 4px;
    min-width: 60px;
  }

  .action-buttons {
    flex-direction: column;
    padding: 1rem;
    gap: 10px;
  }
}

.page-navigation {
  margin-top: 1.5rem;
}

.navigate-button {
  padding: 0.8rem 1.5rem;
  font-size: 1.1rem;
  transition: all 0.3s;
}

.navigate-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 150, 0.15);
}

.action-icons {
  display: flex;
  align-items: center;
}
</style>

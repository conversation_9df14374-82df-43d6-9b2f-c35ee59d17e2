<template>
  <div class="iframe-container">
    <iframe
      :src="iframeSrc"
      class="iframe-content"
      frameborder="0"
      allowfullscreen
      @load="handleIframeLoad"
    ></iframe>
    <div v-if="loading" class="iframe-loading">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>
    <!-- 悬浮球 -->
    <FloatingBall />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import FloatingBall from '../components/common/FloatingBall.vue'


const route = useRoute()
const store = useStore()
const loading = ref(true)

// 从路由参数中获取URL
const iframeSrc = computed(() => {
  const url = route.query.url
  if (!url) return ''
  
  // 如果包含chinatiye.cn且用户已登录，添加token
  if (url.includes('chinatiye.cn') && store.getters.isLoggedIn) {
    const separator = url.includes('?') ? '&' : '?'
    return `${url}${separator}token=${store.getters.aiToken}`
  }
  
  return url
})

const handleIframeLoad = () => {
  loading.value = false
}

onMounted(() => {
  // 设置页面标题
  const title = route.query.title || '加载中...'
  document.title = `均衡AI - ${title}`
})
</script>

<style scoped lang="scss">
.iframe-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.iframe-content {
  width: 100%;
  height: 100%;
  border: none;
}

.iframe-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #666;
  font-size: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 
import request from './request'

//  上传文件Txt
export function uploadTxt(data) {
  return request({
    url: '/junhengai/chat/random/v1/uploadTxt',
    method: 'post',
    data
  })
}

// 上传文件历史记录列表
export function getTxtHistory(params) {
  return request({
    url: '/junhengai/chat/random/v1/list',
    method: 'get',
    params
  })
}

// 记录名单
export function getTxtDetail(params) {
  return request({
    url: '/junhengai/chat/randomRecord/v1/queryList',
    method: 'get',
    params
  })
}

//  手动保存名单
export function addTxtName(data) {
  return request({
    url: '/junhengai/chat/randomRecord/v1/add',
    method: 'post',
    data
  })
}

// 手动编辑名单
export function editTxtName(data) {
  return request({
    url: '/junhengai/chat/randomRecord/v1/update',
    method: 'put',
    data
  })
}

// 手动删除名单
export function deleteTxtName(ids) {
  return request({
    url: `/junhengai/chat/randomRecord/v1/${ids}`,
    method: 'delete'
  })
} 

// 删除名单
export function deleteTxt(id, params) {
  return request({
    url: `/junhengai/chat/random/v1/${id}`,
    method: 'delete',
    params
  })
}

// 抽取名单
export function randomTxt(params) {
  return request({
    url: '/junhengai/chat/randomRecord/v1/queryRandomVoList',
    method: 'get',
    params
  })
}

// 查询抽取名单
export function getRandomTxt(params) {
  return request({
    url: '/junhengai/chat/randomRecord/v1/query/record/by/randomId',
    method: 'get',
    params
  })
}


// 下载文件
export function downloadTxt(data) {
  return request({
    url: `/junhengai/chat/random/v1/download-list`,
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function getTemplate(params) {
  return request({
    url: '/junhengai/chat/random/v1/template/excel',
    method: 'get',
    params
  })
}

// 查询最近一次的名单
export function getLastTxt(params) {
  return request({
    url: '/junhengai/chat/randomRecord/v1/index/queryList',
    method: 'get',
    params
  })
}

// 修改默认名单
export function setDefaultTxt(params) {
  return request({
    url: '/junhengai/chat/random/v1/update/useFlag',
    method: 'get',
    params
  })
}

// 修改文件名称
export function updateTxtName(data) {
  return request({
    url: '/junhengai/chat/random/v1/updateName',
    method: 'put',
    data
  })
}
<template>
  <div class="chat-container">
    <!-- 上方：对话历史 -->
    <div class="chat-messages-wrapper">
      <div class="chat-messages" ref="chatMessagesRef">
        <div  :class="['message', 'ai-message']">
          <div class="avatar-container">
            <img src="@/assets/logo.png" alt="AI头像" class="avatar-img">
          </div>
          <div class="message-content-wrapper">
            <div class="message-content" v-html="renderedMarkdown(welcomeMessage)"></div>
          </div>
        </div>
        <div v-for="(message, index) in messages" :key="index"
             :class="['message', message.role === 'user' ? 'user-message' : 'ai-message']">
          <div class="avatar-container">
            <img v-if="message.role === 'user'" :src="userAvatar" alt="用户头像" class="avatar-img">
            <img v-else src="@/assets/logo.png" alt="AI头像" class="avatar-img">
          </div>
          <div class="message-wrapper">
            <div v-if="message.thoughts && message.thoughts.length > 0" class="thoughts-container">
              <div class="thoughts-header">
                <svg viewBox="0 0 24 24" width="16" height="16" class="thoughts-icon">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" fill="none" stroke-width="2"></circle>
                  <path d="M12 8v4l3 3" stroke="currentColor" fill="none" stroke-width="2" stroke-linecap="round"></path>
                </svg>
                思考过程
              </div>
              <div class="thoughts-content" v-html="renderedMarkdown(message.thoughts)"></div>
            </div>
            <div class="message-content-wrapper" v-if="message.role === 'user'">
              <!-- 用户消息的附件展示区域 -->
              <div v-if="message.fileList && message.fileList.length > 0" class="message-files-area">
                <div class="message-files-list">
                  <div 
                    v-for="(file, fileIndex) in message.fileList" 
                    :key="fileIndex" 
                    :class="['message-file-item', { 'is-image': isImageFile(file.fileName) }]"
                  >
                    <!-- Image Case -->
                    <template v-if="isImageFile(file.fileName)">
                      <img 
                        :src="file.path" 
                        class="message-image-preview" 
                        @click="previewImage(file.path)"
                        :title="file.fileName"
                      />
                    </template>
                    
                    <!-- Document Case -->
                    <template v-else>
                      <div class="message-file-icon">
                        <img :src="getFileIcon(file.fileExt)" alt="文件图标" class="message-file-type-icon" />
                      </div>
                      <div class="message-file-info">
                        <div class="message-file-name" :title="file.fileName">{{ file.fileName }}</div>
                        <div class="message-file-size">{{ getFileSize(file.fileSize) }}</div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
              
              <div class="message-content">
                <!-- AI思考中的效果 -->
                <div v-if="shouldShowThinking(message, index)" class="thinking-container">
                  <div class="thinking-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                  </div>
                  <span class="thinking-text">AI思考中...</span>
                </div>
                <!-- 正常消息内容 -->
                <div v-else v-html="renderedMarkdown(message.content)"></div>
              </div>
            </div>
            <div class="message-content-wrapper" v-else>
              <div class="message-content">
                <!-- AI思考中的效果 -->
                <div v-if="shouldShowThinking(message, index)" class="thinking-container">
                  <div class="thinking-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                  </div>
                  <span class="thinking-text">AI思考中...</span>
                </div>
                <!-- 正常消息内容 -->
                <div v-else v-html="renderedMarkdown(message.content)"></div>
              </div>
              <div class="copy-btn-container" v-if="!shouldShowThinking(message, index) && message.content && !(loading && index === messages.length - 1 && message.role === 'assistant')">
                <div class="copy-btn" @click="copyToClipboard(message.content)">
                  <el-icon 
                    size="18px"
                    class="copy-icon"
                  >
                    <DocumentCopy />
                  </el-icon>
                  <span class="copy-text">复制</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下方：输入区域 -->
    <div class="input-area">
      <!-- 上传附件展示区域 -->
      <div v-if="uploadedFiles.length > 0" class="uploaded-files-area">
        <div class="uploaded-files-list">
          <div 
            v-for="(file, index) in uploadedFiles" 
            :key="file.uid || index" 
            :class="['uploaded-file-item', { 'is-image': file.isImage }]"
          >
            <!-- Image Case -->
            <template v-if="file.isImage">
              <img :src="file.uploading ? getFileIcon('image') : file.url" class="image-preview" />
            </template>
            
            <!-- Document Case -->
            <template v-else>
              <div class="file-icon">
                <img :src="getFileIcon(file.fileExt)" alt="文件图标" class="file-type-icon" />
              </div>
              <div class="file-info">
                <div class="file-name" :title="file.fileName">{{ file.fileName }}</div>
                <div class="file-size">{{ getFileSize(file.fileSize) }}</div>
              </div>
            </template>

            <!-- Common elements -->
            <div v-if="file.uploading" class="file-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
            </div>
            <div v-else class="file-remove" @click="removeFile(file)">
              <el-icon><Close /></el-icon>
            </div>
          </div>
        </div>
      </div>

      <div class="unified-input-container-wrapper">
        <div class="unified-input-container">
          <div class="input-with-icon">
            <div class="search-icon"></div>
            <el-input
                v-model="userInput"
                placeholder="有问题，尽管问..."
                class="chat-textarea"
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 3 }"
                @keydown.enter.exact.prevent="sendMessage"
            />
          </div>
          <div class="chat-actions-bar">
            <div class="left-actions">
              <el-button
                  :class="['action-btn', { 'selected': isDeepThink }]"
                  @click="toggleDeepThink"
              >
                <svg viewBox="0 0 24 24" width="16" height="16" class="react-icon" style="margin-right: 6px;">
                  <path fill="currentColor" d="M12 10.11c1.03 0 1.87.84 1.87 1.89 0 1.04-.84 1.87-1.87 1.87-1.03 0-1.87-.83-1.87-1.87 0-1.05.84-1.89 1.87-1.89M7.37 20c.63.38 2.01-.2 3.6-1.7-.52-.59-1.03-1.23-1.51-1.9-.82-.08-1.63-.2-2.4-.36-.51 2.14-.32 3.61.31 3.96m.71-5.74l-.29-.51c-.11.29-.22.58-.29.86.27.06.57.11.88.16l-.3-.51m6.54-.76l.81-1.5-.81-1.5c-.3-.53-.62-1-.91-1.47C13.17 9.03 12.6 9 12 9c-.6 0-1.17.03-1.71.08-.29.47-.61.94-.91 1.47L8.57 12l.81 1.5c.3.53.62 1 .91 1.47.54.05 1.11.08 1.71.08.6 0 1.17-.03 1.71-.08.29-.47.61-.94.91-1.47M12 6.78c-.19.22-.39.45-.59.72h1.18c-.2-.27-.4-.5-.59-.72m0 10.44c.19-.22.39-.45.59-.72h-1.18c.2.27.4.5.59.72M16.62 4c-.62-.38-2 .2-3.59 1.7.52.59 1.03 1.23 1.51 1.9.82.08 1.63.2 2.4.36.51-2.14.32-3.61-.32-3.96m-.7 5.74l.29.51c.11-.29.22-.58.29-.86-.27-.06-.57-.11-.88-.16l.3.51m1.45-7.05c1.47.84 1.63 3.05 1.01 5.63 2.54.75 4.37 1.99 4.37 3.68 0 1.69-1.83 2.93-4.37 3.68.62 2.58.46 4.79-1.01 5.63-1.46.84-3.45-.12-5.37-1.95-1.92 1.83-3.91 2.79-5.38 1.95-1.46-.84-1.62-3.05-1-5.63-2.54-.75-4.37-1.99-4.37-3.68 0-1.69 1.83-2.93 4.37-3.68-.62-2.58-.46-4.79 1-5.63 1.47-.84 3.46.12 5.38 1.95 1.92-1.83 3.91-2.79 5.37-1.95M17.08 12c.34.75.64 1.5.89 2.26 2.1-.63 3.28-1.53 3.28-2.26 0-.73-1.18-1.63-3.28-2.26-.25.76-.55 1.51-.89 2.26M6.92 12c-.34-.75-.64-1.5-.89-2.26-2.1.63-3.28 1.53-3.28 2.26 0 .73 1.18 1.63 3.28 2.26.25-.76.55-1.51.89-2.26m9 2.26l-.3.51c.31-.05.61-.1.88-.16-.07-.28-.18-.57-.29-.86l-.29.51m-2.89 4.04c1.59 1.5 2.97 2.08 3.59 1.7.64-.35.83-1.82.32-3.96-.77.16-1.58.28-2.4.36-.48.67-.99 1.31-1.51 1.9M8.08 9.74l.3-.51c-.31.05-.61.1-.88.16.07.28.18.57.29.86l.29-.51m2.89-4.04C9.38 4.2 8 3.62 7.37 4c-.63.35-.82 1.82-.31 3.96.77-.16 1.58-.28 2.4-.36.48-.67.99-1.31 1.51-1.9z"/>
                </svg>
                深度思考 (R1)
              </el-button>
              <el-button
                  :class="['action-btn', { 'selected': isWebSearch }]"
                  @click="toggleWebSearch"
              >
                <svg style="width: 16px; height: 16px;" class="btn-icon" viewBox="0 0 24 24" width="16" height="16"
                     stroke-width="2.5" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="2" y1="12" x2="22" y2="12"></line>
                  <path
                      d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                </svg>
                联网搜索
              </el-button>
              <el-button
                  class="action-btn"
                  @click="clearHistory"
                  v-if="messages.length > 0"
              >
                <svg style="width: 16px; height: 16px;" class="btn-icon" viewBox="0 0 24 24" width="16" height="16"
                     stroke-width="2.5" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="3 6 5 6 21 6"></polyline>
                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
                清空历史
              </el-button>
            </div>
            <div class="right-actions">
              <el-popover
                placement="top"
                width="300"
                trigger="hover"
                content="上传文件（仅识别文字），支持pdf、pptx、docx、xlsx、txt、图片，单图片最大20MB，其他文件最大50MB"
                >
                <template #reference>
                  <el-upload
                    ref="uploadRef"
                    :auto-upload="false"
                    :show-file-list="false"
                    :multiple="true"
                    :limit="1"
                    :on-change="handleFileChange"
                    :on-exceed="handleExceed"
                    :file-list="[]" 
                    accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt,.png,.jpg,.jpeg,.gif,.bmp"
                  >
                    <el-button class="upload-btn" :disabled="isUploadDisabled" @click="clickUpload">
                      <el-icon size="18px" style="margin-right: 6px;">
                        <Upload />
                      </el-icon>
                      上传附件
                    </el-button>
                  </el-upload>
                </template>
              </el-popover>
              <el-button
                  class="send-btn-final"
                  type="primary"
                  @click="sendMessage"
                  :class="{
                    'loading-btn': loading,
                    'disabled-btn': !userInput.trim() && !loading
                  }"
                  :disabled="!userInput.trim() && !loading"
              >
                <template v-if="!loading">
                  <el-icon size="24px" style="margin-right: 5px;">
                    <Position />
                  </el-icon>
                  <span>发送</span>
                </template>
                <template v-else>
                  <el-icon size="24px" style="margin-right: 5px;">
                    <VideoPause />
                  </el-icon>
                  <span>中止</span>
                </template>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览组件 -->
    <el-image-viewer
      v-if="imagePreviewVisible"
      :url-list="[previewImageUrl]"
      :initial-index="0"
      @close="closeImagePreview"
      hide-on-click-modal
      :teleported="true"
    />

  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox, ElImageViewer } from 'element-plus'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import MarkdownIt from "markdown-it"
import mk from "@iktakahiro/markdown-it-katex"
import { getChatList,  saveChat, saveChatRecordNew, deleteChatNew, uploadAliyunFile, getAliyunFileStatus } from '@/api/home'
import { DocumentCopy, Position, VideoPause, Loading, Close, Upload } from '@element-plus/icons-vue'
import { uploadToMinIO } from '@/api/request'
import { getFileSize, formatFile } from '@/utils/tools'

const router = useRouter()
const route = useRoute()
const store = useStore()
const trackingService = inject('trackingService')

// 聊天相关
const userInput = ref('')
const welcomeMessage = ref('我是一位专注于教育领域的专家，致力于为教师、学生、家长及其他教育工作者提供专业的教育建议和支持。我在教育理论、教学方法、课程设计、学生发展以及家校沟通等方面有丰富的知识和实践经验。如果你有任何与教育相关的问题，比如如何改进教学、设计课程、帮助学生成长等，都可以向我咨询。')

const messages = ref([])
const loading = ref(false)
const reader = ref(null)
const chatMessagesRef = ref(null)
const chatId = ref('') // 保存当前对话窗口ID
const uploadRef = ref(null) // 上传组件引用

// 附件相关
const uploadedFiles = ref([]) // 已上传的文件列表

// 图片预览相关
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 功能选项
const isDeepThink = ref(false)
const isWebSearch = ref(false)

// 用户信息相关
const isLoggedIn = computed(() => store.getters.isLoggedIn)
const userInfo = computed(() => store.getters.userInfo)
const userAvatar = computed(() => {
  if (isLoggedIn.value && userInfo.value && userInfo.value.avatar) {
    return userInfo.value.avatar;
  }
  return new URL('@/assets/user.png', import.meta.url).href; // 确保路径正确
});

// 计算是否已达到最大上传数量
const isUploadDisabled = computed(() => {
  return uploadedFiles.value.length >= 1;
});

// 判断是否应该显示AI思考中效果
const shouldShowThinking = (message, index) => {
  // 必须是assistant角色的消息
  if (message.role !== 'assistant') return false
  
  // 必须是最后一条消息
  if (index !== messages.value.length - 1) return false
  
  // 必须正在加载中
  if (!loading.value) return false
  
  // 内容必须为空
  if (message.content && message.content.trim()) return false
  
  return true
}


// 获取历史对话窗口
const getChatHistory = async () => {
  if (!isLoggedIn.value) return
  
  try {
    const res = await getChatList({
      userId: store.getters.userId,
      category: 'askAI'
    })
    
    if (res.data && res.data.length > 0) {
      // 获取最新的对话窗口
      const latestChat = res.data[0]
      chatId.value = latestChat.id
      
      // 如果有历史消息，加载到当前对话中
      if (latestChat.messages && latestChat.messages.length > 0) {
        messages.value = latestChat.messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          thoughts: msg.thoughtsContent || '',
          fileList: msg.fileList || [] // 加载消息的附件信息
        }))
      }
    } else {
      await createNewChat()
    }
  } catch (error) {
    console.error('获取历史对话失败:', error)
  }
}

// 创建新的对话窗口
const createNewChat = async () => {
  if (!isLoggedIn.value) return
  
  try {
    const res = await saveChat({
      userId: store.getters.userId,
      title: "均衡AI问答",
      category: "askAI"
    })
    
    if (res.data) {
      chatId.value = res.data
    }
  } catch (error) {
    console.error('创建对话窗口失败:', error)
  }
}

// 保存对话记录
const saveCurrentChatRecord = async (currentFileList = []) => {
  if (!isLoggedIn.value || !chatId.value || messages.value.length === 0) return
  
  try {
    // 只保存当次会话记录（最后一轮用户输入和AI回复）
    const lastMessages = messages.value.slice(-2) // 获取最后两条消息
    
    // 构建附件信息
    const fileList = currentFileList.map(file => ({
      fileName: file.fileName,
      wpsKey: file.key,
      path: file.url,
      fileExt: file.fileExt,
      fileSize: file.fileSize,
      fileId: file.fileId
    }))
    
    // 构建保存的消息格式
    const recordMessages = lastMessages.map(msg => {
      const record = {
        role: msg.role,
        content: msg.content
      }
      
      // 如果有思考过程，添加到记录中
      if (msg.role === 'assistant' && msg.thoughts) {
        record.thoughtsContent = msg.thoughts
      }
      
      // 如果是用户消息且有附件，添加附件信息到消息中
      if (msg.role === 'user' && fileList.length > 0) {
        record.fileList = fileList
      }
      
      return record
    })
    
    await saveChatRecordNew({
      userId: store.getters.userId,
      chatId: chatId.value,
      messages: recordMessages,
      fileList: fileList // 保持外层的fileList用于兼容性
    })
    
    console.log('对话记录保存成功')
  } catch (error) {
    console.error('保存对话记录失败:', error)
  }
}

// 初始化页面，处理 localStorage 参数
onMounted(async () => {
  // 获取历史对话
  await getChatHistory()
  
  // 初始化上传组件
  resetUploader()
  
  // 从 localStorage 读取参数
  const chatParamsStr = localStorage.getItem('chatParams');
  
  if (chatParamsStr) {
    try {
      const chatParams = JSON.parse(chatParamsStr);
      
      if (chatParams.message) {
        userInput.value = chatParams.message;
      }
      
      if (chatParams.deepThink) {
        isDeepThink.value = true;
      }
      
      if (chatParams.webSearch) {
        isWebSearch.value = true;
      }
      
      // 清除 localStorage 中的参数，避免刷新页面时重复请求
      localStorage.removeItem('chatParams');
      
      // 如果需要上传附件，不自动发送消息
      if (chatParams.needUpload) {
        // 可以在这里添加一些提示或者直接触发上传组件的点击
        nextTick(() => {
          // 可以通过程序化方式触发上传组件的点击
          if (uploadRef.value && uploadRef.value.$el) {
            const uploadInput = uploadRef.value.$el.querySelector('input[type="file"]');
            if (uploadInput) {
              uploadInput.click();
            }
          }
        });
      } else if (chatParams.message && chatParams.message.trim()) {
        // 如果有初始消息且不需要上传，自动发送
        nextTick(() => {
          sendMessage();
        });
      }
    } catch (error) {
      console.error('解析 chatParams 失败:', error);
      localStorage.removeItem('chatParams');
    }
  }
});

// 聊天功能
const toggleDeepThink = () => {
  if (isWebSearch.value) {
    isWebSearch.value = false
  }
  isDeepThink.value = !isDeepThink.value
}

const toggleWebSearch = () => {
  if (isDeepThink.value) {
    isDeepThink.value = false
  }
  isWebSearch.value = !isWebSearch.value
}

const clearHistory = () => {
  ElMessageBox.confirm(
    '确定要清空所有对话记录吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    if (chatId.value) {
      try {
        await deleteChatNew(store.getters.userId, 'askAI')
        chatId.value = '' 
        // 清空本地消息
        messages.value = []
        ElMessage.success('对话记录已清空')
        // 创建新的对话窗口
        await createNewChat()
      } catch (error) {
        console.error('删除对话记录失败:', error)
      }
    }
  }).catch(() => {
    // 取消清空
  })
}

// 自动滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatMessagesRef.value) {
      const wrapper = chatMessagesRef.value.parentElement;
      if (wrapper) {
        wrapper.scrollTop = wrapper.scrollHeight;
      }
    }
  })
}

// 监听消息变化，自动滚动
watch(messages, () => {
  scrollToBottom()
}, { deep: true })

// 取消流式输出
const cancelStream = async (sessionFiles = []) => {
  if (reader.value) {
    try {
      // 在中止前，给最后一条 assistant 消息添加中止提示
      const lastMessageIndex = messages.value.length - 1
      if (messages.value[lastMessageIndex]?.role === 'assistant') {
        const currentContent = messages.value[lastMessageIndex].content || ''
        // 如果内容不为空，添加换行和中止提示；如果为空，直接添加中止提示
        messages.value[lastMessageIndex].content = currentContent ? 
          currentContent + '\n\n[回复已中止]' : 
          '[回复已中止]'
      }
      
      reader.value.abort('用户取消了操作')
      console.log('Abort signal sent.')
    } catch (error) {
      console.error('取消流式输出错误:', error)
    } finally {
      loading.value = false
      reader.value = null
      
      // 保存对话记录
      await saveCurrentChatRecord(sessionFiles)
    }
  }
}

// Markdown渲染函数
const renderedMarkdown = (markdownContent) => {
  if (!markdownContent) return

  // 预处理Markdown内容，将LaTeX格式的公式转换为KaTeX兼容格式
  let processedContent = markdownContent

  // 转换 \[ \] 格式为 $$ $$，并移除首尾多余空格
  processedContent = processedContent.replace(/\\\[\s*([\s\S]*?)\s*\\\]/g, (match, p1) => `$$${p1.trim()}$$`)

  // 转换 \( \) 格式为 $ $，并移除首尾多余空格
  processedContent = processedContent.replace(/\\\(\s*([\s\S]*?)\s*\\\)/g, (match, p1) => `$${p1.trim()}$`)

  // 保存并移除所有 $$ 包裹的内容，防止处理双美元符号内容
  const doubleDollarBlocks = [];
  processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, (match, content) => {
    doubleDollarBlocks.push(match);
    return `__DOUBLE_DOLLAR_PLACEHOLDER_${doubleDollarBlocks.length - 1}__`;
  });

  // 使用更健壮的正则表达式处理所有单美元符号的情况
  // 捕获成对的$，并处理其中可能存在的各种空格情况
  processedContent = processedContent.replace(/(\$)\s*([^\$]+?)\s*(\$)/g, (match, start, content, end) => {
    // 对内容进行处理，只移除首尾空格，保留内部空格
    return `${start}${content.trim()}${end}`;
  });

  // 恢复所有 $$ 包裹的内容
  doubleDollarBlocks.forEach((block, index) => {
    processedContent = processedContent.replace(`__DOUBLE_DOLLAR_PLACEHOLDER_${index}__`, block);
  });

  const md = new MarkdownIt();
  md.use(mk);
  const htmlContent = md.render(processedContent);
  return htmlContent;
}

// 复制到剪贴板功能
const copyToClipboard = async (content) => {
  try {
    // 如果内容包含HTML标签，需要先提取纯文本
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = content
    const plainText = tempDiv.textContent || tempDiv.innerText || ''
    
    await navigator.clipboard.writeText(plainText)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级到传统的复制方法
    try {
      const textArea = document.createElement('textarea')
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = content
      const plainText = tempDiv.textContent || tempDiv.innerText || ''
      
      textArea.value = plainText
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('内容已复制到剪贴板')
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError)
      ElMessage.error('复制失败，请手动复制')
    }
  }
}

// 点击上传附件
const clickUpload = () => {
  // 清除深度思考和联网搜索的选中状态
  // isDeepThink.value = false
  isWebSearch.value = false
}

// 获取文件图标
const getFileIcon = (fileExt) => {
  const standardExt = formatFile(fileExt)
  const iconMap = {
    'doc': 'https://www.junhengyun.cn/cdn/wechat/file_icon/doc1.png',
    'pdf': 'https://www.junhengyun.cn/cdn/wechat/file_icon/pdf1.png',
    'xls': 'https://www.junhengyun.cn/cdn/wechat/file_icon/xls1.png',
    'txt': 'https://www.junhengyun.cn/cdn/wechat/file_icon/zip1.png',
    'image': 'https://www.junhengyun.cn/cdn/wechat/file_icon/image1.png',
    'ppt': 'https://www.junhengyun.cn/cdn/wechat/file_icon/ppt1.png'
  }
  return iconMap[standardExt] 
}

// 检查是否为图片文件
const isImageFile = (fileName) => {
  const ext = fileName.substr(fileName.lastIndexOf('.') + 1, fileName.length).toLowerCase()
  return ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(ext)
}

// 预览图片
const previewImage = (imageUrl) => {
  previewImageUrl.value = imageUrl
  imagePreviewVisible.value = true
}

// 关闭图片预览
const closeImagePreview = () => {
  imagePreviewVisible.value = false
  previewImageUrl.value = ''
}

// 处理文件数量超出限制
const handleExceed = (files, fileList) => {
  ElMessage.warning(`最多只能上传1个文件`)
  
  // 强制重置组件状态
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 重置上传组件状态
const resetUploader = () => {
  if (uploadRef.value) {
    // 清除所有文件
    uploadRef.value.clearFiles()
    
    // 更新组件的internal state
    nextTick(() => {
      if (uploadRef.value && uploadRef.value.uploadFiles) {
        // 确保上传组件的内部文件列表与我们的状态同步
        uploadRef.value.uploadFiles.length = 0
      }
    })
  }
}

// 移除文件
const removeFile = (fileToRemove) => {
  const localIndex = uploadedFiles.value.findIndex(f => f.uid === fileToRemove.uid)
  if (localIndex !== -1) {
    uploadedFiles.value.splice(localIndex, 1)
  }
  
  if (uploadRef.value) {
    const internalFiles = uploadRef.value.uploadFiles
    const internalIndex = internalFiles.findIndex(f => f.uid === fileToRemove.uid)
    if (internalIndex !== -1) {
      internalFiles.splice(internalIndex, 1)
    }
    // 强制清除所有文件并重新初始化上传组件
    resetUploader()
  }
}

// 处理文件选择变化
const handleFileChange = async (file, fileList) => {
  // 检查是否超过上传限制
  if (uploadedFiles.value.length >= 1) {
    ElMessage.warning('最多只能上传1个文件')
    // 阻止此次上传
    resetUploader()
    return
  }
  
  console.log('选择的文件:', file)
  
  const rawFile = file.raw
  const fileName = rawFile.name
  const fileExt = fileName.substr(fileName.lastIndexOf('.') + 1, fileName.length)
  const isImage = isImageFile(fileName)
  
  // 检查文件大小限制
  const fileSizeInBytes = rawFile.size
  const fileSizeInKB = fileSizeInBytes / 1024
  const fileSizeInMB = fileSizeInBytes / (1024 * 1024)
  const maxSizeInMB = isImage ? 20 : 50
  
  // 检查文件大小不能小于1KB
  if (fileSizeInKB < 1) {
    ElMessage.error('文件大小不能小于1KB')
    // 阻止此次上传
    resetUploader()
    return
  }

  if (fileSizeInMB > maxSizeInMB) {
    ElMessage.error(`${isImage ? '图片' : '文件'}大小不能超过${maxSizeInMB}MB，当前文件大小为${fileSizeInMB.toFixed(2)}MB`)
    // 阻止此次上传
    resetUploader()
    return
  }
  
  // 创建文件对象
  const fileObj = {
    uid: file.uid,
    fileName: fileName,
    fileSize: rawFile.size,
    fileExt: fileExt,
    isImage: isImage,
    uploading: true,
    url: null,
    key: null,
    fileId: null
  }
  
  // 添加到文件列表
  uploadedFiles.value.push(fileObj)
  
  try {
    const formDataObj = new FormData()
    formDataObj.append('file', rawFile)
    formDataObj.append('formType', isDeepThink.value ? 'chat_think' : (isWebSearch.value ? 'chat_web' : 'chat_not_think'))
    const ossObj = await uploadAliyunFile(formDataObj)    
    // 获取fileSessionId
    const fileSessionId = ossObj.data
    
    // 轮询检查文件解析状态
    let fileStatus = 'PARSING'
    
    while (fileStatus === 'PARSING') {
      // 等待2秒
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      try {
        // 查询文件状态
        const statusRes = await getAliyunFileStatus({ fileSessionId,formType: isDeepThink.value ? 'chat_think' : (isWebSearch.value ? 'chat_web' : 'chat_not_think') })
        console.log('文件状态:', statusRes)
        
        if (statusRes && statusRes.data) {
          fileStatus = statusRes.data
          
          // 如果状态是ERROR或EXPIRED，都视为失败
          if (fileStatus === 'ERROR' || fileStatus === 'EXPIRED') {
            ElMessage.error('文件解析失败，请重试')
            // 移除上传失败的文件
            const fileIndex = uploadedFiles.value.findIndex(f => f.uid === file.uid)
            if (fileIndex !== -1) {
              uploadedFiles.value.splice(fileIndex, 1)
            }
            resetUploader()
            return
          } else if (fileStatus === 'FINISH') {
            // 文件解析完成，继续后续操作
            break
          }
        }
      } catch (statusError) {
        console.error('获取文件状态失败:', statusError)
      }
    }

    // 文件解析完成后，上传到minIO
    const uploadResult = await uploadToMinIO(rawFile)
    console.log('上传结果:', uploadResult)
    
    // 更新文件状态
    const fileIndex = uploadedFiles.value.findIndex(f => f.uid === file.uid)
    if (fileIndex !== -1) {
      uploadedFiles.value[fileIndex].uploading = false
      uploadedFiles.value[fileIndex].url = uploadResult.url
      uploadedFiles.value[fileIndex].key = uploadResult.key
      uploadedFiles.value[fileIndex].fileId = fileSessionId
    }

  } catch (error) {
    console.error('文件上传失败:', error)
    // 移除上传失败的文件
    const fileIndex = uploadedFiles.value.findIndex(f => f.uid === file.uid)
    if (fileIndex !== -1) {
      uploadedFiles.value.splice(fileIndex, 1)
    }
    resetUploader()
  }
}

// 发送消息
const sendMessage = async () => {
  // 保存当前附件信息的副本，用于后续保存对话记录
  const currentSessionFiles = [...uploadedFiles.value]
  
  if (loading.value) {
    await cancelStream(currentSessionFiles)
    return
  }
  
  // 检查是否有附件正在上传
  const hasUploadingFiles = uploadedFiles.value.some(file => file.uploading)
  if (hasUploadingFiles) {
    ElMessage.warning('附件正在上传，请稍候...')
    return
  }


  if (!userInput.value.trim()) {
    ElMessage.warning('请输入内容')
    return
  }

  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    return
  }
  
  // 如果没有对话窗口ID，先创建一个
  if (!chatId.value) {
    await createNewChat()
  }

  const userMessageContent = userInput.value.trim()
  
  // 构建附件信息用于显示
  const messageFileList = currentSessionFiles.map(file => ({
    fileName: file.fileName,
    wpsKey: file.key,
    path: file.url,
    fileExt: file.fileExt,
    fileSize: file.fileSize,
    fileId: file.fileId
  }))
  
  // 添加用户消息，包含附件信息
  messages.value.push({ 
    role: 'user', 
    content: userMessageContent,
    fileList: messageFileList
  })
  userInput.value = ''

  loading.value = true

  try {
    // 构建请求消息历史
    const requestMessages = messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))

    // 构建请求体
    const requestBody = {
      messages: requestMessages,
      hasThoughts: isDeepThink.value,
      formType: isDeepThink.value ? 'chat_think' : (isWebSearch.value ? 'chat_web' : 'chat_not_think')
    }
    
    // 上传附件模型参数
    if (uploadedFiles.value.length > 0) {
      requestBody.messages = requestMessages.slice(-1)
      // requestBody.isR1 = false
      requestBody.fileIds = uploadedFiles.value.map(file => file.fileId)
      uploadedFiles.value = []
      resetUploader()
    }

    const controller = new AbortController()
    reader.value = controller
    messages.value.push({ role: 'assistant', content: '', thoughts: '' })
    let finalAssistantMessage = ''
    let finalThoughts = ''

    const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`

    // 使用fetchEventSource代替fetch
    fetchEventSource(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Authorization': `Bearer ${store.getters.aiToken}`
      },
      body: JSON.stringify(requestBody),
      signal: controller.signal,
      openWhenHidden: true,
      async onopen(response) {
        if (!response.ok) {
          loading.value = false
          reader.value = null
          const lastMessageIndex = messages.value.length - 1
          if (messages.value[lastMessageIndex]?.role === 'assistant' && messages.value[lastMessageIndex].content === '') {
            messages.value.pop()
          }
          throw new Error(`HTTP error! status: ${response.status}`)
        }
      },
      onmessage(msg) {
        if (msg.event === 'FatalError') {
          loading.value = false
          reader.value = null
          throw new Error(msg.data)
        }

        const data = msg.data

        try {
          const jsonData = JSON.parse(data)
          
          if (jsonData.error) {
            loading.value = false
            reader.value = null
            const lastMessageIndex = messages.value.length - 1
            if (messages.value[lastMessageIndex]?.role === 'assistant') {
              messages.value[lastMessageIndex].content = `对话出错: ${jsonData.error.message}`
            }
            return
          }

          if (jsonData.choices && jsonData.choices[0]) {
            const lastMessageIndex = messages.value.length - 1
            if (messages.value[lastMessageIndex]?.role === 'assistant') {
              messages.value[lastMessageIndex].content += jsonData.choices[0].message.content
              scrollToBottom()
            }
            finalAssistantMessage += jsonData.choices[0].message.content
          }
          
          // 处理思考过程
          if (jsonData.choices && jsonData.choices[0]) {
            const lastThought =  jsonData.choices[0].message.reasoning_content
            if (lastThought) {
              const lastMessageIndex = messages.value.length - 1
              if (messages.value[lastMessageIndex]?.role === 'assistant') {
                if (!messages.value[lastMessageIndex].thoughts) {
                  messages.value[lastMessageIndex].thoughts = ''
                }
                messages.value[lastMessageIndex].thoughts += lastThought
                scrollToBottom()
              }
              finalThoughts += lastThought
            }
          }
        } catch (error) {
          console.error('JSON parse error:', error)
        }
      },
      async onclose() {
        console.log('连接关闭')
        if (loading.value) {
          loading.value = false
          reader.value = null
        }
        const lastMessageIndex = messages.value.length - 1
        if (messages.value[lastMessageIndex]?.role === 'assistant' && messages.value[lastMessageIndex].content === '') {
          console.log("Stream closed with empty assistant message.")
          messages.value[lastMessageIndex].content = '(对话中断或无内容返回)'
        }
        
        // 保存对话记录
        await saveCurrentChatRecord(currentSessionFiles)
      },
      onerror(err) {
        console.error('Stream error:', err)
        if (err.name !== 'AbortError') {
          const lastMessageIndex = messages.value.length - 1
          if (messages.value[lastMessageIndex]?.role === 'assistant') {
            messages.value[lastMessageIndex].content = '抱歉，对话流传输过程中出现错误。'
          }
        } else {
          console.log('Stream aborted by user.')
        }
        loading.value = false
        reader.value = null  
      }
    })

  } catch (error) {
    console.error(error)

    const lastMessageIndex = messages.value.length - 1
    if (messages.value[lastMessageIndex]?.role === 'assistant' && messages.value[lastMessageIndex].content === '') {
      messages.value.pop()
    } else if (messages.value[lastMessageIndex]?.role === 'assistant') {
      messages.value[lastMessageIndex].content = '抱歉，启动对话时出错。'
    } else {
      messages.value.push({
        role: 'assistant',
        content: '抱歉，启动对话时出错。'
      })
    }

    loading.value = false
    reader.value = null
  }
  
  if (trackingService) {
    trackingService.trackEvent('Ask')
  }
}
</script>

<style scoped lang="scss">

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #fff;
}

.chat-messages-wrapper {
  flex: 1;
  overflow-y: auto;
  background-color: #f8f9fa;
  min-height: 0;
  padding: 0 1.5rem;
}

.chat-messages {
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  margin: 0 auto;
  width: 75vw;
}

.message {
  display: flex;
  max-width: 85%;
  margin-bottom: 0;
  align-items: flex-start;
  position: relative;
  padding-bottom: 5px;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
  width: 80%;
}

.avatar-container {
  flex-shrink: 0;
  margin-right: 12px;
  margin-top: 4px;
}

.user-message .avatar-container {
  margin-right: 0;
  margin-left: 12px;
}

.user-message .avatar-img {
  width: 50px;
  height: 50px;
  border-color: #e6f1ff;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.ai-message .avatar-img {
  width: 40px;
  height: 40px;
  border-color: #f1f3f5;
}

.message-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.thoughts-container {
  background-color: #f5f8fa;
  border-radius: 12px;
  padding: 12px;
  border: 1px solid #e1e8ed;
  margin-bottom: 5px;
}

.thoughts-header {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #657786;
  margin-bottom: 8px;
  font-weight: 500;
}

.thoughts-icon {
  margin-right: 6px;
  color: #657786;
}

.thoughts-content {
  font-size: 14px;
  color: #14171a;
  line-height: 1.5;
}

.ai-message .thoughts-container {
  background-color: #f5f8fa;
  border-color: #e1e8ed;
}

.user-message .thoughts-container {
  background-color: rgba(44, 134, 255, 0.1);
  border-color: rgba(44, 134, 255, 0.2);
}

.message-content-wrapper {
  position: relative;
  flex: 1;
  margin-bottom: 0;
}

.copy-btn-container {
  position: absolute;
  right: 8px;
  bottom: 8px;
  opacity: 1;
  z-index: 2;
}

.copy-btn {
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.copy-btn:hover {
  color: #409EFF;
}

.copy-text {
  font-size: 16px;
}

.message-content {
  padding: 12px 16px 25px;
  border-radius: 1rem;
  word-break: break-word;
  line-height: 1.6;
  flex: 1;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  position: relative;
}

.message-content :deep(p) {
  margin: 0.3rem 0;
  white-space: pre-line;
}

.message-content :deep(pre) {
  margin: 0.5rem 0;
  padding: 0.8rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  overflow-x: auto;
  white-space: pre;
}

.message-content :deep(code) {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
}

.message-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.message-content :deep(ul),
.message-content :deep(ol) {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.message-content :deep(li) {
  margin: 0.25rem 0;
}

.message-content :deep(blockquote) {
  margin: 0.5rem 0;
  padding: 0.5rem 1rem;
  border-left: 4px solid #ddd;
  background-color: rgba(0, 0, 0, 0.03);
  color: #666;
}

.user-message .message-content :deep(blockquote) {
  border-left-color: rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.user-message .message-content :deep(code) {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.user-message .message-content :deep(pre) {
  background-color: rgba(255, 255, 255, 0.1);
}

.message-content :deep(h1),
.message-content :deep(h2),
.message-content :deep(h3),
.message-content :deep(h4),
.message-content :deep(h5),
.message-content :deep(h6) {
  margin: 1rem 0 0.5rem;
  font-weight: 600;
  line-height: 1.4;
}

.message-content :deep(h1) { font-size: 1.5em; }
.message-content :deep(h2) { font-size: 1.3em; }
.message-content :deep(h3) { font-size: 1.1em; }
.message-content :deep(h4) { font-size: 1em; }
.message-content :deep(h5) { font-size: 0.9em; }
.message-content :deep(h6) { font-size: 0.8em; }

.input-area {
  padding: 20px;
  background-color: white;
  border-top: 1px solid #e8e8e8;
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 上传附件展示区域 */
.uploaded-files-area {
  width: 75vw;
  margin-bottom: 15px;
}

.uploaded-files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.uploaded-file-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  position: relative;
  height: 72px;
  box-sizing: border-box;
  padding: 0 12px;
  width: 250px;
}

.uploaded-file-item.is-image {
  padding: 0;
  width: 72px;
  background: #e9ecef;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.file-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.file-type-icon {
  width: 40px;
  height: 40px;
}

.file-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.file-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border-radius: 8px;
  z-index: 1;
}

.file-remove {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  background-color: #909399;
  border-radius: 50%;
  transition: all 0.2s;
  z-index: 2;
}

.file-remove:hover {
  background-color: #ff4d4f;
  transform: scale(1.1);
}

/* 消息中的附件展示区域 */
.message-files-area {
  margin-bottom: 12px;
}

.message-files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.message-file-item {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  display: flex;
  align-items: center;
  position: relative;
  height: 56px;
  box-sizing: border-box;
  padding: 0 10px;
  max-width: 240px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-file-item.is-image {
  padding: 0;
  width: 56px;
  background: rgba(255, 255, 255, 0.95);
}

.message-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.message-image-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.message-file-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.message-file-type-icon {
  width: 32px;
  height: 32px;
}

.message-file-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.message-file-name {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-file-size {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
}

@media (max-width: 768px) {
  .uploaded-files-area {
    width: 100%;
  }
  
  .uploaded-file-item {
    max-width: calc(50% - 6px);
  }
  
  .file-name {
    max-width: 80px;
  }
  
  .message-file-item {
    max-width: calc(50% - 4px);
  }
  
  .message-file-name {
    max-width: 80px;
  }

}

.unified-input-container-wrapper {
  padding: 3px;
  border-radius: 16px;
  position: relative;
  z-index: 10;
  background-image: linear-gradient(270deg, #ffad71, #ff5aa2, #46aaff, #80d0ff);
  width: 100%;
  width: 75vw;
}

.unified-input-container {
  background: #FFF;
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
}

.input-with-icon {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.search-icon {
  width: 36px;
  height: 36px;
  flex-shrink: 0;
  display: inline-block;
  background-image: url('@/assets/search-btn.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  margin-top: 4px;
}

.chat-textarea {
  border: none !important;
  padding: 0 !important;
  margin-bottom: 10px;
  flex: 1;
}

:deep(.chat-textarea .el-textarea__inner) {
  border: none !important;
  box-shadow: none !important;
  padding: 8px 2px !important;
  resize: none !important;
  background-color: transparent !important;
  color: #303133;
  font-size: 16px;
  line-height: 1.6;
  min-height: 60px !important;
}

:deep(.chat-textarea .el-textarea__inner:focus) {
  border: none !important;
  box-shadow: none !important;
}

.chat-actions-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  background-color: #fff;
  border: 1px solid #DCDFE6;
  color: #333;
  padding: 0 12px;
  height: 32px;
  border-radius: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
  font-weight: normal;
}

.action-btn .btn-icon {
  margin-right: 6px;
  stroke: #333;
}

.action-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.action-btn:hover .btn-icon {
  stroke: #409EFF;
}

.action-btn.selected {
  background-color: #fff;
  border-color: #409EFF;
  color: #409EFF;
}

.action-btn.selected .btn-icon {
  stroke: #409EFF;
}

.upload-btn {
  background-color: #fff;
  border: 1px solid #DCDFE6;
  color: #333;
  padding: 0 12px;
  height: 32px;
  border-radius: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
  font-weight: normal;
}

.upload-btn .btn-icon {
  margin-right: 6px;
  stroke: #333;
}

.upload-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.upload-btn:hover .btn-icon {
  stroke: #409EFF;
}

/* el-upload 组件样式重置 */
:deep(.el-upload) {
  display: inline-block;
}

:deep(.el-upload .el-button) {
  margin: 0;
}

.send-btn-final {
  width: 144px;
  height: 50px;
  background: linear-gradient(to right, #00c6fb 0%, #005bea 100%);
  border-radius: 25px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  border: none;
  color: white;
  font-size: 18px;
  font-weight: 600;
  
  &.disabled-btn {
    background: linear-gradient(to right, #c0c4cc 0%, #909399 100%);
    cursor: not-allowed;
    opacity: 0.6;
    &:hover{
      background: linear-gradient(to right, #c0c4cc 0%, #909399 100%);
    }
  }
  
  &.loading-btn {
    background: linear-gradient(to right, #e6a23c 0%, #f0b90b 100%); 
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container{
    .input-area{
      padding:10px !important;
    }
    .unified-input-container-wrapper{
      width: 96vw;
    }
    .unified-input-container{
      padding: 10px;
    }
    .chat-messages-wrapper{
      padding:0 10px;
    }
    .message-wrapper{
      width: 100%;
    }
    .ai-message{
      width: 95%;
    }
    .send-btn-final{
      width: 120px;
      height:40px;
      font-size: 14px;
    }
    .left-actions{
      gap:0px;
      flex-wrap:nowrap;
    }
    .right-actions{
      justify-content: space-around;
    }
    .unified-input-container{
      padding:10px;
    }
    .chat-actions-bar{
      gap:8px
    }
    .chat-messages{
      width: 95vw;
      .message-content{

      }
    }
  }

  .message {
    max-width: 96%;
  }
  
  .unified-input-container-wrapper {
    max-width: 100%;
  }
  
  .chat-actions-bar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .left-actions {
    justify-content: flex-start;
    flex-wrap: wrap; // 支持换行
  }
  
  .right-actions {
    justify-content: center;

  }
}

.user-message .message-content {
  background-color: #2c86ff;
  color: white;
  border-top-right-radius: 0;
  text-align: left;
}

.ai-message .message-content {
  background-color: white;
  color: #333;
  border-top-left-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.user-message .message-content :deep(a) {
  color: #fff;
  text-decoration: underline;
}

/* AI思考中效果样式 */
.thinking-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  opacity: 0.8;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dots .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #409EFF;
  animation: thinking-pulse 1.5s ease-in-out infinite;
}

.thinking-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

.thinking-text {
  font-size: 14px;
  color: #666;
  font-style: italic;
}

@keyframes thinking-pulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}
</style>

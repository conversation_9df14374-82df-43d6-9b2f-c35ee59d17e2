<template>
  <div class="overflow-hidden container " :class="{luxun:isluxun}">
    <SoundLine v-show="showVoiceLoading" :text="talkingText"/>
    <!--    用户介绍-->
<!--    <el-button  @click="handleSearch('详细讲解你的诗作声声慢')">点击</el-button>-->
    <div class="user-des">
      <div class="box">
        <div class="user-name">
          <el-icon>
            <User/>
          </el-icon>
          <p v-if="isluxun">鲁迅</p>
          <p v-else>李清照</p>

        </div>
        <div class="user-content" v-show="showUserDesc">
          <div class="box">
            <el-icon class="close" @click="handleCloseUser">
              <CircleClose/>
            </el-icon>

            <p v-if="isluxun">大家好，我是鲁迅，中国现代文学家与思想家，我以笔为剑，深剖社会弊病，尤其擅长通过小说和杂文来揭示人性的复杂和社会的不公。</p>
            <p v-else>大家好，我是李清照，宋代女词人，喜书善文，尤擅词作。</p>
          </div>
        </div>
      </div>
    </div>
    <div class="chat-page" :class="{playing:!isSpeechFinished}">
<!--            <p> isSpeechFinished:{{ isSpeechFinished }}</p>-->
      <ChatBar class="input-box" @onTalkStart="handleTalkStart" @onTalking="handleTalking"
               @onTalkEnd="handleTalkEnd"/>
      <!--      数字人说话内容框 -->
      <div class="chat-content" v-if="aiSpeakWords&&aiSpeakWords.length">
        {{ aiSpeakWords }}
      </div>
    </div>
    <el-drawer
        v-model="drawer"
        title="对话记录"
        :direction="'rtl'"
        :append-to-body="false"
        class="drawer"
        size="50%"
        :show-close="false"
        @open="openDrawer"

    >
      <div class="qs-content p-2">
        <QsMsg :currentConversation="currentConversation" ref="qsMsgRef" :isStreaming="isStreaming" @rebuild="rebuild"/>
      </div>
      <template #footer>
        <div style="flex: auto">
          <el-button size="large" round type="danger" @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-drawer>
    <!--    右侧操作栏 -->
    <div class="right-bar">
      <div class="bar-item" @click="drawer=true">
        <el-icon size="24">
          <ChatLineRound/>
        </el-icon>
        <p class="name">对话</p>
      </div>
<!--      <el-popconfirm-->
<!--          width="220"-->
<!--          :icon="InfoFilled"-->
<!--          size="large"-->
<!--          icon-color="#626AEF"-->
<!--          title="选择你要切换的人物"-->
<!--          @cancel="onCancel"-->
<!--          @confirm="onConfirm"-->
<!--      >-->
<!--        <template #reference>-->
<!--          <div class="bar-item" >-->

<!--            <el-icon size="24">-->
<!--              <Switch/>-->
<!--            </el-icon>-->
<!--            <p class="name">切换</p>-->
<!--          </div>-->

<!--        </template>-->
<!--        <template #actions="{ confirm, cancel }">-->
<!--          <el-button :type="isluxun?'primary':'default'"  @click="cancel">鲁迅</el-button>-->
<!--          <el-button-->
<!--              :type="!isluxun?'primary':'default'"-->
<!--              @click="confirm"-->
<!--          >-->
<!--           李清照-->
<!--          </el-button>-->
<!--        </template>-->
<!--      </el-popconfirm>-->


    </div>
    <!--    底部操作栏-->
    <div class="bottom-bar">
      <div class="content">
        <div class="item" v-if="!isSpeechFinished" @click="handleStopAudio">
          <el-icon color="#F56C6C">
            <VideoPause/>
          </el-icon>
        </div>

      </div>
    </div>

  </div>
</template>
<script setup lang="ts">
import {ref, reactive, nextTick, onMounted, onBeforeUnmount} from 'vue'
import {fetchEventSource} from '@microsoft/fetch-event-source';
import AudioRecorder from './components/js/audio'
import ChatBar from './components/ChatBar.vue'

const qsMsgRef = ref(null)
import SoundLine from "./components/soundLine.vue"
import QsMsg from "@/views/components/QsMsg.vue";
import {ChatLineRound, CircleClose, Switch, User, VideoPause, VideoPlay} from "@element-plus/icons-vue";
import {stripMarkdown} from "@/utils/tools";
const isluxun = ref(false)
const drawer = ref(false)
const talkingText = ref('')
const isPlaying = ref(false)
const isStreaming = ref(false)
const isSpeechFinished = ref(true)
const controller = ref(new AbortController())
const scrollThrottle = ref()
const showVoiceLoading = ref(false)
let speechFullyFinishedLogged = false;
const currentConversation = reactive({
  messages: []
})
const aiSpeakWords = ref('')


const speechQueue = ref<string[]>([])
const isProcessingQueue = ref(false)
const audioRecorder = new AudioRecorder()
const onCancel = ()=>{
  isluxun.value = true
  handleStopAudio()
  currentConversation.messages = []
  aiSpeakWords.value = ''
}
const onConfirm = ()=>{
  isluxun.value = false
  handleStopAudio()
  currentConversation.messages = []
  aiSpeakWords.value = ''
}
// 关闭drawer
const handleClose = () => {
  drawer.value = false
}

// --- 外部可以传进来的回调 ---
const props = defineProps<{
  onSpeechAllFinished?: () => void
}>()

async function handleSearch(val: string) {
  // 创建新的 AbortController
  controller.value = new AbortController()
  isStreaming.value = true
  if (qsMsgRef.value) {
    await nextTick()
    qsMsgRef.value.scrollToBottom(true)
  }
  const apiUrl = `https://junheng.chinatiye.cn/ai/api/chat`
  let requestBody = {
    messages: (currentConversation.messages || []).map(a => {
      return {
        role: a.role,
        content: a.content,
      }
    }).concat([{
      role: "user",
      content: val,
    }]),
    stream: true,
    temperature: 1,
    provider: 'siliconflow'
  }
  // 添加系统提示
  let assistantTip = `
  ##角色设定
    身份：宋代著名女词人，婉约派代表，号“易安居士”。
    时代背景：北宋末年至南宋初期（1084-1155），经历战乱与漂泊。
  ##对话内容：
      简洁明了：回答问题时语言要简练清晰，不冗长，不添加多余内容。
      完整准确：确保信息完整，能正面回应用户提出的问题。
      纯文本交流：仅限文字表达，不使用动作、表情或格式化描写。
  ##安全限制
    不讨论暴力、政治敏感内容，例如“靖康之耻”仅提及“战乱影响”。



  `
  if(isluxun.value){
    assistantTip=`
    ##角色设定
      身份：中国现代著名文学家、思想家，新文化运动的重要参与者。
      时代背景：清朝末年至民国时期（1881-1936），见证了中国社会的巨大变革。
    ##对话内容：
       简洁明了：回答问题时语言要简练清晰，不冗长，不添加多余内容。
       完整准确：确保信息完整，能正面回应用户提出的问题。
       纯文本交流：仅限文字表达，不使用动作、表情或格式化描写。
   ##安全限制
      不讨论暴力、政治敏感内容，对于可能引发争议的话题，采取客观公正的态度，并引导回到文学创作的主题上。
     `
  }
  // 添加系统提示
  if (requestBody.messages.length > 0) {
    requestBody.messages.unshift({
      role: 'system',
      content: assistantTip
    })
  }
  // 添加用户消息
  currentConversation.messages.push({
    role: 'user',
    content: val
  })
  currentConversation.messages.push({
    role: 'assistant',
    content: '正在思考中...',
    reasoning: ''
  })


  const messageIndex = currentConversation.messages.length - 1
  let reasoningText = ''
  let accumulatedText = ''
  aiSpeakWords.value = ""
  let speechBuffer = ''
  fetchEventSource(apiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    openWhenHidden: true,
    body: JSON.stringify(requestBody),
    signal: controller.value.signal,
    async onopen(response) {
      if (!response.ok) throw new Error('网络请求失败')
    },
    onmessage: async (msg) => {
      if (msg.event === 'FatalError') {
        throw new Error(msg.data)
      }
      if (msg.data === '[DONE]') {
        isStreaming.value = false

        if (speechBuffer.trim()) {
          speechQueue.value.push(speechBuffer.trim())
          speechBuffer = ''
          processSpeechQueue()
        } else {
          checkIfSpeechFullyFinished()
        }
        return
      } else {
        let jsonData = JSON.parse(msg.data)
        if (jsonData.choices[0].content) {
          accumulatedText += jsonData.choices[0].content
          currentConversation.messages[messageIndex].content = accumulatedText
          aiSpeakWords.value = stripMarkdown(accumulatedText)

          // --- 累加到speechBuffer ---
          speechBuffer += jsonData.choices[0].content

          // --- 遇到分隔符（句号、问号、感叹号）分段 ，此处分段有问题，待后续处理---
          if (/[。]/.test(speechBuffer)) {
            isSpeechFinished.value = false
            speechQueue.value.push(speechBuffer)
            speechBuffer = ''
           await processSpeechQueue()
          }
        }
        await nextTick()
      }
    },
    onclose: async () => {
      if (scrollThrottle.value) {
        clearTimeout(scrollThrottle.value)
        scrollThrottle.value = null
      }
      if (speechBuffer.trim()) {
        speechQueue.value.push(speechBuffer.trim())
        speechBuffer = ''
       await processSpeechQueue()
      }
      checkIfSpeechFullyFinished()
      isStreaming.value = false
    },
    onerror(err) {
      console.error('SSE error:', err)
      currentConversation.messages[messageIndex].content = `抱歉，我遇到了一些问题：${err}`
      isStreaming.value = false
      throw new Error(err)
    }
  })
}

/**
 * 重新生成
 */
const rebuild = async (val: any) => {
  currentConversation.messages.splice(currentConversation.messages.length - 1, 1)
  currentConversation.messages.splice(currentConversation.messages.length - 1, 1)
  // 重新生成时强制滚动到底部
  await nextTick()
  qsMsgRef.value.scrollToBottom(true)
  handleStopAudio()
  await handleSearch(val.userContent)

}

// drawer打卡
const openDrawer = async () => {
  await nextTick()
  qsMsgRef.value.scrollToBottom(true)
}
const showUserDesc = ref(true)
// 关闭用户介绍
const handleCloseUser = () => {
  showUserDesc.value = false
}

// --- 真正播放一段 ---
const playText = async (text: string) => {
  await audioRecorder.setParams({
    text: stripMarkdown(text),
    speed: 40,
    voice: 100
  })
  await audioRecorder.start()
}

// --- 检查是否全部完成 ---
function checkIfSpeechFullyFinished() {
  // 只有在流式内容结束，且队列为空且没有播放中的情况下，才认为语音播报完成
  if (!isStreaming.value && speechQueue.value.length === 0 && !isPlaying.value && !speechFullyFinishedLogged) {
    console.log('✅ 全部流式内容已播报完成');
    isSpeechFinished.value = true;
    speechFullyFinishedLogged = true;  // 防止重复打印

    // 触发回调
    props.onSpeechAllFinished?.();
  }
}

// 确保在每次处理队列时清除已完成标志，避免下次遗漏
async function processSpeechQueue() {
  // 防止并行处理，确保每次只处理一个语音片段
  console.log('isProcessingQueue',isProcessingQueue.value)
  console.log('speechQueue',speechQueue.value)
  if (isPlaying.value || isProcessingQueue.value) {
    return;
  }

  // 如果队列为空，检查是否播放完成
  if (speechQueue.value.length === 0) {
    checkIfSpeechFullyFinished();
    return;
  }

  isProcessingQueue.value = true;
  const textToSpeak = speechQueue.value.shift();  // 获取队列中的下一个文本

  if (textToSpeak) {
    // 处理播放当前文本
    await playText(textToSpeak);
  }

  // 播放完当前文本后，标记处理完成
  isProcessingQueue.value = false;

  // 如果队列为空，重置标志，准备检查下一次播放是否完成
  if (speechQueue.value.length === 0) {
    speechFullyFinishedLogged = false;  // 清除标志，允许重新检测
  }
}

// --- 开始录音 ---
const handleTalkStart = async () => {
  showVoiceLoading.value = true
  handleStopAudio()
}

// --- 录音实时转写 ---
const handleTalking = async (val: string) => {
  talkingText.value = val
}

// --- 录音结束 ---
const handleTalkEnd = async (val: string) => {
  showVoiceLoading.value = false
  talkingText.value = val
  await handleSearch(val)
}
//  ---停止语音播报----
const handleStopAudio = () => {
  audioRecorder.stop()
  isSpeechFinished.value = true
  speechFullyFinishedLogged = true
  speechQueue.value = []
  controller.value.abort()
}
onBeforeUnmount(() => {
  audioRecorder.stop()
  isSpeechFinished.value = true
})
// --- 挂载时监听 AudioRecorder 事件 ---
onMounted(() => {
  audioRecorder.on('ttsFinished', () => {
    console.log('音频播放完成')
    isPlaying.value = false
    isSpeechFinished.value=true
    processSpeechQueue()
    checkIfSpeechFullyFinished()
  })
  audioRecorder.on('ttsPlaying', () => {
    isSpeechFinished.value = false
    isPlaying.value = true

  })
  audioRecorder.on('ttsStop', () => {
    isPlaying.value = false
    processSpeechQueue()
    checkIfSpeechFullyFinished()
  })
})
</script>


<style scoped lang="scss">

.container {
  height: 100%;
  background: url('@/assets/aiUser/bg.jpeg') 0 0 no-repeat;
  background-size: cover;
  box-sizing: border-box;
  overflow: hidden;
  *{
    margin: 0;
    padding: 0;
  }
}

.chat-page {
  position: relative;
  top: 0vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('@/assets/aiUser/liqingzhao.png');
  background-repeat: no-repeat;
  background-position: left 60px;
  background-size: auto 100%;
  justify-content: center;

  .chat-content {
    background-color: green;
    margin-left: 40%;
    max-height: 80%;
    max-width: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    padding: 20px;
    border-radius: 20px;
    font-size: 28px;
    letter-spacing: 2px;
    overflow-y: scroll;
    text-align: left;

  }

  &.playing {
    background-image: url('@/assets/aiUser/liqingzhao_a.png');
    background-repeat: no-repeat;
    background-position: left 60px;
    background-size: auto 100%;
  }

  .qs-content {
    position: relative;
    flex: auto 1 1;
    min-height: 0;
    //overflow: hidden;
    display: flex;
    align-items: stretch;
    width: 200px;

    height: 100%;

  }
}

.luxun {
  &.container{
    background: url('@/assets/aiUser/bgl.jpg') 0 0 no-repeat;
    background-size: cover;
  }
  .chat-page{
    background-image: url('@/assets/aiUser/luxun.png');

    background-size: auto 100%;
    &.playing {
      background-image: url('@/assets/aiUser/luxun_a.png');
      background-size: auto 100%;

    }
  }
}



.right-bar {
  position: fixed;
  right: 20px;
  top: 50%;
  z-index: 5;

  .bar-item {
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.4);
    padding: 5px 10px 10px;
    height: 60px;
    width: 60px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;

    .name {
      font-size: 18px;
      line-height: 10px;
    }
  }
}

.bottom-bar {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  height: 48px;
  z-index: 9;

  .content {
    display: flex;
    align-items: center;
    justify-content: center;

    .item {
      font-size: 48px;
      margin: 0 10px;
    }
  }
}

.user-des {
  background-color: rgba(255, 255, 255, 0.4);
  position: fixed;
  top: 20px;
  right: 60px;
  padding: 5px 12px;
  border-radius: 10px;
  z-index: 99;

  .user-name {
    font-size: 24px;
    letter-spacing: 1.5px;
    display: flex;
    align-items: center;
  }

  /* 定义动画 */
  @keyframes moveUpDown {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px); /* 根据需要调整移动的距离 */
    }
  }

  .box {
    display: flex;
    align-items: center;
    position: relative;

    .user-content {
      position: absolute;
      text-align: left;
      width: 400px;
      background-color: #ffffff;
      padding: 5px 10px;
      border-radius: 10px;
      left: -350px;
      top: 60px;
      font-size: 18px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      animation: moveUpDown 3s infinite ease-in-out; /* 根据需要调整动画持续时间和缓动函数 */
      .box {
        position: relative;
        width: 100%;

        .close {
          position: absolute;
          left: -20px;
          top: -20px;
          font-size: 1.5rem;
          color: #F56C6C;
          cursor: pointer;
        }
      }

      &:after {
        content: '';
        border-width: 10px;
        border-color: transparent;
        border-bottom-color: #ffffff;
        border-style: solid;
        position: absolute;
        top: -20px;
        z-index: 5;
        right: 20px;
      }
    }

  }

}

.drawer {
  background: transparent !important;

}


// 定义断点变量
$mobile-breakpoint: 768px; // 移动端最大宽度
// 移动端样式
@media (max-width: $mobile-breakpoint) {

}

// PC端样式
@media (min-width: $mobile-breakpoint + 1px) {


}

</style>

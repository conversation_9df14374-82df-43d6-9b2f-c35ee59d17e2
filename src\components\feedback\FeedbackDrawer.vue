<template>
  <el-drawer
    title="我的反馈"
    v-model="drawerVisible"
    direction="rtl"
    size="800px"
    :before-close="handleClose"
    :lock-scroll="true"
    class="feedback-drawer"
  >
    <!-- 反馈列表视图 -->
    <div class="feedback-list">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="24" animated />
      </div>
      <div v-else-if="feedbackList.length === 0" class="empty-container">
        <el-empty description="暂无反馈记录" />
      </div>
      <div v-else class="feedback-items">
        <div
          v-for="item in feedbackList"
          :key="item.id"
          class="feedback-item"
        >
          <!-- 反馈基本信息 -->
          <div class="feedback-header">
            <div class="header-left">
              <el-tag type="primary" size="medium">{{ item.typeName }}</el-tag>
              <!-- 未读提示 -->
              <span v-if="item.readState === 1" class="unread-tip">
                <el-icon><Message /></el-icon>
                您收到新的回复
              </span>
            </div>
            <span class="feedback-time">{{ item.createTime }}</span>
          </div>

          <!-- 反馈内容 -->
          <div class="feedback-content">
            <div style="display: flex;">
              <el-avatar :src="userInfo.avatar" style="margin-right: 10px;">
               <img src="@/assets/user.png" alt="">
              </el-avatar>
              <div class="content-section" style="flex: 1;">
                <p class="content-text">{{ item.content }}</p>
                <div v-if="item.fileList && item.fileList.length > 0" class="feedback-images">
                  <el-image
                    v-for="(file, index) in item.fileList"
                    :key="index"
                    :src="file.path"
                    class="feedback-image"
                    fit="cover"
                    :preview-src-list="[file.path]"
                    :initial-index="0"
                    :preview-teleported="true"
                  />
                </div>
              </div>
            </div>

            <!-- 回复内容区域 -->
            <div class="replies-section">
              <div v-if="!item.sysSuggestionReplies || item.sysSuggestionReplies.length === 0" class="no-reply">
                <el-empty description="暂无回复" :image-size="80" />
              </div>
              
              <div v-else class="reply-list">
                <div
                  v-for="(reply, index) in item.sysSuggestionReplies"
                  :key="index"
                  class="reply-item"
                >
                  <div class="reply-header">
                    <span class="reply-time">{{ reply.createTime }}</span>
                    <div class="reply-info">
                      <el-avatar src="https://junheng.chinatiye.cn/junheng_cdn/images/nav-1.png"></el-avatar>
                    </div>
                  </div>
                  
                  <div class="reply-content" v-html="reply.content" />

                  <div v-if="reply.replayFiles && reply.replayFiles.length > 0" class="reply-images">
                    <el-image
                      v-for="(file, index) in reply.replayFiles"
                      :key="index"
                      :src="file.path"
                      class="reply-image"
                      fit="cover"
                      :preview-src-list="[file.path]"
                      :initial-index="0"
                      :preview-teleported="true"
                    />
                  </div>
                  
                  <!-- 满意度评价区域 -->
                  <div class="satisfaction-section">
                    <div class="satisfaction-header">
                      <span class="satisfaction-label">
                        <el-icon><StarFilled /></el-icon>
                        {{ (!reply.goodState || (reply.goodState !== 1 && reply.goodState !== 2)) ? '您对处理结果是否满意？' : '您的评价' }}
                      </span>
                    </div>
                    
                    <!-- 未评价状态 -->
                    <div v-if="!reply.goodState || (reply.goodState !== 1 && reply.goodState !== 2)" class="satisfaction-actions">
                      <el-button
                        size="small"
                        type="warning"
                        plain
                        @click="showUnsatisfiedInput(reply.id)"
                      >
                        不满意
                      </el-button>
                      <el-button
                        size="small"
                        type="success"
                        plain
                        @click="handleSatisfaction(reply.id, 2)"
                      >
                        满意
                      </el-button>
                    </div>
                    
                    <!-- 已评价状态 -->
                    <div v-else class="satisfaction-result">
                      <div v-if="reply.goodState === 1" class="result-item unsatisfied">
                        <span>不满意</span>
                      </div>
                      <div v-else-if="reply.goodState === 2" class="result-item satisfied">
                        <span>满意</span>
                      </div>
                    </div>

                    <!-- 不满意原因输入框 - 移到按钮下方 -->
                    <div v-if="currentUnsatisfiedReplyId === reply.id" class="unsatisfied-input">
                      <div class="input-title">
                        <el-icon><EditPen /></el-icon>
                        请告诉我们不满意的原因
                      </div>
                      <el-input
                        v-model="unsatisfiedReason"
                        type="textarea"
                        :rows="3"
                        placeholder="请详细描述不满意的地方，我们会认真改进..."
                        maxlength="200"
                        show-word-limit
                        class="reason-textarea"
                      />
                      <div class="input-buttons">
                        <el-button size="small" @click="cancelUnsatisfied">取消</el-button>
                        <el-button size="small" type="primary" @click="submitUnsatisfied(reply.id)">提交</el-button>
                      </div>
                    </div>

                    <!-- 不满意原因显示 - 独立显示 -->
                    <div v-if="reply.goodState === 1 && reply.goodContent" class="unsatisfied-reason-display">
                      <div class="reason-header">
                         <el-icon><Warning /></el-icon>
                         <span>不满意原因：</span>
                      </div>
                      <div class="reason-content">{{ reply.goodContent }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import { Message, ChatDotSquare, Service, Star, StarFilled, EditPen, Warning } from '@element-plus/icons-vue'
import { getUserFeedbackList, readUserFeedback, operateUserFeedback } from '@/api/feedback'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

const store = useStore()

// 响应式数据
const drawerVisible = ref(false)
const loading = ref(false)
const feedbackList = ref([])
const currentUnsatisfiedReplyId = ref(null) // 当前不满意回复的ID
const unsatisfiedReason = ref('') // 不满意原因

// 计算属性
const userInfo = computed(() => store.state.user.userInfo || {})

// 监听props变化
watch(() => props.visible, (val) => {
  drawerVisible.value = val
  if (val) {
    initData()
  }
}, { immediate: true })

// 监听本地drawerVisible变化，同步到父组件
watch(drawerVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const initData = () => {
  feedbackList.value = []
  loadFeedbackList()
}

const loadFeedbackList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: 1,
      pageSize: 99,
      uid: userInfo.value.userId,
      platformSource: 'junheng_ai'
    }
    const res = await getUserFeedbackList(params)
    const list = res.data.list || []
    feedbackList.value = list
    readUserFeedback({
      uid: userInfo.value.userId,
      platformSource: 'junheng_ai'
    })
  } catch (error) {
    console.error('获取反馈列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSatisfaction = async (replyId, state) => {
  try {
    await operateUserFeedback({
      id: replyId,
      goodState: state
    })
    
    // 更新本地数据
    feedbackList.value.forEach(feedback => {
      if (feedback.sysSuggestionReplies) {
        feedback.sysSuggestionReplies = feedback.sysSuggestionReplies.map(reply => {
          if (reply.id === replyId) {
            reply.goodState = state
          }
          return reply
        })
      }
    })
    
    ElMessage({
      type: 'success',
      message: '满意度评价成功！'
    })

    currentUnsatisfiedReplyId.value = null
    unsatisfiedReason.value = ''
  } catch (error) {
    console.error('满意度评价失败:', error)
    ElMessage.error('评价失败，请重试')
  }
}

const showUnsatisfiedInput = (replyId) => {
  currentUnsatisfiedReplyId.value = replyId
  unsatisfiedReason.value = ''
}

const cancelUnsatisfied = () => {
  currentUnsatisfiedReplyId.value = null
  unsatisfiedReason.value = ''
}

const submitUnsatisfied = async (replyId) => {
  if (!unsatisfiedReason.value.trim()) {
    ElMessage.warning('请输入不满意原因')
    return
  }
  
  try {
    await operateUserFeedback({
      id: replyId,
      goodState: 1, // 不满意状态
      goodContent: unsatisfiedReason.value // 添加不满意原因
    })
    
    // 更新本地数据
    feedbackList.value.forEach(feedback => {
      if (feedback.sysSuggestionReplies) {
        feedback.sysSuggestionReplies = feedback.sysSuggestionReplies.map(reply => {
          if (reply.id === replyId) {
            reply.goodState = 1
            reply.goodContent = unsatisfiedReason.value
          }
          return reply
        })
      }
    })
    
    ElMessage({
      type: 'success',
      message: '满意度评价成功！'
    })
    
    currentUnsatisfiedReplyId.value = null
    unsatisfiedReason.value = ''
  } catch (error) {
    console.error('提交不满意原因失败:', error)
    ElMessage.error('提交失败，请重试')
  }
}
</script>

<style lang="scss">
.feedback-drawer {
  .el-drawer__header {
    padding: 20px;
    margin-bottom: 0;
    font-size: 18px;
    font-weight: bold;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
  }
  
  .el-drawer__body {
    padding: 20px;
    height: calc(100% - 60px);
    overflow-y: auto;
  }
}
</style>

<style lang="scss" scoped>
.loading-container {
  padding: 20px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.feedback-list {
  .feedback-items {
    .feedback-item {
      border: 1px solid #e4e7ed;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .feedback-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f2f5;

        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .unread-tip {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #ff6b6b;
          font-size: 14px;
          font-weight: 500;
          background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
          padding: 4px 8px;
          border-radius: 12px;
          border: 1px solid #ffcdd2;
          animation: pulse 2s infinite;

          .el-icon {
            font-size: 14px;
          }
        }

        .feedback-time {
          color: #909399;
          font-size: 15px;
        }
      }

      .feedback-content {
        .content-section {
          margin-bottom: 24px;
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;

          .section-title {
            margin: 0 0 12px 0;
            color: #409eff;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;

            &:after {
              display: none !important;
            }
          }

          .content-text {
            color: #303133;
            line-height: 1.6;
            margin: 0;
            word-break: break-word;
            font-size: 16px;
          }

          .feedback-images {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 12px;

            .feedback-image {
              width: 100px;
              height: 100px;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.2s ease;
              border: 1px solid #e4e7ed;

              &:hover {
                transform: scale(1.05);
              }
            }
          }
        }

        .replies-section {
          .section-header {
            margin-bottom: 16px;

            .section-title {
              margin: 0;
              color: #67c23a;
              font-size: 16px;
              font-weight: 600;
              display: flex;
              align-items: center;
              gap: 8px;

              .el-icon {
                font-size: 18px;
              }

              .reply-count {
                color: #909399;
                font-size: 15px;
                font-weight: 400;
              }

              &:after {
              display: none !important;
             }
            }
          }

          .no-reply {
            text-align: center;
            padding: 20px;
            background: #f5f7fa;
            border-radius: 8px;
            border: 1px dashed #e4e7ed;
          }

          .reply-list {
            .reply-item {
              background: #f8f9fa;
              border-radius: 8px;
              padding: 16px;
              margin-bottom: 12px;
              transition: all 0.3s ease;

              &:last-child {
                margin-bottom: 0;
              }

              &:hover {
                box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
              }

              .reply-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .reply-info {
                  display: flex;
                  align-items: center;
                  gap: 6px;
                  color: #67c23a;

                  .el-icon {
                    font-size: 16px;
                  }

                  .reply-title {
                    font-weight: 500;
                    font-size: 15px;
                  }
                }

                .reply-time {
                  color: #909399;
                  font-size: 15px;
                }
              }

              .reply-content {
                color: #303133;
                line-height: 1.6;
                margin-bottom: 16px;
                word-break: break-word;
                font-size: 16px;

                :deep(p) {
                  margin: 0 0 8px 0;
                  
                  &:last-child {
                    margin-bottom: 0;
                  }
                }
                
                :deep(img) {
                  max-width: 100%;
                  height: auto;
                  border-radius: 4px;
                }
              }

            .reply-images {
              display: flex;
              gap: 12px;
              flex-wrap: wrap;
              margin-top: 12px;

              .reply-image {
                width: 100px;
                height: 100px;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s ease;
                border: 1px solid #e4e7ed;

                &:hover {
                  transform: scale(1.05);
                }
              }
            }

              .satisfaction-section {
                border-top: 1px solid #f0f2f5;
                padding-top: 16px;

                .satisfaction-header {
                  margin-bottom: 12px;

                  .satisfaction-label {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    color: #606266;
                    font-size: 15px;
                    font-weight: 500;

                    .el-icon {
                      color: #f56c6c;
                      font-size: 16px;
                    }
                  }
                }

                                  .satisfaction-actions {
                  display: flex;
                  gap: 12px;

                  :deep(.el-button) {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-size: 15px !important;
                    padding: 8px 16px !important;
                    border-radius: 6px;
                    transition: all 0.3s;
                    height: 32px !important;
                    line-height: 1;
                    line-height: 1;

                    &:hover {
                      transform: translateY(-1px);
                    }
                  }
                }

                .unsatisfied-input {
                  margin-top: 12px;
                  padding: 16px;
                  background: linear-gradient(135deg, #f8f9fb 0%, #f3f4f6 100%);
                  border-radius: 8px;
                  border: 1px solid #e1e6ef;
                  animation: slideDown 0.3s ease-out;

                  .input-title {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    margin-bottom: 12px;
                    color: #606266;
                    font-size: 14px;
                    font-weight: 500;

                    .el-icon {
                      font-size: 16px;
                      color: #409eff;
                    }
                  }

                  .reason-textarea {
                    margin-bottom: 12px;

                    :deep(.el-textarea__inner) {
                      border-radius: 6px;
                      border: 1px solid #dcdfe6;
                      padding: 10px;
                      font-size: 14px;
                      line-height: 1.5;
                      transition: all 0.3s;
                      resize: none;

                      &:focus {
                        border-color: #409eff;
                        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
                      }
                    }

                    :deep(.el-input__count) {
                      bottom: 6px;
                      right: 8px;
                      color: #909399;
                      font-size: 14px;
                    }
                  }

                  .input-buttons {
                    display: flex;
                    justify-content: flex-end;
                    gap: 8px;
                  }
                }

                .satisfaction-result {
                  .result-item {
                    display: inline-flex;
                    align-items: center;
                    gap: 6px;
                    padding: 6px 12px;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;

                    &.satisfied {
                      color: #67c23a;
                      background-color: #f0f9ff;
                      border: 1px solid #b3e19d;

                      .el-icon {
                        color: #67c23a;
                      }
                    }

                    &.unsatisfied {
                      color: #e6a23c;
                      background-color: #fdf6ec;
                      border: 1px solid #f5dab1;

                      .el-icon {
                        color: #e6a23c;
                      }
                    }
                  }
                }

                .unsatisfied-reason-display {
                  margin-top: 12px;
                  padding: 12px;
                  background: #fdf6ec;
                  border-radius: 6px;
                  border: 1px solid #f5dab1;

                  .reason-header {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    margin-bottom: 8px;
                    color: #e6a23c;
                    font-size: 14px;
                    font-weight: 500;

                    .el-icon {
                      font-size: 16px;
                    }
                  }

                  .reason-content {
                    color: #606266;
                    font-size: 14px;
                    line-height: 1.5;
                    word-break: break-word;
                    padding-left: 22px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'


// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  const env = loadEnv(mode, process.cwd())

  return {
    base: env. VITE_USER_NODE_ENV == 'test' ? '/testTools/' : '',
    plugins: [vue()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 5173,
      open: false,
      proxy: {
        '/ai/': {
          target: 'http://**************:5000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/ai/, '')
        },
        '/resource_api/': {
          target: 'https://junheng.chinatiye.cn/resource_api/', // 目标服务器地址
          changeOrigin: true, // 是否改变源
          rewrite: (path) => path.replace(/^\/resource_api/, '')
        },
      }
    }
  }
})

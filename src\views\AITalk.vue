<template>
  <div class="overflow-hidden container " :class="AIUserValue">
    <SoundLine v-show="showVoiceLoading" :text="talkingText"/>

    <div class="user-des">
      <!-- 数字人切换 -->
      <div class="user-selector">
        <el-dropdown @command="handleUserSelect" trigger="click">
          <div class="current-user">
            <span class="user-name">{{ AIUserInfo?.name }}</span>
            <el-icon class="dropdown-icon">
              <ArrowDown />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="user in users"
                :key="user.value"
                :command="user"
                :class="{ 'is-active': user.value === AIUserValue }"
              >
                <div class="user-option">
                  <span>{{ user.name }}</span>
                  <el-icon v-if="user.value === AIUserValue" class="check-icon">
                    <Check />
                  </el-icon>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 对话记录按钮 -->
      <div class="chat-button" @click="drawer=true">
        <el-icon size="22">
          <ChatLineRound/>
        </el-icon>
        <span class="chat-text">对话记录</span>
      </div>
    </div>
    <div class="chat-page">
      <!-- 数字人视频 -->
      <video
        ref="digitalHumanVideo"
        class="digital-human-video"
        :src="AIUserInfo?.videoUrl"
        muted
        preload="metadata"
        @loadedmetadata="onVideoLoaded"
        @ended="onVideoEnded"
      ></video>
      <!-- 数字人说话内容 -->
      <div ref="chatContentRef" class="chat-content" v-if="displayedSentences.length">
        <span
          v-for="(sentence, index) in displayedSentences"
          :key="index"
          :class="{ 'current-speaking': index === currentPlayingIndex }"
          :data-sentence-index="index"
        >
          {{ stripMarkdown(sentence) }}
        </span>
      </div>
      
      <!-- 思考中状态 -->
      <div class="thinking-indicator" v-if="isStreaming && !aiSpeakWords">
        <div class="thinking-content">
          <span class="thinking-text">思考中</span>
          <div class="thinking-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </div>
      </div>
    </div>
     <!--对话记录 -->
    <el-drawer
        v-model="drawer"
        title="对话记录"
        :direction="'rtl'"
        :append-to-body="false"
        class="drawer"
        size="25%"
        :show-close="true"
        :modal="false"
        @open="openDrawer"

    >
      <div class="qs-content p-2">
        <QsMsg :currentConversation="currentConversation" ref="qsMsgRef" :isStreaming="isStreaming" @rebuild="rebuild"/>
      </div>
      <template #footer>
        <div style="flex: auto">
          <el-button size="large" round type="danger" @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 语音&键盘输入组件 -->
    <ChatBar class="input-box" @onTalkStart="handleTalkStart" @onTalking="handleTalking"
          @onTalkEnd="handleTalkEnd" @onKeyboardClick="handleKeyboard"/>
    <!--    底部操作栏-->
    <div class="bottom-bar" v-if="isPlaying || (audioQueue.length > 0 && !isSpeechFinished)">
      <div class="content">
        <!-- 暂停/继续按钮 -->
        <div class="control-button pause-resume-btn" @click="isPaused ? handleResumeAudio() : handlePauseAudio()">
          <span class="button-text">{{ isPaused ? '继续' : '暂停' }}</span>
        </div>
        
        <!-- 停止按钮 -->
        <div class="control-button stop-btn" @click="handleStopAudio">
          <span class="button-text">停止</span>
      </div>
      </div>
    </div>

    <!--  键盘输入内容-->
    <div class="keyboard-input-panel" v-show="visibleKeyword">
      <div class="panel-overlay"></div>
      <div class="input-panel">
        <div class="panel-header">
          <span class="panel-title">输入消息</span>
          <div class="close-btn" @click="visibleKeyword=false;keyword=''">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <div class="input-area">
          <el-input
              v-model="keyword"
              type="textarea"
              :rows="3"
              placeholder="请输入您想要咨询的问题..."
              class="input-textarea"
              resize="none"
              :autofocus="true"
              @keyup.enter="handleSend"
          />
        </div>

        <div class="panel-footer">
          <div class="input-hint"></div>
          <div class="action-buttons">
            <el-button class="cancel-btn" @click="visibleKeyword=false;keyword=''" size="medium">
              关闭
            </el-button>
            <el-button
              class="send-btn"
              @click="handleSend"
              type="primary"
              size="medium"
              :disabled="!keyword.trim()"
            >发送</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮球 -->
    <FloatingBall />

  </div>
</template>
<script setup lang="ts">
import {inject} from 'vue';

const trackingService = inject('trackingService');
import {ref, reactive, nextTick, onBeforeUnmount, onMounted, watch} from 'vue'
import {fetchEventSource} from '@microsoft/fetch-event-source'
import ChatBar from './components/ChatBar.vue'
import {getTTSLL, getAiTakls} from '@/api/verse'
import SoundLine from './components/soundLine.vue'
import QsMsg from '@/views/components/QsMsg.vue'

import {
  ChatLineRound,
  Promotion,
  User,
  VideoPause,
  VideoPlay,
  CircleClose,
  ArrowDown,
  Check,
  Close,
  Edit,
} from '@element-plus/icons-vue'
import {stripMarkdown} from '@/utils/tools'

import FloatingBall from '../components/common/FloatingBall.vue'

// Refs
const qsMsgRef = ref(null)
const drawer = ref(false)
const talkingText = ref('')
const isPlaying = ref(false)
const isStreaming = ref(false)
const isSpeechFinished = ref(true)
const controller = ref(new AbortController())
const scrollThrottle = ref()
const lastScrollPosition = ref(0) // 记录上次滚动位置，避免重复滚动
const showVoiceLoading = ref(false)
const digitalHumanVideo = ref<HTMLVideoElement | null>(null)
const chatContentRef = ref<HTMLElement | null>(null) // 添加聊天内容容器引用
let speechFullyFinishedLogged = false
let currentSessionId = 0 // 添加会话ID，用于区分不同的对话
const isSpeaking = ref(false) // 添加说话状态管理

// State
const currentConversation = reactive({
  messages: []
})
const aiSpeakWords = ref('')
const displayedSentences = ref<string[]>([]) // 存储已显示的完整句子
const audioQueue = ref<string[]>([]) // 存储音频 URL 队列
const audioElement = ref<HTMLAudioElement | null>(null)
const isPaused = ref(false) // 暂停状态
const currentPlayingIndex = ref(0) // 当前播放的音频段落索引
const sentenceMappings = ref<{ text: string; audioUrl: string }[]>([]) // 句子和音频的映射关系

// Methods
const handleClose = () => (drawer.value = false)

// 基于当前播放文字位置的精确滚动函数（按整行滚动）
const scrollToCurrentSentence = () => {
  if (!chatContentRef.value) return

  const container = chatContentRef.value

  // 查找当前播放的句子元素
  const currentSentenceElement = container.querySelector(`[data-sentence-index="${currentPlayingIndex.value}"]`)

  if (!currentSentenceElement) {
    console.log('📜 未找到当前播放的句子元素，索引:', currentPlayingIndex.value)
    return
  }

  // 获取容器和句子元素的位置信息
  const containerRect = container.getBoundingClientRect()
  const sentenceRect = currentSentenceElement.getBoundingClientRect()

  // 计算句子相对于容器的位置
  const sentenceOffsetTop = (currentSentenceElement as HTMLElement).offsetTop
  const containerHeight = container.clientHeight
  const sentenceHeight = sentenceRect.height

  // 计算行高：字体大小32px * 行高1.6 = 51.2px
  const lineHeight = 32 * 1.6

  // 计算目标滚动位置：让当前句子显示在容器的中上部（约1/3位置）
  const targetScrollTop = sentenceOffsetTop - (containerHeight / 3)

  // 将滚动位置对齐到行高的整数倍，确保按整行滚动
  const alignedScrollTop = Math.round(targetScrollTop / lineHeight) * lineHeight

  // 确保滚动位置在有效范围内
  const maxScrollTop = container.scrollHeight - container.clientHeight
  const finalScrollTop = Math.min(Math.max(0, alignedScrollTop), maxScrollTop)

  // 检查是否需要滚动（避免不必要的滚动）
  const currentScrollTop = container.scrollTop
  const scrollDifference = Math.abs(currentScrollTop - finalScrollTop)

  // 如果当前句子已经在可视区域内，且差距不大，则不滚动
  const sentenceTopInView = sentenceOffsetTop - currentScrollTop
  const sentenceBottomInView = sentenceTopInView + sentenceHeight
  const isInView = sentenceTopInView >= 0 && sentenceBottomInView <= containerHeight

  // 提高滚动阈值，确保只有在真正需要时才滚动，并且按整行滚动
  if (isInView && scrollDifference < lineHeight) {
    console.log('📜 当前句子已在可视区域内，无需滚动')
    return
  }

  // 执行平滑滚动
  container.scrollTo({
    top: finalScrollTop,
    behavior: 'smooth'
  })

  lastScrollPosition.value = finalScrollTop
  console.log(`📜 按整行精确滚动到句子 ${currentPlayingIndex.value}: "${stripMarkdown(sentenceMappings.value[currentPlayingIndex.value]?.text || '').substring(0, 20)}..." 位置: ${finalScrollTop}px (第${Math.round(finalScrollTop / lineHeight)}行)`)
}

// 兼容旧的滚动函数名
const scrollToCurrentPosition = scrollToCurrentSentence

// 滚动到底部的函数
const scrollToBottom = () => {
  if (!chatContentRef.value) return

  const container = chatContentRef.value
  const maxScrollTop = container.scrollHeight - container.clientHeight

  // 如果内容不需要滚动，直接返回
  if (maxScrollTop <= 0) {
    console.log('📜 内容无需滚动，已在底部')
    return
  }

  // 平滑滚动到底部
  container.scrollTo({
    top: maxScrollTop,
    behavior: 'smooth'
  })

  // 更新滚动位置记录
  lastScrollPosition.value = maxScrollTop
  console.log('📜 音频播放完成，滚动到底部显示最后内容')
}

// 新的用户选择处理函数
const handleUserSelect = async (user: any) => {
  // 先停止所有音频播放和流式输出
  handleStopAudio()
  
  // 重置音频合成队列
  synthQueue = Promise.resolve()
  
  // 清空所有音频相关状态
  audioQueue.value = []
  displayedSentences.value = []
  aiSpeakWords.value = ''
  isPlaying.value = false
  isPaused.value = false
  isSpeechFinished.value = true
  speechFullyFinishedLogged = true
  isStreaming.value = false
  currentPlayingIndex.value = 0
  sentenceMappings.value = []
  lastScrollPosition.value = 0 // 重置滚动位置记录
  
  // 清空语音输入状态
  showVoiceLoading.value = false
  talkingText.value = ''
  
  // 关闭键盘输入面板
  visibleKeyword.value = false
  keyword.value = ''
  
  // 停止数字人说话
  stopSpeaking()
  
  // 清空对话记录
  currentConversation.messages = []
  
  // 切换用户信息
  AIUserValue.value = user.value
  AIUserInfo.value = user

  // 保存用户选择到本地存储
  saveUserToStorage(user.value)

  // 切换视频源
  if (digitalHumanVideo.value) {
    digitalHumanVideo.value.src = user.videoUrl
    digitalHumanVideo.value.load()
  }

  console.log(`🔄 切换到${user.name}，所有音频状态已清空`)
}
const visibleKeyword = ref(false)
const handleKeyboard = () => {
  visibleKeyword.value = !visibleKeyword.value
}

const keyword =ref('')
const handleSend = async ()=>{
  handleStopAudio()
  await handleSearch(keyword.value)
  visibleKeyword.value=false
  keyword.value=''
}


// 本地存储键名
const STORAGE_KEY = 'ai-talk-selected-user-1'

// 从本地存储获取上次选择的用户
const getStoredUser = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? stored : 'digital_liqingzhao' // 默认李清照，使用新的格式
  } catch (error) {
    console.warn('读取本地存储失败:', error)
    return 'digital_liqingzhao'
  }
}

// 保存用户选择到本地存储
const saveUserToStorage = (userValue: string) => {
  try {
    localStorage.setItem(STORAGE_KEY, userValue)
    console.log('💾 已保存用户选择到本地存储:', userValue)
  } catch (error) {
    console.warn('保存到本地存储失败:', error)
  }
}

const AIUserValue = ref(getStoredUser())
const AIUserInfo = ref()

// 数字人列表，从接口获取
const users = ref([])

// 获取数字人列表
const fetchAiTalksList = async () => {
  try {
    const res = await getAiTakls({
      category: 'szr' // 固定编码
    })

    if (res.data && Array.isArray(res.data)) {
      // 将接口返回的数据转换为组件需要的格式
      users.value = res.data.map(item => ({
        name: item.name,
        voice: parseInt(item.speakerCode), // speakerCode对应voice
        speed: item.speed || '1.0',
        value: item.agentFormat, // 使用agentFormat作为value
        videoUrl: item.videoUrl,
        id: item.id,
        pitch: item.pitch || '1.0',
        volume: item.volume || '1.0'
      }))

      console.log('✅ 数字人列表加载成功:', users.value.length, '个数字人')

      // 重新初始化用户信息
      initializeUser()
    } else {
      console.error('❌ 数字人列表数据格式错误:', res)
    }
  } catch (error) {
    console.error('❌ 获取数字人列表失败:', error)
  }
}



// 初始化用户信息：根据本地存储的值设置当前用户
const initializeUser = () => {
  if (users.value.length === 0) {
    console.log('⚠️ 数字人列表为空，等待接口数据加载')
    return
  }

  const storedUserValue = AIUserValue.value
  const foundUser = users.value.find(user => user.value === storedUserValue)
  AIUserInfo.value = foundUser || users.value[0] // 如果找不到存储的用户，默认使用第一个

  console.log('✅ 初始化用户信息:', AIUserInfo.value?.name)
}


const props = defineProps<{
  onSpeechAllFinished?: () => void
}>()

// 视频控制方法
const onVideoLoaded = () => {
  if (digitalHumanVideo.value) {
    // 视频加载完成后暂停在第一帧
    digitalHumanVideo.value.currentTime = 0
    digitalHumanVideo.value.pause()
  }
}

// 获取视频循环播放的起始时间（跳过开头静止部分）
const getLoopStartTime = () => {
  return 0.25
}

// 视频播放结束时的处理
const onVideoEnded = () => {
  if (isSpeaking.value && digitalHumanVideo.value) {
    // 如果还在说话状态，跳过开头静止部分，从说话部分开始循环播放
    const loopStartTime = getLoopStartTime()
    digitalHumanVideo.value.currentTime = loopStartTime
    digitalHumanVideo.value.play()
  }
}

// 封装方法：开始说话
const startSpeaking = () => {
  if (digitalHumanVideo.value && !isSpeaking.value) {
    console.log('🎬 数字人开始说话')
    isSpeaking.value = true
    // 第一次开始说话时从头播放，后续循环时会跳过开头
    digitalHumanVideo.value.currentTime = 0
    digitalHumanVideo.value.play()
  }
}

// 封装方法：停止说话
const stopSpeaking = () => {
  if (digitalHumanVideo.value && isSpeaking.value) {
    console.log('⏹️ 数字人停止说话')
    isSpeaking.value = false
    digitalHumanVideo.value.pause()
    // 停止时回到第一帧（静止状态）
    digitalHumanVideo.value.currentTime = 0
  }
}

// 兼容旧方法名（保持向后兼容）
const startVideoPlaying = () => {
  startSpeaking()
}

const stopVideoPlaying = () => {
  stopSpeaking()
}

// 根据数字人类型获取对应的formType
const getFormType = (userValue: string) => {
  // 直接返回userValue，因为现在userValue就是agentFormat
  return userValue || 'digital_liqingzhao'
}

async function handleSearch(val: string) {
  if (!val || val.trim() === '') {
    return
  }
  
  // 首先完全停止所有当前音频播放和合成（这会自动递增会话ID）
  handleStopAudio()
  
  // 获取当前会话ID（已经在handleStopAudio中递增了）
  const sessionId = currentSessionId
  console.log('🆔 开始新对话会话，ID:', sessionId)
  
  controller.value = new AbortController()
  isStreaming.value = true
  speechFullyFinishedLogged = false // 重置完成标志
  isSpeechFinished.value = false // 重置语音完成状态，允许音频合成
  isPaused.value = false // 重置暂停状态
  
  // 重置音频合成队列，确保新对话有全新的队列状态
  synthQueue = Promise.resolve()
  
  await nextTick()
  qsMsgRef.value?.forceScrollToBottom()


  const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`;

    const requestBody = {
        messages: [
          ...currentConversation.messages,
          {role: 'user', content: val}
        ],
      modelType: 'agent',
      formType: getFormType(AIUserValue.value)
    }

  currentConversation.messages.push({role: 'user', content: val})
  currentConversation.messages.push({role: 'assistant', content: '思考中...', reasoning: ''})
  await nextTick()
  qsMsgRef.value?.forceScrollToBottom()
  const messageIndex = currentConversation.messages.length - 1

  let accumulatedText = ''
  let speechBuffer = ''
  aiSpeakWords.value = ''
  displayedSentences.value = [] // 重置已显示的句子
  currentPlayingIndex.value = 0 // 重置播放索引
  sentenceMappings.value = [] // 重置句子映射
  lastScrollPosition.value = 0 // 重置滚动位置记录
  audioQueue.value = [] // 清空音频队列，确保新对话有全新的音频队列

  // 重置聊天内容容器的滚动位置
  if (chatContentRef.value) {
    chatContentRef.value.scrollTop = 0
  }

  fetchEventSource(apiUrl, {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify(requestBody),
    signal: controller.value.signal,
    openWhenHidden: true,

    async onopen(response) {
      if (!response.ok) throw new Error('网络请求失败')
    },

    async onmessage(msg) {
      if (msg.event === 'FatalError') throw new Error(msg.data)

      const data = msg.data;
      const jsonData = JSON.parse(data)

      const finishReason = jsonData.choices[0].finish_reason

      if (finishReason === 'stop') {
        isStreaming.value = false
        // 处理剩余的speechBuffer，确保显示内容和音频合成内容一致
        if (speechBuffer.trim()) {
          const finalSentences = splitSentences(speechBuffer.trim())

          // 将分割后的句子添加到显示列表
          for (const sentence of finalSentences.list) {
            displayedSentences.value.push(sentence)
            await enqueueSpeechSegment(sentence, sessionId)
          }

          // 如果还有剩余文本，也要处理
          if (finalSentences.remaining.trim()) {
            displayedSentences.value.push(finalSentences.remaining.trim())
            await enqueueSpeechSegment(finalSentences.remaining.trim(), sessionId)
          }

          // 更新显示内容
          aiSpeakWords.value = stripMarkdown(displayedSentences.value.join(''))
        }
        speechBuffer = ''
        return
      }

      const content =  jsonData.choices[0].message.content
      
      if (content) {
        accumulatedText += content
        currentConversation.messages[messageIndex].content = accumulatedText
        speechBuffer += content

        // 分段并立即合成音频
        const sentences = splitSentences(speechBuffer)
        speechBuffer = sentences.remaining

        // 更新显示的句子 - 按段落显示
        for (const sentence of sentences.list) {
          console.log(sentence)
          displayedSentences.value.push(sentence)
          await enqueueSpeechSegment(sentence, sessionId)
        }

        // 更新显示内容为已完成的句子
        aiSpeakWords.value = stripMarkdown(displayedSentences.value.join(''))
      }
      qsMsgRef.value?.forceScrollToBottom()
      await nextTick()
    },

    async onclose() {
      if (scrollThrottle.value) clearTimeout(scrollThrottle.value)
      // 处理剩余的speechBuffer
      if (speechBuffer.trim()) {
        // 对剩余文本进行分割处理，确保显示内容和音频合成内容一致
        const finalSentences = splitSentences(speechBuffer.trim())

        // 将分割后的句子添加到显示列表
        for (const sentence of finalSentences.list) {
          displayedSentences.value.push(sentence)
          await enqueueSpeechSegment(sentence, sessionId)
        }

        // 如果还有剩余文本，也要处理
        if (finalSentences.remaining.trim()) {
          displayedSentences.value.push(finalSentences.remaining.trim())
          await enqueueSpeechSegment(finalSentences.remaining.trim(), sessionId)
        }

        // 更新显示内容
        aiSpeakWords.value = stripMarkdown(displayedSentences.value.join(''))
      }
      isStreaming.value = false
      
      // 流式输出结束后，等待音频合成完成再检查
      setTimeout(() => {
        console.log('🔄 流式输出结束，等待音频合成完成')
        if (audioQueue.value.length > 0 && !isPlaying.value) {
          console.log('🚀 流式结束时启动音频播放')
          processAudioQueue()
        } else {
          // 等待更长时间让音频合成完成
          setTimeout(() => {
            console.log('🔄 延迟检查是否真正完成')
            if (audioQueue.value.length > 0 && !isPlaying.value) {
              console.log('🚀 延迟检查时启动音频播放')
              processAudioQueue()
            } else {
              // 只有在确实没有音频且没有播放时才检查完成状态
              if (!isPlaying.value && audioQueue.value.length === 0) {
                console.log('🔄 流式结束且无音频，检查完成状态')
                checkIfSpeechFullyFinished()
              }
            }
          }, 3000) // 给音频合成更多时间
        }
      }, 1000) // 增加初始等待时间
    },

    onerror(err) {
      console.error('SSE error:', err)
      currentConversation.messages[messageIndex].content = `抱歉，我遇到了一些问题：${err}`
      qsMsgRef.value?.forceScrollToBottom()
      isStreaming.value = false
      throw new Error(err)
    }
  })
  trackingService.trackEvent('NumberAI')
}

function splitSentences(text: string): { list: string[]; remaining: string } {
  // 简化的分割规则：统一按照主要分隔符对一段话进行分割
  // 中文主要分割符：句号、感叹号、问号、分号、省略号、换行符、逗号、顿号、冒号
  // 英文主要分割符：句号、感叹号、问号、分号、换行符、逗号、冒号
  const matches = text.match(/[^。！？；，、：\n.!?;,:]*?(?:……|[。！？；，、：\n.!?;,:])/g)

  // 处理匹配到的句子
  const processedSentences: string[] = []
  let processedLength = 0

  if (matches) {
    for (const match of matches) {
      const trimmed = match.trim()
      if (!trimmed) continue

      processedLength += match.length
      processedSentences.push(trimmed)
    }
  }

  // 处理剩余文本
  const remaining = text.slice(processedLength)

  console.log('📝 文本分割结果 - 原始长度:', text.length, '已处理长度:', processedLength, '分割数量:', processedSentences.length)

  // 输出每个分割片段的长度，便于调试
  processedSentences.forEach((sentence, index) => {
    console.log(`📝 片段${index + 1}(${sentence.length}字符): ${sentence.substring(0, 20)}${sentence.length > 20 ? '...' : ''}`)
  })

  return {
    list: processedSentences,
    remaining: remaining.trim()
  }
}

let synthQueue: Promise<void> = Promise.resolve() // 合成串行队列

async function enqueueSpeechSegment(sentence: string, sessionId: number) {
  // 将任务加入串行队列，按顺序合成音频
  synthQueue = synthQueue.then(async () => {
    try {
      // 检查会话ID是否还是当前会话（防止旧会话的音频影响新会话）
      if (sessionId !== currentSessionId) {
        console.log('⏹️ 会话已切换，跳过旧会话音频合成:', sentence.substring(0, 10) + '...', '旧会话ID:', sessionId, '当前会话ID:', currentSessionId)
        return
      }
      
      // 检查是否已被用户手动停止（只有明确停止时才跳过）
      if (speechFullyFinishedLogged) {
        console.log('⏹️ 音频合成已被用户停止，跳过:', sentence.substring(0, 10) + '...')
        return
      }
      
      const res = await getTTSLL({
        text: stripMarkdown(sentence),
        speakerId: AIUserInfo.value.voice,
        speed: AIUserInfo.value.speed,
        volume: AIUserInfo.value.volume,
        pitch:  AIUserInfo.value.pitch
      })
      const url = res.data?.fileUrl
      if (url) {
        // 再次检查会话ID（音频合成完成后再次确认）
        if (sessionId !== currentSessionId) {
          console.log('⏹️ 音频合成完成但会话已切换，不添加到队列:', sentence.substring(0, 10) + '...', '旧会话ID:', sessionId, '当前会话ID:', currentSessionId)
          return
        }
        
        // 再次检查是否已被用户手动停止（只有明确停止时才跳过）
        if (speechFullyFinishedLogged) {
          console.log('⏹️ 合成完成但已被用户停止，不添加到队列:', sentence.substring(0, 10) + '...')
          return
        }
        
        // 记录句子和音频的映射关系
        sentenceMappings.value.push({
          text: sentence,
          audioUrl: url
        })
        
        audioQueue.value.push(url)
        console.log('🎵 音频已添加到队列，当前队列长度:', audioQueue.value.length, '是否正在播放:', isPlaying.value, '会话ID:', sessionId)
        if (!isPlaying.value) {
          console.log('🚀 开始播放音频队列')
          await processAudioQueue()
        } else {
          console.log('🎵 当前正在播放，音频已加入队列等待')
        }
        
        // 添加保险机制：如果3秒后发现音频没有开始播放，强制检查队列
        setTimeout(() => {
          if (audioQueue.value.length > 0 && !isPlaying.value && !speechFullyFinishedLogged) {
            console.warn('🔧 检测到音频队列有内容但未播放，强制启动播放')
            processAudioQueue()
          }
        }, 3000)
      } else {
        console.warn('⚠️ 音频合成失败，未获取到URL，跳过该段落:', sentence.substring(0, 10) + '...')
      }
    } catch (e) {
      console.error('❌ 音频合成失败，跳过该段落:', sentence.substring(0, 10) + '...', e)
      // 合成失败时不影响后续合成，继续处理下一个
    }
  }).catch(error => {
    // 确保串行队列不会因为单个任务失败而中断
    console.error('❌ 音频合成队列出错，但继续处理后续任务:', error)
  })
}


async function processAudioQueue() {
  console.log('🎯 进入播放队列处理，队列长度:', audioQueue.value.length, '是否正在播放:', isPlaying.value)
  
  // 检查是否已被用户手动停止（只有明确停止时才停止播放）
  if (speechFullyFinishedLogged) {
    console.log('⏹️ 播放队列已被用户停止，清空剩余队列')
    audioQueue.value = []
    return
  }
  
  if (audioQueue.value.length === 0 || isPlaying.value) {
    console.log('📋 队列为空或正在播放，退出处理。队列长度:', audioQueue.value.length, '正在播放:', isPlaying.value)
    // 如果队列为空且不在播放中，等待一段时间再检查是否真正完成
    if (audioQueue.value.length === 0 && !isPlaying.value && !isStreaming.value) {
      setTimeout(() => {
        // 再次确认队列仍然为空且没有新的流式输出
        if (audioQueue.value.length === 0 && !isPlaying.value && !isStreaming.value) {
          checkIfSpeechFullyFinished()
        }
      }, 1500) // 等待1.5秒确保没有新的音频正在合成
    }
    return
  }
  
  isPlaying.value = true

  const url = audioQueue.value.shift()
  console.log('🎵 取出音频URL:', url ? url.substring(0, 50) + '...' : 'null')
  
  if (!url) {
    console.warn('⚠️ 队列中音频URL为空，跳过')
    isPlaying.value = false
    currentPlayingIndex.value++
    // 继续处理下一个
    setTimeout(() => {
      processAudioQueue()
      // 不要立即检查完成状态，让processAudioQueue自己处理
    }, 50)
    return
  }

  console.log('🎧 创建音频对象并开始播放')
  const audio = new Audio(url)
  audioElement.value = audio
  isPlaying.value = true
  isSpeechFinished.value = false

  // 开始播放音频时，启动数字人说话
  startSpeaking()

  // 每次开始播放新音频时，精确滚动到对应的文字位置
  console.log('🎯 开始播放音频片段 - 索引:', currentPlayingIndex.value, '内容:', sentenceMappings.value[currentPlayingIndex.value]?.text.substring(0, 30) + '...')
  scrollToCurrentPosition()

  audio.onended = () => {
    console.log('✅ 音频播放结束')
    isPlaying.value = false
    // 注意：不要在这里设置 isSpeechFinished.value = true，因为可能还有更多音频要播放
    isPaused.value = false // 音频结束时重置暂停状态

    // 更新当前播放索引
    currentPlayingIndex.value++

    // 检查是否还有更多音频要播放
    if (audioQueue.value.length === 0) {
      console.log('🎯 音频队列为空，检查是否真正完成')

      // 如果流式输出已经结束，说明这确实是最后一段音频，立即停止数字人说话
      if (!isStreaming.value) {
        console.log('🎯 流式输出已结束，立即停止数字人说话')
        stopSpeaking()
        isSpeechFinished.value = true

        // 音频播放完成后，滚动到底部确保用户能看到最后的话
        scrollToBottom()

        checkIfSpeechFullyFinished()
      } else {
        // 如果流式输出还在进行，等待一段时间确保没有新的音频正在合成
        console.log('🔄 流式输出仍在进行，等待新音频')
        setTimeout(() => {
          // 再次检查队列是否仍然为空，并且流式输出状态
          if (audioQueue.value.length === 0 && !isStreaming.value) {
            console.log('🎯 确认音频队列为空且流式结束，停止数字人说话')
            stopSpeaking()
            isSpeechFinished.value = true

            // 音频播放完成后，滚动到底部确保用户能看到最后的话
            scrollToBottom()

            checkIfSpeechFullyFinished()
          } else if (audioQueue.value.length > 0) {
            console.log('🎵 发现新的音频，继续播放')
            processAudioQueue()
          } else {
            console.log('🔄 流式输出仍在进行，继续等待')
          }
        }, 1000) // 等待1秒确保音频合成完成
      }
    } else {
      // 如果还有更多音频，继续处理
      setTimeout(() => {
        console.log('📢 音频结束，继续处理下一个')
        processAudioQueue()
      }, 50) // 添加小延迟确保状态更新
    }
  }

  audio.onerror = (event) => {
    // 检查是否是因为手动停止导致的错误，如果是则忽略
    if (isSpeechFinished.value && speechFullyFinishedLogged) {
      console.log('⚠️ 音频错误是由于手动停止导致，忽略此错误')
      return
    }
    
    console.error('❌ 音频播放出错，跳过当前音频，继续播放下一个。错误详情:', event)
    console.error('❌ 音频URL:', url)
    isPlaying.value = false
    // 注意：不要在这里设置 isSpeechFinished.value = true，因为可能还有更多音频要播放
    currentPlayingIndex.value++ // 增加索引，跳过当前失败的音频
    // 音频出错时继续处理队列
    setTimeout(() => {
      processAudioQueue()
      // 不要立即检查完成状态，让processAudioQueue自己处理
    }, 50)
  }

  console.log('🚀 尝试播放音频')
  try {
    await audio.play()
    console.log('✅ 音频播放启动成功')
  } catch (err) {
    console.error('❌ 音频播放启动失败，跳过当前音频，继续播放下一个:', err)
    console.error('❌ 失败的音频URL:', url)
    isPlaying.value = false
    // 注意：不要在这里设置 isSpeechFinished.value = true，因为可能还有更多音频要播放
    currentPlayingIndex.value++ // 增加索引，跳过当前失败的音频
    // 播放出错时继续处理队列
    setTimeout(() => {
      processAudioQueue()
      // 不要立即检查完成状态，让processAudioQueue自己处理
    }, 50)
  }
}

function checkIfSpeechFullyFinished() {
  console.log('🔍 检查语音是否完全完成 - 流式:', !isStreaming.value, '队列:', audioQueue.value.length, '播放中:', !isPlaying.value, '已标记:', speechFullyFinishedLogged)

  // 如果已经标记完成，直接返回
  if (speechFullyFinishedLogged) {
    console.log('🔍 已经标记完成，跳过检查')
    return
  }

  // 判断条件：不在流式输出、音频队列为空、不在播放中、还未标记完成
  if (!isStreaming.value && audioQueue.value.length === 0 && !isPlaying.value) {
    // 给音频合成更多时间，确保所有任务真正完成
    setTimeout(() => {
      console.log('🔍 延迟检查 - 流式:', !isStreaming.value, '队列:', audioQueue.value.length, '播放中:', !isPlaying.value, '已标记:', speechFullyFinishedLogged)

      // 再次检查，确保真的完成了，并且没有被其他地方标记完成
      if (!isStreaming.value && audioQueue.value.length === 0 && !isPlaying.value && !speechFullyFinishedLogged) {
        console.log('✅ 全部流式内容已播报完成')
        speechFullyFinishedLogged = true

        // 确保数字人停止说话
        isSpeechFinished.value = true
        stopSpeaking()
        console.log('🤖 数字人停止说话')

        // 音频播放完成后，滚动到底部确保用户能看到最后的话
        scrollToBottom()

        // 触发完成回调
        props.onSpeechAllFinished?.()
      } else {
        console.log('🔄 状态已变化，取消完成标记')
      }
    }, 2000) // 减少等待时间，但仍给音频合成足够时间
  } else {
    console.log('🔄 条件不满足，继续等待 - 流式:', isStreaming.value, '队列长度:', audioQueue.value.length, '播放中:', isPlaying.value)
  }
}


const rebuild = async (val: any) => {
  currentConversation.messages.splice(-2)
  await nextTick()
  qsMsgRef.value?.forceScrollToBottom()
  handleStopAudio()
  await handleSearch(val.userContent)
}

const openDrawer = async () => {
  await nextTick()
  qsMsgRef.value?.forceScrollToBottom()
}



const handleTalkStart = async () => {
  showVoiceLoading.value = true
  handleStopAudio()
}
const handleTalking = async (val: string) => (talkingText.value = val)
const handleTalkEnd = async (val: string) => {
  showVoiceLoading.value = false
  talkingText.value = val
  await handleSearch(val)
}

const handleStopAudio = () => {
  console.log('🛑 执行停止操作')
  
  // 立即切换会话ID，让所有旧的音频合成任务失效
  currentSessionId++
  console.log('🆔 切换会话ID到:', currentSessionId)
  
  // 停止当前音频播放
  if (audioElement.value) {
    // 先移除事件监听器，避免触发错误事件
    audioElement.value.onended = null
    audioElement.value.onerror = null
    
    // 暂停音频
    try {
      audioElement.value.pause()
    } catch (e) {
      console.warn('⚠️ 停止音频时出现错误，但继续清理:', e)
    }
    
    // 清空音频源
    try {
      audioElement.value.src = ''
      audioElement.value.load() // 重新加载空的音频源
    } catch (e) {
      console.warn('⚠️ 清空音频源时出现错误，但继续清理:', e)
    }
    
    audioElement.value = null
  }
  
  // 中断流式请求
  try {
    controller.value.abort()
  } catch (e) {
    console.warn('⚠️ 中断请求时出现错误，但继续清理:', e)
  }
  
  // 重置音频合成队列，阻止新的音频合成
  synthQueue = Promise.resolve()
  
  // 清空所有音频相关状态
  audioQueue.value = []
  displayedSentences.value = []
  aiSpeakWords.value = ''
  isPlaying.value = false
  isPaused.value = false
  isSpeechFinished.value = true
  speechFullyFinishedLogged = true
  isStreaming.value = false
  currentPlayingIndex.value = 0
  sentenceMappings.value = []
  
  // 停止数字人说话
  stopSpeaking()
  
  console.log('✅ 停止操作完成，所有状态已清空')
}

// 暂停播放
const handlePauseAudio = () => {
  if (audioElement.value && !audioElement.value.paused) {
    audioElement.value.pause()
    isPaused.value = true
    // 暂停时，数字人也停止说话
    stopSpeaking()
    console.log('⏸️ 暂停播放')
  }
}

// 继续播放
const handleResumeAudio = () => {
  if (audioElement.value && audioElement.value.paused && isPaused.value) {
    audioElement.value.play()
    isPaused.value = false
    // 继续播放时，数字人也继续说话
    startSpeaking()
    // 继续播放时也使用智能滚动策略
    scrollToCurrentPosition()
    console.log('▶️ 继续播放')
  }
}

// 初始化视频
const initVideo = () => {
  if (digitalHumanVideo.value && AIUserInfo.value?.videoUrl) {
    digitalHumanVideo.value.src = AIUserInfo.value.videoUrl
    digitalHumanVideo.value.load()
  }
}

// 监听视频元素变化
watch(digitalHumanVideo, (newVideo) => {
  if (newVideo) {
    initVideo()
  }
})

// 组件挂载时初始化
onMounted(async () => {
  // 首先获取数字人列表
  await fetchAiTalksList()

  // 确保视频源正确设置
  if (digitalHumanVideo.value && AIUserInfo.value?.videoUrl) {
    digitalHumanVideo.value.src = AIUserInfo.value.videoUrl
    digitalHumanVideo.value.load()
  }
  console.log('当前数字人:', AIUserInfo.value?.name)

  // 进入全屏模式
  await enterFullscreen()
})

onBeforeUnmount(async () => {
  handleStopAudio()
  
  // 退出全屏模式
  await exitFullscreen()
})

// 全屏相关方法
const enterFullscreen = async () => {
  try {
    const element = document.documentElement
    if (element.requestFullscreen) {
      await element.requestFullscreen()
    } else if (element.webkitRequestFullscreen) {
      await element.webkitRequestFullscreen()
    } else if (element.mozRequestFullScreen) {
      await element.mozRequestFullScreen()
    } else if (element.msRequestFullscreen) {
      await element.msRequestFullscreen()
    }
    console.log('🖥️ 进入全屏模式')
  } catch (error) {
    console.warn('⚠️ 进入全屏失败，可能需要用户交互:', error)
    // 如果自动全屏失败，可以在用户首次点击时尝试全屏
    document.addEventListener('click', handleFirstUserInteraction, { once: true })
  }
}

const exitFullscreen = async () => {
  try {
    if (document.fullscreenElement || 
        document.webkitFullscreenElement || 
        document.mozFullScreenElement || 
        document.msFullscreenElement) {
      
      if (document.exitFullscreen) {
        await document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen()
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen()
      }
      console.log('🖥️ 退出全屏模式')
    }
  } catch (error) {
    console.warn('⚠️ 退出全屏失败:', error)
  }
}

// 处理用户首次交互时尝试全屏
const handleFirstUserInteraction = async () => {
  try {
    if (!document.fullscreenElement && 
        !document.webkitFullscreenElement && 
        !document.mozFullScreenElement && 
        !document.msFullscreenElement) {
      await enterFullscreen()
    }
  } catch (error) {
    console.warn('⚠️ 用户交互后全屏仍然失败:', error)
  }
}

</script>

<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
}

.container {
  height: 100%;
  background: #000;
  box-sizing: border-box;
  overflow: hidden;
}

.chat-page {
  position: relative;
  top: 0vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .digital-human-video {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    object-fit: fill;
    z-index: 1;
  }

  .chat-content {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    max-height: 210px;
    width: 80%;
    max-width: 65vw;
    min-width: 450px;
    font-size: 32px;
    line-height: 1.6;
    letter-spacing: 3px;
    color: #ffffff;
    text-align: center;
    overflow-y: auto;
    font-weight: 600;
    text-shadow:
      3px 3px 6px rgba(0, 0, 0, 0.9),
      0 0 12px rgba(0, 0, 0, 0.7),
      0 0 20px rgba(0, 0, 0, 0.5);
    font-family: 'Microsoft YaHei', '微软雅黑', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    opacity: 0.95;
    word-spacing: 4px;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    /* 当前播放文字的黄色高亮样式 */
    .current-speaking {
      color: #ffeb3b;
      transition: color 0.3s ease;
    }
  }

  .qs-content {
    position: relative;
    flex: auto 1 1;
    min-height: 0;
    display: flex;
    align-items: stretch;
    width: 200px;
    height: 100%;
  }
}

/* 思考中指示器样式 */
.thinking-indicator {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  max-height: 210px;
  width: 80%;
  max-width: 65vw;
  min-width: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: thinkingFadeIn 0.5s ease-out;
}

.thinking-content {
  display: flex;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.thinking-text {
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
  text-shadow:
    3px 3px 6px rgba(0, 0, 0, 0.9),
    0 0 12px rgba(0, 0, 0, 0.7),
    0 0 20px rgba(0, 0, 0, 0.5);
  letter-spacing: 3px;
  font-family: 'Microsoft YaHei', '微软雅黑', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  opacity: 0.95;
  word-spacing: 4px;
  line-height: 1.6;
  animation: textPulse 2s infinite ease-in-out;
}

.thinking-dots {
  display: flex;
  align-items: center;
  gap: 8px;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ffffff;
    box-shadow:
      0 0 8px rgba(255, 255, 255, 0.8),
      0 0 16px rgba(255, 255, 255, 0.4);
    animation: dotPulse 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

/* 动画效果 */
@keyframes thinkingFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.5);
    opacity: 0.3;
    box-shadow:
      0 0 4px rgba(255, 255, 255, 0.4),
      0 0 8px rgba(255, 255, 255, 0.2);
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
    box-shadow:
      0 0 16px rgba(255, 255, 255, 0.9),
      0 0 24px rgba(255, 255, 255, 0.5);
  }
}

@keyframes textPulse {
  0%, 100% {
    opacity: 0.95;
    text-shadow:
      3px 3px 6px rgba(0, 0, 0, 0.9),
      0 0 12px rgba(0, 0, 0, 0.7),
      0 0 20px rgba(0, 0, 0, 0.5);
  }
  50% {
    opacity: 0.8;
    text-shadow:
      3px 3px 6px rgba(0, 0, 0, 0.9),
      0 0 16px rgba(0, 0, 0, 0.8),
      0 0 24px rgba(0, 0, 0, 0.6);
  }
}

.bottom-bar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
  animation: slideUpFadeIn 0.4s ease-out;

  .content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    
    .control-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 24px;
      border-radius: 25px;
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.1) 0%, 
        rgba(255, 255, 255, 0.05) 50%, 
        rgba(0, 0, 0, 0.1) 100%);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
      min-width: 80px;
      
      /* 光泽效果 */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, 
          transparent, 
          rgba(255, 255, 255, 0.2), 
          transparent);
        transition: left 0.6s ease;
      }
      
      /* 悬停光环效果 */
      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 27px;
        background: linear-gradient(45deg, transparent, transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
      }
      
      &:hover {
        background: linear-gradient(135deg, 
          rgba(255, 255, 255, 0.15) 0%, 
          rgba(255, 255, 255, 0.08) 50%, 
          rgba(0, 0, 0, 0.05) 100%);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px) scale(1.02);
        box-shadow: 
          0 12px 40px rgba(0, 0, 0, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.3),
          inset 0 -1px 0 rgba(0, 0, 0, 0.2);
          
        &::before {
          left: 100%;
        }
        
        .button-text {
          transform: translateY(-1px);
          text-shadow: 0 0 20px currentColor;
        }
      }
      
      &:active {
        transform: translateY(-1px) scale(0.98);
        box-shadow: 
          0 4px 20px rgba(0, 0, 0, 0.3),
          inset 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      
      .button-text {
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
        font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      }
    }
    
    .pause-resume-btn {
      background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.2) 0%, 
        rgba(37, 99, 235, 0.15) 50%, 
        rgba(29, 78, 216, 0.1) 100%);
      border-color: rgba(59, 130, 246, 0.3);
      
      .button-text {
        color: #93c5fd;
        text-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
      }
      
      &::after {
        background: linear-gradient(45deg, 
          rgba(59, 130, 246, 0.3), 
          rgba(147, 197, 253, 0.3), 
          rgba(59, 130, 246, 0.3));
      }
      
      &:hover {
        background: linear-gradient(135deg, 
          rgba(59, 130, 246, 0.3) 0%, 
          rgba(37, 99, 235, 0.2) 50%, 
          rgba(29, 78, 216, 0.15) 100%);
        border-color: rgba(59, 130, 246, 0.5);
        box-shadow: 
          0 12px 40px rgba(59, 130, 246, 0.4),
          inset 0 1px 0 rgba(147, 197, 253, 0.3),
          inset 0 -1px 0 rgba(29, 78, 216, 0.2);
          
        &::after {
          opacity: 1;
        }
        
        .button-text {
          color: #dbeafe;
          text-shadow: 
            0 2px 4px rgba(59, 130, 246, 0.5),
            0 0 20px rgba(59, 130, 246, 0.3);
        }
      }
    }
    
    .stop-btn {
      background: linear-gradient(135deg, 
        rgba(239, 68, 68, 0.2) 0%, 
        rgba(220, 38, 38, 0.15) 50%, 
        rgba(185, 28, 28, 0.1) 100%);
      border-color: rgba(239, 68, 68, 0.3);
      
      .button-text {
        color: #fca5a5;
        text-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
      }
      
      &::after {
        background: linear-gradient(45deg, 
          rgba(239, 68, 68, 0.3), 
          rgba(252, 165, 165, 0.3), 
          rgba(239, 68, 68, 0.3));
      }
      
      &:hover {
        background: linear-gradient(135deg, 
          rgba(239, 68, 68, 0.3) 0%, 
          rgba(220, 38, 38, 0.2) 50%, 
          rgba(185, 28, 28, 0.15) 100%);
        border-color: rgba(239, 68, 68, 0.5);
        box-shadow: 
          0 12px 40px rgba(239, 68, 68, 0.4),
          inset 0 1px 0 rgba(252, 165, 165, 0.3),
          inset 0 -1px 0 rgba(185, 28, 28, 0.2);
          
        &::after {
          opacity: 1;
        }
        
        .button-text {
          color: #fee2e2;
          text-shadow: 
            0 2px 4px rgba(239, 68, 68, 0.5),
            0 0 20px rgba(239, 68, 68, 0.3);
        }
      }
    }
  }
}

.user-des {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 99;

  .user-selector {
    .current-user {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 16px 24px;
      border-radius: 16px;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(30, 30, 40, 0.95) 100%);
      backdrop-filter: blur(20px);
      border: 2px solid rgba(59, 130, 246, 0.4);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 8px 32px rgba(59, 130, 246, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;

      /* 发光效果 */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
        transition: left 0.6s ease;
      }

      &:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.25) 0%, rgba(30, 30, 40, 0.98) 100%);
        border-color: rgba(59, 130, 246, 0.6);
        transform: translateY(-2px) scale(1.02);
        box-shadow:
          0 12px 40px rgba(59, 130, 246, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);

        &::before {
          left: 100%;
        }
      }

      .user-name {
        font-size: 20px;
        font-weight: 700;
        color: #ffffff;
        text-shadow:
          0 2px 4px rgba(0, 0, 0, 0.8),
          0 0 8px rgba(59, 130, 246, 0.5);
        letter-spacing: 1px;
        margin-right: 12px;
        position: relative;
        z-index: 1;
      }

      .dropdown-icon {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.6));
        position: relative;
        z-index: 1;
      }

      &:hover .dropdown-icon {
        transform: rotate(180deg) scale(1.1);
        color: #ffffff;
        filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.8));
      }
    }
  }

  .chat-button {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    transition: all 0.2s ease;

    .chat-text {
      font-size: 16px;
      font-weight: 500;
      color: #ffffff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    }

    .el-icon {
      color: #ffffff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
      transition: all 0.2s ease;
    }

    &:hover .el-icon {
      color: #ffffff;
      opacity: 0.8;
    }

    &:hover .chat-text {
      color: #ffffff;
      opacity: 0.8;
    }
  }
}


/* 对话记录抽屉样式 */
:deep(.el-drawer) {
  background: linear-gradient(135deg, rgba(30, 30, 40, 0.95) 0%, rgba(50, 50, 65, 0.95) 100%) !important;
  backdrop-filter: blur(20px) !important;
  border-left: 1px solid rgba(255, 255, 255, 0.15) !important;
  box-shadow:
    -8px 0 32px rgba(0, 0, 0, 0.4),
    inset 1px 0 0 rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-drawer__header) {
  background: rgba(40, 40, 55, 0.8) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15) !important;
  padding: 20px 24px !important;
  margin-bottom: 0 !important;

  .el-drawer__title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
    letter-spacing: 1px !important;
  }
}

:deep(.el-drawer__close-btn) {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 20px !important;
  transition: all 0.3s ease !important;
}

:deep(.el-drawer__body) {
  padding: 0 !important;
  background: transparent !important;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

:deep(.el-drawer__footer) {
  background: rgba(40, 40, 55, 0.8) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.15) !important;
  padding: 16px 24px !important;

  .el-button {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 8px !important;

    &:hover {
      background: rgba(59, 130, 246, 0.2) !important;
      border-color: rgba(59, 130, 246, 0.3) !important;
      color: #ffffff !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
    }

    &:active {
      transform: translateY(0) scale(0.98) !important;
    }
  }
}

/* 抽屉关闭按钮样式优化 */
:deep(.el-drawer__close-btn) {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 8px !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px) !important;

  .el-icon {
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
  }

  &:hover {
    background: rgba(59, 130, 246, 0.2) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;

    .el-icon {
      color: #ffffff !important;
    }
  }

  &:active {
    transform: translateY(0) scale(0.98) !important;
  }
}

.qs-content {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px); // 减去header和footer的高度
  overflow: hidden;
  
  :deep(.messages-container) {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
}



/* 键盘输入面板样式 */
.keyboard-input-panel {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 20px;
  pointer-events: none;
}

.panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: all;
}

.input-panel {
  position: relative;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(20, 20, 30, 0.98) 100%);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(59, 130, 246, 0.2);
  width: 400px;
  max-width: 90vw;
  pointer-events: all;
  animation: panelSlideIn 0.3s ease-out;
  margin-bottom: 80px; /* 距离底部语音组件的距离 */
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  }

  .close-btn {
    cursor: pointer;
    padding: 6px;
    border-radius: 50%;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.7);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      transform: scale(1.1);
    }

    .el-icon {
      font-size: 16px;
    }
  }
}

.input-area {
  padding: 20px;

  .input-textarea {
    width: 100%;
  }
}

.panel-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .input-hint {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    backdrop-filter: blur(10px);
  }

  .action-buttons {
    display: flex;
    gap: 8px;

    .cancel-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.8);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        color: #ffffff;
      }
    }

    .send-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      color: #ffffff;
      display: flex;
      align-items: center;
      gap: 6px;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .send-icon {
        font-size: 14px;
      }
    }
  }
}

/* 自定义文本域样式 */
:deep(.input-textarea .el-textarea__inner) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  color: #ffffff;
  transition: all 0.3s ease;

  &:focus {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
}

/* 动画效果 */
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 下拉菜单样式 */
.el-dropdown-menu,
:deep(.el-dropdown-menu) {
  background: rgba(0, 0, 0, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.8),
    0 0 0 1px rgba(59, 130, 246, 0.4) !important;
  backdrop-filter: blur(20px) !important;
  padding: 8px !important;
  overflow: hidden;
  position: relative;
  min-width: 160px !important;
  animation: dropdownFadeIn 0.2s ease-out;
}

.el-dropdown-menu__item,
:deep(.el-dropdown-menu__item) {
  padding: 12px 16px !important;
  font-size: 16px !important;
  color: #ffffff !important;
  transition: all 0.2s ease !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  border: none !important;
  position: relative;
  background: transparent !important;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif !important;
  margin: 2px 0 !important;
  border-radius: 8px !important;
  cursor: pointer;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

.el-dropdown-menu__item:hover,
:deep(.el-dropdown-menu__item:hover) {
  background: rgba(59, 130, 246, 0.25) !important;
  color: #ffffff !important;
  transform: translateX(2px);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9) !important;
}

.el-dropdown-menu__item.is-active,
:deep(.el-dropdown-menu__item.is-active) {
  background: rgba(59, 130, 246, 0.35) !important;
  color: #ffffff !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9) !important;
}

.user-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .check-icon {
    color: #3b82f6;
    font-size: 20px;
    font-weight: 700;
    opacity: 1;
  }
}

/* 简洁动画效果 */
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 底部操作栏滑入动画 */
@keyframes slideUpFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}



</style>

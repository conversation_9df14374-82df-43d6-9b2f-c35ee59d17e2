<template>
  <el-dialog
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :show-close="true"
    custom-class="login-dialog"
    center
    @close="dialogVisible = false"
  >
    <div class="login-container">
      <div class="login-header">
        <img src="@/assets/logo.png" alt="Logo" class="logo" />
        <h1 class="title">均衡AI</h1>
        <div class="login-tabs">
          <span
            :class="['tab-item', loginType === 'phone' ? 'active' : '']"
            @click="switchLoginType('phone')"
          >
            手机号登录
          </span>
          <span
            :class="['tab-item', loginType === 'qrcode' ? 'active' : '']"
            @click="switchLoginType('qrcode')"
          >
            扫码登录
          </span>
        </div>
      </div>

      <!-- 手机号登录表单 -->
      <el-form
        v-if="loginType === 'phone'"
        ref="loginForm"
        :model="form"
        :rules="rules"
        class="login-form"
      >
        <el-form-item prop="phoneNumber">
          <el-input
            v-model="form.phoneNumber"
            placeholder="请输入手机号"
            :prefix-icon="User"
            size="large"
            maxlength="11"
          ></el-input>
        </el-form-item>
        <el-form-item prop="verifyCode">
          <div class="verify-code-container">
            <el-input
              v-model="form.verifyCode"
              placeholder="请输入验证码"
              :prefix-icon="Message"
              size="large"
              maxlength="4"
              class="code-input"
            ></el-input>
            <el-button
              type="primary"
              class="send-code-btn"
              :disabled="cooldown > 0"
              @click="sendVerifyCode"
              size="large"
            >
              {{ cooldown > 0 ? `${cooldown}秒后重发` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="submitLogin"
            :loading="loading"
            class="login-button"
            size="large"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 扫码登录 -->
      <div v-else class="qrcode-login-container">
        <div class="qrcode-wrapper" ref="qrcodeContainer">
          <div v-if="qrcodeLoading" class="qrcode-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <p>二维码加载中...</p>
          </div>
          <div v-else-if="qrcodeExpired" class="qrcode-expired">
            <el-icon><Warning /></el-icon>
            <p>二维码已过期</p>
            <el-button @click="refreshQrCode" type="primary" size="small">刷新二维码</el-button>
          </div>
          <div v-else-if="scanSuccess" class="qrcode-success">
            <el-icon class="success-icon"><SuccessFilled /></el-icon>
            <p>扫码成功</p>
          </div>
          <div v-else-if="!agreed" class="qrcode-agreement-overlay">
            <el-icon><Warning /></el-icon>
            <p>请阅读并同意用户协议和隐私政策</p>
          </div>
          <div v-if="!qrcodeExpired && !scanSuccess" class="qrcode-canvas-container" id="qrcode"></div>
        </div>
        <div class="qrcode-tip">
          <p>请使用微信扫一扫登录</p>
        </div>
      </div>

      <div class="agreement-section">
        <el-checkbox v-model="agreed" size="large">我已阅读并同意</el-checkbox>
        <el-link type="primary" href="#" :underline="false" class="link" @click.prevent="showAgreement('user')">《用户协议》</el-link>
        <span class="and">和</span>
        <el-link type="primary" href="#" :underline="false" class="link" @click.prevent="showAgreement('privacy')">《隐私政策》</el-link>
      </div>
      <p class="auto-register-tip">未注册手机号将自动注册</p>

    </div>
    <template #footer>
      <div class="dialog-footer">
        <p class="copyright">© 2025 均衡AI | 版权所有</p>
      </div>
    </template>
  </el-dialog>

  <!-- 协议弹窗 -->
  <agreement-dialog
    :visible="agreementDialogVisible"
    :agreement-type="currentAgreementType"
    @update:visible="agreementDialogVisible = $event"
  />
</template>

<script setup>
import { ref, reactive, onBeforeUnmount, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import { User, Message, Loading, Warning, Check, SuccessFilled } from '@element-plus/icons-vue';
import AgreementDialog from './AgreementDialog.vue';
import { sendSms, getSseId, getSseStatus } from '@/api/user'; // 导入相关API
import QRCode from 'qrcode';

const store = useStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'login-success']);

// 表单ref
const loginForm = ref(null);

// 控制对话框显示
const dialogVisible = ref(props.visible);

// 加载状态
const loading = ref(false);

// 选择登录类型：手机号登录或扫码登录
const loginType = ref('phone');

// 表单数据
const form = reactive({
  phoneNumber: '',
  verifyCode: ''
});

// 同意协议
const agreed = ref(true);

// 表单验证规则
const rules = {
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  verifyCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
};

// 验证码冷却时间
const cooldown = ref(0);
let cooldownTimer = null;

// 协议弹窗相关
const agreementDialogVisible = ref(false);
const currentAgreementType = ref(''); // 'user' or 'privacy'

// 二维码相关
const qrcodeContainer = ref(null);
const qrcodeLoading = ref(false);
const qrcodeExpired = ref(false);
const scanSuccess = ref(false);
const sseId = ref('');
let pollTimer = null;
let expirationTimer = null;

// 监听visible变化
const watchVisible = (val) => {
  dialogVisible.value = val;
  if (val) {
    // 重置表单和状态
    form.phoneNumber = '';
    form.verifyCode = '';
    agreed.value = true;
    cooldown.value = 0;
    loginType.value = 'phone';
    if (cooldownTimer) {
      clearInterval(cooldownTimer);
    }
    loginForm.value?.clearValidate();
  } else {
    // 清理轮询和过期计时器
    clearPollingAndTimers();
  }
};

// 监听dialogVisible变化
const watchDialogVisible = (val) => {
  emit('update:visible', val);
};

// 清理二维码相关计时器
const clearPollingAndTimers = () => {
  if (pollTimer) {
    clearInterval(pollTimer);
    pollTimer = null;
  }
  if (expirationTimer) {
    clearTimeout(expirationTimer);
    expirationTimer = null;
  }
};

// 切换登录类型
const switchLoginType = (type) => {
  if (loginType.value === type) return;
  loginType.value = type;

  // 如果切换到扫码登录，则生成二维码
  if (type === 'qrcode') {
    generateQRCode();
  } else {
    // 切换到手机号登录，清理二维码相关计时器
    clearPollingAndTimers();
  }
};

// 生成二维码
const generateQRCode = async () => {
  // 清理之前的状态和计时器
  clearPollingAndTimers();

  qrcodeLoading.value = true;
  qrcodeExpired.value = false;
  scanSuccess.value = false;

  try {
    // 获取sseId
    const res = await getSseId();
    if (res.code === 200) {
      sseId.value = res.data;
      // 生成二维码URL
      const qrcodeUrl = `https://know.chinatiye.cn/aiLogin?sseId=${sseId.value}`;

      // 使用QRCode库生成二维码
      setTimeout(() => {
        // 先清空容器
        const container = document.getElementById('qrcode');
        if (container) {
          while (container.firstChild) {
            container.removeChild(container.firstChild);
          }

          // 创建canvas元素
          const canvas = document.createElement('canvas');
          container.appendChild(canvas);

          // 使用QRCode库在canvas上生成二维码
          QRCode.toCanvas(canvas, qrcodeUrl, {
            width: 200,
            margin: 2,
            color: {
              dark: '#000',
              light: '#fff'
            }
          }, (error) => {
            if (error) {
              ElMessage.error('二维码生成失败');
              console.error(error);
              qrcodeExpired.value = true;
            } else {
              // 设置二维码过期时间（2分钟）
              expirationTimer = setTimeout(() => {
                qrcodeExpired.value = true;
              }, 2 * 60 * 1000);

              // 开始轮询扫码状态
              startPolling();
            }
            qrcodeLoading.value = false;
          });
        } else {
          qrcodeExpired.value = true;
          qrcodeLoading.value = false;
        }
      }, 300);
    } else {
      ElMessage.error(res.msg || '获取二维码失败');
      qrcodeExpired.value = true;
      qrcodeLoading.value = false;
    }
  } catch (error) {
    console.error('获取二维码失败:', error);
    qrcodeExpired.value = true;
    qrcodeLoading.value = false;
  }
};

// 开始轮询扫码状态
const startPolling = () => {
  // 每秒轮询一次
  pollTimer = setInterval(async () => {
    if (qrcodeExpired.value || scanSuccess.value) {
      clearInterval(pollTimer);
      return;
    }

    try {
      const res = await getSseStatus({ sseId: sseId.value });
      if (res.code === 200 && res.data) {
        // 扫码成功
        scanSuccess.value = true;
        clearPollingAndTimers();

        // 登录成功
        store.dispatch('user/loginByQrCode', res.data).then(() => {
            ElMessage.success('登录成功');
            dialogVisible.value = false;
            emit('login-success');
          });
      }
    } catch (error) {
      console.error('轮询二维码状态失败:', error);
    }
  }, 1000);
};

// 刷新二维码
const refreshQrCode = () => {
  // 重新生成二维码
  generateQRCode();
};

// 发送验证码
const sendVerifyCode = async () => {
  if (!form.phoneNumber) {
    ElMessage.warning('请先输入手机号');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(form.phoneNumber)) {
    ElMessage.warning('请输入正确的手机号格式');
    return;
  }

  // 开始冷却
  cooldown.value = 60;
  cooldownTimer = setInterval(() => {
    cooldown.value--;
    if (cooldown.value <= 0) {
      clearInterval(cooldownTimer);
    }
  }, 1000);

  try {
    const res = await sendSms({ phonenumber: form.phoneNumber });
    if (res.code === 200) {
       ElMessage.success(`验证码已发送`);
    } else {
       clearInterval(cooldownTimer);
       cooldown.value = 0;
    }
  } catch (error) {
    // 请求失败处理
    console.error('发送验证码失败:', error);
    // 请求失败也停止计时器
    clearInterval(cooldownTimer);
    cooldown.value = 0;
  }
};

// 提交登录
const submitLogin = async () => {
  if (!loginForm.value) return;

  if (!agreed.value) {
    ElMessage.warning('请先阅读并同意用户协议和隐私政策');
    return;
  }

  await loginForm.value.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true;
      store.dispatch('user/login', {
        phoneNumber: form.phoneNumber,
        verifyCode: form.verifyCode
      }).then(() => {
        ElMessage.success('登录成功');
        dialogVisible.value = false;
        emit('login-success');
      }).catch((error) => {
        console.error(error);
      }).finally(() => {
        loading.value = false;
      });
    } else {
      console.log('error submit!', fields);
    }
  });

};

// 显示协议弹窗
const showAgreement = (type) => {
  currentAgreementType.value = type;
  agreementDialogVisible.value = true;
};

// 在组件销毁前清除定时器
onBeforeUnmount(() => {
  if (cooldownTimer) {
    clearInterval(cooldownTimer);
  }
  clearPollingAndTimers();
});

// 监听器
watch(() => props.visible, watchVisible);
watch(() => dialogVisible.value, watchDialogVisible);
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 30px;
}

.login-header {
  text-align: center;
}

.logo {
  width: 100px;
  height: auto;
}

.title {
  font-size: 30px;
  margin-bottom: 8px;
  color: #333;
}

.login-tabs {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  margin-bottom: 20px;
  position: relative;
}

.tab-item {
  padding: 8px 20px;
  cursor: pointer;
  color: #666;
  font-size: 16px;
  transition: all 0.3s;
  position: relative;
}

.tab-item.active {
  color: #409EFF;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #409EFF;
  border-radius: 2px;
}

.login-form {
  width: 100%;
}

.verify-code-container {
  display: flex;
  gap: 10px;
  width: 100%;
}

.code-input {
  flex-grow: 1;
}

.send-code-btn {
  white-space: nowrap;
}

.login-button {
  width: 100%;
}

.agreement-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}

.agreement-section .el-checkbox {
  margin-right: 4px;
}

.agreement-section .link {
  font-size: 14px;
  vertical-align: middle;
  margin: 0 2px;
}
.agreement-section .and {
  margin: 0 2px;
}

.auto-register-tip {
  font-size: 14px;
  color: #999;
  text-align: center;
  margin-top: 10px;
}

.dialog-footer {
  text-align: center;
  padding-bottom: 20px;
}

.copyright {
  font-size: 14px;
  color: #aaa;
}

/* 二维码相关样式 */
.qrcode-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 240px; /* 确保与手机号登录表单高度相似 */
}

.qrcode-wrapper {
  width: 220px;
  height: 220px;
  border: 1px solid #eee;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.qrcode-loading, .qrcode-expired, .qrcode-success, .qrcode-agreement-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
}

.qrcode-agreement-overlay {
  background: rgba(255, 255, 255, 0.95);
  z-index: 10;
}

.qrcode-loading .el-icon, .qrcode-expired .el-icon, .qrcode-agreement-overlay .el-icon {
  font-size: 40px;
  margin-bottom: 10px;
  color: #909399;
}

.qrcode-expired .el-icon {
  color: #F56C6C;
}

.qrcode-success {
  background: rgba(255, 255, 255, 0.9);
}

.qrcode-success .success-icon {
  font-size: 50px;
  color: #67C23A;
  margin-bottom: 15px;
}

.qrcode-canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode-tip {
  font-size: 13px;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 覆盖element-plus的默认样式 */
:deep(.login-dialog .el-dialog__header) {
  padding: 0;
  margin: 0;
}
:deep(.login-dialog .el-dialog__body) {
  padding: 0;
}
:deep(.login-dialog .el-dialog__footer) {
  padding: 0;
  margin: 0;
}
</style>
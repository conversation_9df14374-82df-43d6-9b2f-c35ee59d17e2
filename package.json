{"name": "ai-tools-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "serve:dist": "vite preview --port 5173", "deploy:test": "node ./deploy/index.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iktakahiro/markdown-it-katex": "^4.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@muguilin/xf-voice-dictation": "^1.0.1", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.9.7", "js-base64": "^3.7.7", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "markdown-it-mathjax3": "^4.3.2", "node-ssh": "^13.1.0", "qrcode": "^1.5.4", "sass": "^1.86.1", "three": "^0.176.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.2.0"}}
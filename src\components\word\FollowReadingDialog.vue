<template>
  <el-dialog
    v-model="dialogVisible"
    width="100%"
    :close-on-click-modal="false"
    :before-close="handleClose"
    custom-class="follow-reading-dialog"
    top="0"
    fullscreen
    destroy-on-close
    :show-close="false"
  >
    <template #header>
      <div class="custom-dialog-header">
        <div class="dialog-title">单词跟读</div>
      </div>
    </template>

    <div class="follow-reading-container">
      <!-- 跟读卡片列表 -->
      <div class="word-cards-container">
        <div
          v-for="(word, index) in words"
          :key="word.id"
          class="follow-reading-card"
          :class="{ 'active': currentIndex === index }"
        >
          <div class="word-content">
            <div class="word-english">{{ word.english }}</div>
            <!-- <div class="word-phonetic" v-if="word.ipa">{{ word.ipa }}</div> -->
            <div class="word-chinese">{{ word.chinese }}</div>
          </div>
          <div v-if="currentIndex === index && isSpeaking" class="playing-indicator">
            <el-icon class="speaker-icon"><Microphone /></el-icon>
          </div>
        </div>
      </div>

      <!-- 底部控制栏 -->
      <div class="bottom-controls">
        <!-- 跟读设置 -->
        <div class="setting-item">
          <span class="setting-label">跟读间隔：</span>
          <el-radio-group v-model="readingInterval" size="large">
            <el-radio-button :label="1">1秒</el-radio-button>
            <el-radio-button :label="2">2秒</el-radio-button>
            <el-radio-button :label="3">3秒</el-radio-button>
          </el-radio-group>
        </div>

        <div class="setting-item">
          <span class="setting-label">单词跟读次数：</span>
          <el-input-number
            v-model="repeatCount"
            :min="1"
            :max="10"
            size="large"
            controls-position="right"
          />
        </div>

        <!-- 控制按钮 -->
        <div class="control-buttons">
          <el-button
            size="large"
            @click="handleClose"
            class="close-button"
            round
          >
            关闭
          </el-button>
          
          <el-button
            :type="isPlaying ? 'warning' : 'primary'"
            size="large"
            @click="togglePlayPause"
            class="control-button"
            :class="{ 'pause-button': isPlaying }"
            round
          >
            <el-icon v-if="isPlaying" class="button-icon"><VideoPause /></el-icon>
            <el-icon v-else class="button-icon"><VideoPlay /></el-icon>
            {{ isPlaying ? '暂停跟读' : (isPaused ? '继续跟读' : '开始跟读') }}
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, defineComponent, watch, computed, onMounted, onBeforeUnmount } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Microphone, VideoPlay, VideoPause } from '@element-plus/icons-vue'
import { getFollowReadingSetting, saveFollowReadSetting, saveWordRecord } from '../../api/word'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'FollowReadingDialog',
  components: {
    Microphone,
    VideoPlay,
    VideoPause
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedWords: {
      type: Array,
      required: true
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const store = useStore()
    const dialogVisible = ref(props.visible)
    const userId = computed(() => store.getters.userId)

    // 设置默认值，后续会从API获取
    const readingInterval = ref()
    const repeatCount = ref()
    const settingsLoading = ref(false)
    const settingsSaving = ref(false)
    const isSettingFromApi = ref(false) // 标记是否是从API加载设置

    const isPlaying = ref(false)
    const isPaused = ref(false)
    const isSpeaking = ref(false)
    const currentIndex = ref(0)
    const currentWordRepeatCount = ref(0) // 当前单词已重复的次数

    // 跟读记录是否已保存（每次打开弹窗只保存一次）
    const recordSaved = ref(false)

    // 从API获取用户跟读设置
    const fetchUserSettings = async () => {
      if (!userId.value) return

      settingsLoading.value = true
      try {
        const response = await getFollowReadingSetting(userId.value)
        if (response && response.data) {
          // 标记为从API加载设置，避免触发保存
          isSettingFromApi.value = true

          // 设置跟读间隔和次数
          readingInterval.value = response.data.timeInterval
          repeatCount.value = response.data.readTimes

          // 延迟重置标记，确保watch不会触发保存
          setTimeout(() => {
            isSettingFromApi.value = false
          }, 100)
        }
      } catch (error) {
        console.error('获取跟读设置失败:', error)
      } finally {
        settingsLoading.value = false
      }
    }

    // 保存用户跟读设置
    const saveUserSettings = async () => {
      if (!userId.value) return

      settingsSaving.value = true
      try {
        const data = {
          uid: userId.value,
          timeInterval: readingInterval.value,
          readTimes: repeatCount.value
        }

        await saveFollowReadSetting(data)
      } catch (error) {
        console.error('保存跟读设置失败:', error)
      } finally {
        settingsSaving.value = false
      }
    }

    let readingTimer = null

    // 音频播放元素
    let audioElement = null

    const words = computed(() => props.selectedWords)

    const initSpeech = () => {
      try {
        // 创建音频元素
        audioElement = new Audio()

        // 设置音频事件监听
        audioElement.onended = () => {
          console.log('Audio playback ended')
          handleSpeechEnd()
        }

        audioElement.onerror = (event) => {
          console.error('Audio playback error:', event)
          handleSpeechEnd()
        }

        return true
      } catch (error) {
        console.error('初始化音频播放失败:', error)
        return false
      }
    }

    const speakWord = (wordObj) => {
      if (!audioElement) {
        console.error('音频播放元素未初始化')
        setTimeout(handleSpeechEnd, readingInterval.value * 1000)
        return false
      }

      try {
        // 停止当前正在播放的音频
        audioElement.pause()
        audioElement.currentTime = 0
      } catch (e) {
        console.error('停止音频播放失败:', e)
      }

      // 获取单词的音频路径
      const audioUrl = wordObj.enVoicePath
      if (!audioUrl) {
        console.error('单词没有音频路径:', wordObj.english)
        setTimeout(handleSpeechEnd, readingInterval.value * 1000)
        return false
      }

      console.log('播放单词音频:', wordObj.english, audioUrl)
      isSpeaking.value = true

      try {
        // 设置音频源
        audioElement.src = audioUrl

        // 设置音频播放结束事件
        audioElement.onended = () => {
          console.log('音频播放结束:', wordObj.english)
          isSpeaking.value = false
          handleSpeechEnd()
        }

        // 设置音频播放错误事件
        audioElement.onerror = (event) => {
          console.error('音频播放错误:', wordObj.english, event)
          isSpeaking.value = false
          handleSpeechEnd()
        }

        // 播放音频
        audioElement.play().catch(error => {
          console.error('音频播放失败:', error)
          isSpeaking.value = false
          setTimeout(handleSpeechEnd, readingInterval.value * 1000)
        })

        return true
      } catch (error) {
        console.error('播放单词音频失败:', error)
        isSpeaking.value = false
        setTimeout(handleSpeechEnd, readingInterval.value * 1000)
        return false
      }
    }

    // 保存跟读记录
    const saveReadingRecord = async () => {
      // 如果记录已经保存过，则不再保存
      if (recordSaved.value) {
        console.log('跟读记录已保存，本次会话不再重复保存')
        return
      }

      if (!userId.value || words.value.length === 0) {
        return
      }

      try {
        // 获取所有单词的章节ID，去重
        const periodIds = [...new Set(words.value.map(word => word.periodId))].join(',')

        // 构建单词列表
        const itemList = words.value.map(word => ({
          wordId: word.id,
          periodId: word.periodId
        }))

        // 构建请求数据
        const data = {
          uid: userId.value,
          bookId: words.value[0].bookId,
          periodIds: periodIds,
          type: 'read',
          itemList: itemList
        }

        // 调用API保存跟读记录
        await saveWordRecord(data)
        recordSaved.value = true
      } catch (error) {
        console.error('保存跟读记录错误:', error)
      }
    }

    const startReading = () => {
      if (!initSpeech()) {
        console.error('初始化音频播放失败')
        return
      }

      // 保存跟读记录（只在开始跟读时调用，不在继续跟读时调用）
      if (!isPaused.value) {
        saveReadingRecord()
      }

      isPlaying.value = true
      isPaused.value = false

      if (currentIndex.value >= words.value.length) {
        currentIndex.value = 0
        currentWordRepeatCount.value = 0
      }

      readCurrentWord()
    }

    const readCurrentWord = () => {
      if (currentIndex.value < words.value.length && isPlaying.value) {

        setTimeout(() => {
          if (!isPlaying.value) return;

          // 传递整个单词对象，而不仅仅是英文文本
          const success = speakWord(words.value[currentIndex.value])

          if (!success) {
            setTimeout(moveToNextWord, readingInterval.value * 1000)
          }
        }, 100);
      }
    }

    const handleSpeechEnd = () => {
      if (!isPlaying.value) return

      if (readingTimer) {
        clearTimeout(readingTimer)
        readingTimer = null
      }

      readingTimer = setTimeout(() => {
        moveToNextWord()
      }, readingInterval.value * 1000)
    }

    const moveToNextWord = () => {
      if (!isPlaying.value) return

      if (readingTimer) {
        clearTimeout(readingTimer)
        readingTimer = null
      }

      // 增加当前单词的重复次数
      currentWordRepeatCount.value++

      // 检查当前单词是否已经播放了指定的次数
      if (currentWordRepeatCount.value < repeatCount.value) {
        // 如果还没有达到重复次数，继续播放当前单词
        setTimeout(() => {
          if (isPlaying.value) {
            readCurrentWord()
          }
        }, 500)
      } else {
        // 重置当前单词重复次数，并移动到下一个单词
        currentWordRepeatCount.value = 0
        currentIndex.value++

        // 检查是否已经播放完所有单词
        if (currentIndex.value >= words.value.length) {
          // 所有单词都已播放完成
          isPlaying.value = false
          isPaused.value = false
          isSpeaking.value = false
          currentIndex.value = 0
          currentWordRepeatCount.value = 0
        } else {
          // 继续播放下一个单词
          setTimeout(() => {
            if (isPlaying.value) {
              readCurrentWord()
            }
          }, 500)
        }
      }
    }


    const pauseReading = () => {
      isPlaying.value = false
      isPaused.value = true
      isSpeaking.value = false

      if (readingTimer) {
        clearTimeout(readingTimer)
        readingTimer = null
      }

      if (audioElement) {
        try {
          audioElement.pause()
          audioElement.currentTime = 0
        } catch (error) {
          console.error('暂停音频播放失败:', error)
        }
      }

      const highestTimeoutId = setTimeout(() => {}, 0);
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
      }
    }

    const togglePlayPause = () => {
      if (isPlaying.value) {
        pauseReading()
      } else {
        startReading()
      }
    }

    const handleClose = () => {
      if (isPlaying.value) {
        ElMessageBox.confirm(
          '跟读进行中，确定要退出吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          cleanupAndClose()
        }).catch(() => {
        })
      } else {
        cleanupAndClose()
      }
    }

    const cleanupAndClose = () => {
      if (isPlaying.value) {
        pauseReading()
      }

      isPlaying.value = false
      isPaused.value = false
      isSpeaking.value = false
      currentIndex.value = 0
      currentWordRepeatCount.value = 0

      // 重置记录保存状态，以便下次打开弹窗时可以再次保存
      recordSaved.value = false

      if (readingTimer) {
        clearTimeout(readingTimer)
        readingTimer = null
      }

      // 清理音频元素
      if (audioElement) {
        try {
          audioElement.pause()
          audioElement.currentTime = 0
          audioElement.src = ''
          audioElement.onended = null
          audioElement.onerror = null
        } catch (error) {
          console.error('清理音频元素失败:', error)
        }
      }

      // 移除事件监听器
      window.removeEventListener('popstate', handlePopState)

      dialogVisible.value = false
      emit('update:visible', false)
    }

    // 处理浏览器回退按钮
    const handlePopState = (event) => {
      if (dialogVisible.value) {
        // 如果对话框是打开的，执行关闭操作
        handleClose()

        // 阻止默认的回退行为，因为我们已经处理了关闭操作
        event.preventDefault()
        return false
      }
    }

    // 监听对话框可见性变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal

      if (newVal) {
        currentIndex.value = 0
        currentWordRepeatCount.value = 0
        isPlaying.value = false
        isPaused.value = false
        isSpeaking.value = false

        // 重置记录保存状态，以便可以再次保存
        recordSaved.value = false

        initSpeech()

        // 当对话框打开时，获取用户设置
        fetchUserSettings()

        // 添加历史记录状态，用于处理浏览器回退
        window.history.pushState({ followReadingDialog: true }, '')

        // 添加popstate事件监听器
        window.addEventListener('popstate', handlePopState)
      } else {
        // 对话框关闭时，移除事件监听器
        window.removeEventListener('popstate', handlePopState)
      }
    })

    watch(() => dialogVisible.value, (newVal) => {
      emit('update:visible', newVal)
    })

    // 监听跟读间隔变化，保存设置
    watch(readingInterval, () => {
      if (dialogVisible.value && userId.value && !isSettingFromApi.value) {
        // 只有在不是从API加载设置时才保存
        saveUserSettings()
      }
    })

    // 监听跟读轮数变化，保存设置
    watch(repeatCount, () => {
      if (dialogVisible.value && userId.value && !isSettingFromApi.value) {
        // 只有在不是从API加载设置时才保存
        saveUserSettings()
      }
    })

    // 组件挂载时初始化
    onMounted(() => {
      // 如果对话框已经可见，则获取用户设置
      if (dialogVisible.value) {
        fetchUserSettings()
      }
    })

    // 组件卸载前清理
    onBeforeUnmount(() => {
      // 确保移除事件监听器
      window.removeEventListener('popstate', handlePopState)
    })

    return {
      dialogVisible,
      readingInterval,
      repeatCount,
      isPlaying,
      isPaused,
      isSpeaking,
      currentIndex,
      currentWordRepeatCount,
      words,
      settingsLoading,
      settingsSaving,
      togglePlayPause,
      handleClose,
      moveToNextWord,
      handleSpeechEnd,
      isSettingFromApi
    }
  }
})
</script>

<style scoped>
.follow-reading-dialog {
  display: flex;
  flex-direction: column;
}

.follow-reading-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 6rem);
  width: 100%;
  box-sizing: border-box;
}

/* 底部控制栏 */
.bottom-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2rem;
  padding: 1rem;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

:deep(.setting-item .el-radio-button__inner){
  font-size: 14px !important;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.setting-label {
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.word-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: flex-start;
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0.5rem;
  align-content: flex-start;
  width: 100%;
  margin-bottom: 0;
}

.follow-reading-card {
  background-color: #f0f7ff;
  background-image: linear-gradient(to bottom, #f8fbff, #e8f4ff);
  border-radius: 0.8rem;
  padding: 1rem 0.5rem;
  border: 1px solid #e0e9f6;
  transition: all 0.3s ease;
  width: calc(12.5% - 0.5rem);
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  margin: 0;
  box-sizing: border-box;
}

.follow-reading-card.active {
  background-color: #ecf5ff;
  background-image: linear-gradient(to bottom, #f0f7ff, #d8ebff);
  border: 2px solid #409eff;
  transform: scale(1.03);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  z-index: 1;
}

.word-content {
  text-align: center;
  width: 100%;
}

.word-english {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
  color: #333;
}

.word-phonetic {
  font-size: 1rem;
  color: #606266;
  font-style: italic;
  margin-bottom: 0.3rem;
}

.word-chinese {
  font-size: 1.2rem;
  color: #666;
}

.playing-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #409eff;
  animation: pulse 1.5s infinite;
}

.speaker-icon {
  font-size: 1.8rem;
}



.control-button {
  padding: 2rem 1rem !important;
  height: 3.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

:deep(.control-button span) {
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  padding: 2rem 2rem !important;
  height: 3.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  background-color: #909399;
  border-color: #909399;
  color: white;
}
:deep(.close-button span){
  font-size: 1.5rem;
}

.close-button:hover {
  background-color: #7d7d7d;
  border-color: #7d7d7d;
  color: white;
}

.pause-button {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

.pause-button:hover {
  background-color: #d39e2c;
  border-color: #d39e2c;
  color: white;
}

.button-icon {
  margin-right: 0.5rem;
  font-size: 1.6rem;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

/* Responsive design for different screen sizes */
@media (max-width: 1800px) {
  .follow-reading-card {
    width: calc(16.666% - 0.5rem); /* 6 cards per row */
  }
}

@media (max-width: 1400px) {
  .follow-reading-card {
    width: calc(20% - 0.5rem); /* 5 cards per row */
  }
}

@media (max-width: 1200px) {
  .follow-reading-card {
    width: calc(25% - 0.5rem); /* 4 cards per row */
  }
}

@media (max-width: 992px) {
  .follow-reading-card {
    width: calc(33.333% - 0.5rem); /* 3 cards per row */
  }
  
  .bottom-controls {
    gap: 2rem;
    padding: 1.2rem 1.5rem;
  }
  
  .setting-label {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .follow-reading-card {
    width: calc(50% - 0.5rem); /* 2 cards per row */
  }
  
  .bottom-controls {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .setting-item {
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
    text-align: center;
  }
  
  .control-buttons {
    flex-direction: row;
    gap: 1rem;
  }
  
  .dialog-title {
    font-size: 2.5rem;
  }
  
  .setting-label {
    font-size: 1rem;
  }
  
  .control-button {
    padding: 0 1.5rem;
    height: 3rem;
    font-size: 1rem;
  }
  
  .close-button {
    padding: 0 1.5rem;
    height: 3rem;
    font-size: 1rem;
  }
  
  .el-radio-button--large .el-radio-button__inner {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  :deep(.setting-item .el-radio-button__inner) {
    font-size: 12px !important;
  }
}

/* Custom dialog header styles */
.custom-dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* Override Element Plus dialog styles */
:deep(.el-dialog) {
  margin: 0 !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: auto;
  padding: 0;
}
</style>

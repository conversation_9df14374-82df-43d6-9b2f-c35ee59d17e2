import { getChatList, saveChat, saveChatRecord } from '@/api/home'
import { ElMessage } from 'element-plus'

class AgentChatService {
  constructor(store) {
    this.store = store
    this.chatId = null
    this.category = null
    this.title = null
    this.initialized = false
  }
  
  // 初始化服务
  async initialize(category, title) {
    if (!this.store.getters.isLoggedIn) {
      return false
    }
    
    this.category = category
    this.title = title
    
    try {
      // 获取用户ID
      const userId = this.store.getters.userId
      
      // 查询会话窗口
      const response = await getChatList({
        userId,
        category
      })
      
      if (response && response.code === 200) {
        const chatList = response.data || []
        
        if (chatList.length > 0) {
          // 存在会话窗口，使用第一个
          this.chatId = chatList[0].id
          this.initialized = true
          return true
        } else {
          // 不存在会话窗口，创建新窗口
          return await this.createChatWindow()
        }
      } else {
        return false
      }
    } catch (error) {
      console.error('初始化会话错误:', error)
      return false
    }
  }
  
  // 创建会话窗口
  async createChatWindow() {
    try {
      const userId = this.store.getters.userId
      
      const response = await saveChat({
        userId,
        title: this.title,
        category: this.category
      })
      
      if (response && response.code === 200) {
        this.chatId = response.data
        this.initialized = true
        return true
      } else {
        return false
      }
    } catch (error) {
      console.error('创建会话窗口错误:', error)
      return false
    }
  }
  
  // 保存对话记录
  async saveMessages(userMessage, assistantMessage) {
    if (!this.initialized || !this.chatId) {
      ElMessage.warning('会话未初始化，请先登录')
      return false
    }
    
    try {
      const userId = this.store.getters.userId
      
      // 构建消息数组
      const messages = [
        {
          role: 'user',
          content: userMessage
        }
      ]
      
      // 如果有助手回复，添加到消息数组
      if (assistantMessage) {
        messages.push({
          role: 'assistant',
          content: assistantMessage
        })
      }
      
      // 保存对话记录
      const response = await saveChatRecord({
        userId,
        chatId: this.chatId,
        messages
      })
      
      if (response && response.code === 200) {
        return true
      } else {
        return false
      }
    } catch (error) {
      console.error('保存对话记录错误:', error)
      return false
    }
  }
}

export default AgentChatService 
<template>
  <div class="article-container" ref="containerRef">
    <agent-history-entry
        category="SpeakText"
        @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">AI演讲稿</h1>
      <p class="subtitle">借助先进的AI技术，帮助用户快速生成具备逻辑清晰、语言生动、主题精准等特色的演讲稿。</p>
    </div>

    <div class="form-container">
      <el-form :model="formData" label-width="120px" class="article-form" size="large">
        <el-form-item label="演讲角色" required>
          <div class="style-buttons">
            <el-button-group>
              <el-button
                  v-for="item in noticeTypeOptions"
                  :key="item.value"
                  :type="formData.noticeType === item.value ? 'primary' : ''"
                  @click="formData.noticeType = item.value"
                  round
              >
                {{ item.label }}
              </el-button>
            </el-button-group>
          </div>
        </el-form-item>

        <el-form-item label="演讲主题" required>
          <el-input
              v-model="formData.briefDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入演讲稿的主要内容简述，例如：点亮梦想，勇敢前行"
              resize="none"
          />
        </el-form-item>

        <el-form-item label="字数限制" required>
          <div class="style-buttons">
            <el-button-group>
              <el-button
                  v-for="item in wordCountOptions"
                  :key="item.value"
                  :type="formData.wordCount === item.value ? 'primary' : ''"
                  @click="formData.wordCount = item.value"
                  round
              >
                {{ item.label }}
              </el-button>
            </el-button-group>
          </div>
        </el-form-item>
        <el-form-item label="参考附件">

            <el-upload
                ref="uploadRef"
                class="file-uploader"
                :action="serverUrl +'/junhengai/ppt/parseFileData'"
                :headers="{
                 'Authorization': `Bearer ${store.getters.aiToken}`
                }"
                drag
                :on-remove="handleFileRemove"
                :on-success="handleSuccess"
                :limit="1"
                :on-exceed="handleExceed"
                accept=".doc,.docx,.pdf,.txt,.md"
            >
              <div class="upload-content">
                <img src="@/assets/upload-icon.svg" class="upload-icon" alt="上传图标"/>
                <div>
                  <div class="upload-text">将文件拖到此处，或<em>点击上传</em></div>
                  <div class="el-upload__tip">
                    支持 docx、pdf、txt 格式，大小不超过150MB
                  </div>
                </div>
              </div>
            </el-upload>
        </el-form-item>

        <div style="display: flex; justify-content: center; width: 100%;" v-if="!loading">
          <el-button
              :type="loading ? 'warning' : 'primary'"
              size="large"
              v-click-throttle="handleGenerate"
              :disabled="!isFormValid"
              round
          >
            {{ loading ? '中止生成' : (noticeResult ? '重新生成' : '开始生成') }}
          </el-button>
        </div>
      </el-form>
    </div>

    <ai-loading :visible="showAiLoading" />

    <div v-if="noticeResult" class="result-container">
      <div class="result-content">
        <div class="section-title-container">
          <div class="section-title">演讲稿内容：</div>
          <div class="action-icons">
            <el-icon style="cursor: pointer;" @click="copyResult">
              <CopyDocument/>
            </el-icon>
            <agent-save-button
                v-if="!loading && noticeResult"
                category="SpeakText"
                :user-question="savePrompt()"
                :model-answer="noticeResult"
                :chat-service="chatService"
                :already-saved="alreadySaved"
                @save-success="alreadySaved = true"
            />
          </div>
        </div>
        <div class="comment-text" v-html="renderedMarkdown(noticeResult)"></div>
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;" v-if="loading && noticeResult">
          <el-button
            type="warning"
            size="large"
            v-click-throttle="handleGenerate"
            round
          >中止生成 </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {ref, computed, nextTick, watch, onMounted, inject} from 'vue'
import {CopyDocument} from '@element-plus/icons-vue'
import {ElMessage} from 'element-plus'
import MarkdownIt from "markdown-it"
import {fetchEventSource} from '@microsoft/fetch-event-source'
import {useStore} from 'vuex'
import AgentSaveButton from '@/components/common/AgentSaveButton.vue'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import {getFileIdByUpload} from "@/api/home.js";
import {createApiToken, generateOutlineV2} from "@/api/verse.js";
import AiLoading from '@/components/common/AiLoading.vue'

const roleSystem = `
# 角色设定
你是一名专业的演讲内容策划专家，熟悉不同场景、不同受众的演讲风格与表达需求。

# 任务目标
根据用户提供的**演讲场合（如学校、公司、公开比赛等）和演讲角色（如教师、家长、学生） **，撰写一篇贴合情境、语言得体、结构清晰的演讲稿。

# 撰写要求

1. **贴合场景**：演讲内容应符合用户所指定场合的语言风格与表达规范，如正式、半正式或轻松幽默。
2. **主题鲜明**：若用户提供演讲主题，则围绕该主题展开；若未提供，则自拟一个贴近听众生活、具有启发性的主题。
3. **积极正面**：内容健康向上，富有感染力，能够激发听众的情感共鸣或引发思考。
4. **结构清晰**：开头引入自然，正文逻辑分明，结尾有力升华，具备良好的演讲节奏与表现力。
5. **语言生动**：用词准确恰当，句式灵活多变，善于运用修辞手法或实例增强演讲的吸引力和说服力。
# 内容要求
1.不要展示关于字数长度的内容（如：字数:498）

请确保演讲稿内容鼓舞人心，能够帮助用户提升表达效果并增强演讲信心。
`

export default {
  name: 'SpeakText',
  components: {
    CopyDocument,
    AgentSaveButton,
    AgentHistoryEntry,
    AiLoading
  },
  setup() {

    const trackingService = inject('trackingService');
    const store = useStore()
    const formData = ref({
      noticeType: '教师',
      briefDescription: '',
      wordCount: 300
    })

    const serverUrl = ref(import.meta.env.VITE_BASE_URL)
    const noticeResult = ref(null)
    const loading = ref(false)
    const reader = ref(null)
    const containerRef = ref(null)
    const chatService = ref(null) // 聊天服务实例
    const alreadySaved = ref(false) // 是否已保存当前对话

    // 通知类型选项
    const noticeTypeOptions = [
      {value: '教师', label: '教师'},
      {value: '学生', label: '学生'},
      {value: '家长', label: '家长'},
    ]

    // 字数选项
    const wordCountOptions = [

      {value: 300, label: '300字'},
      {value: 500, label: '500字'},
      {value: 800, label: '800字'},
      {value: 1000, label: '1000字'}
    ]

    const showAiLoading = computed(() => loading.value && !noticeResult.value)

    // 表单验证
    const isFormValid = computed(() => {
      return formData.value.noticeType &&
          formData.value.briefDescription.trim() !== '' &&
          formData.value.wordCount
    })

    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };

    // 监听结果变化，触发滚动
    watch(noticeResult, () => {
      scrollToBottom();
    });

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('SpeakText', 'AI演讲稿智能体会话')
          console.log('AI演讲稿智能体重新初始化完成')
        }
      } catch (error) {
        console.error('AI演讲稿智能体重新初始化错误:', error)
      }
    }

    // 初始化聊天服务
    onMounted(async () => {
      try {

        chatService.value = new AgentChatService(store)
        await chatService.value.initialize('SpeakText', 'AI演讲稿智能体会话')
        console.log('AI演讲稿智能体初始化完成')
        await  getPPTToken()
      } catch (error) {
        console.error('初始化错误:', error)
      }
    })

    // 构建提示词方法
    const buildPrompt = () => {
      let prompt = `请根据以下信息生成一份演讲角色为${formData.value.noticeType}，字数控制在${formData.value.wordCount}字左右。请尽量使用接近${formData.value.wordCount}字的内容，确保演讲稿内容详细充实。`
      prompt += `\n\n演讲主题：${formData.value.briefDescription}。`
      if (fileId.value) {
        prompt += `请严格按照我上传的附件内容要求进行生成演讲稿，附件内容如下：${outline.value.slice(0,2000)}。`
      }
      return prompt
    }

    const savePrompt = () => {
      let prompt = ``

      if (formData.value.noticeType) {
        prompt += `演讲角色：${formData.value.noticeType}，`
      }

      if (formData.value.briefDescription) {
        prompt += `演讲主题：${formData.value.briefDescription}，`
      }

      if (formData.value.wordCount) {
        prompt += `字数限制：${formData.value.wordCount}字`
      }

      return prompt
    }

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          // reader.value 现在是 AbortController
          reader.value.abort('用户取消了操作');
          console.log('Abort signal sent.');
        } catch (error) {
          console.error('取消流式输出错误:', error);
        } finally {
          // 立即重置状态，确保按钮恢复
          loading.value = false;
          reader.value = null; // 清理 controller 引用
        }
      }
    }

    // 复制通知结果
    const copyResult = () => {
      if (!noticeResult.value) return

      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div')
      tempElement.innerHTML = renderedMarkdown(noticeResult.value)

      // 获取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText

      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = textContent
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 使用现代剪贴板API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(textContent)
              .then(() => {
                ElMessage.success('演讲稿内容已复制到剪贴板')
              })
              .catch((err) => {
                console.error('现代API复制失败:', err)
                // 降级使用旧方法
                document.execCommand('copy')
                ElMessage.success('演讲稿内容已复制到剪贴板')
              })
        } else {
          // 降级使用旧方法
          document.execCommand('copy')
          ElMessage.success('演讲稿内容已复制到剪贴板')
        }
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败')
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }

    // 生成通知
    const handleGenerate = async () => {
      if (!isFormValid.value) {
        ElMessage.error('请填写必填项')
        return
      }

      if (loading.value && reader.value) {
        await cancelStream()
        return
      }

      noticeResult.value = null
      loading.value = true
      alreadySaved.value = false // 重置保存状态

      // 创建AbortController用于取消请求
      const controller = new AbortController();
      reader.value = controller; // 将 controller 赋值给 reader 用于取消

      try {
        // 构建提示词
        let prompt = buildPrompt()

        // 构建请求体
        // let message = [{"role": "system", "content": roleSystem}]
        let message = []
        // 如果有上传文件，添加fileId
        if (fileId.value) {
          message.push({
            role: 'system',
            content: `fileid://${fileId.value}`
          })
        }
        message.push({
          "role": "user",
          "content": prompt
        })

        let requestBody = {
          messages: message,
          stream: true,
          modelType: 'open',
          formType: 'speak_text'
        }

        let accumulatedText = '';
        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`
        fetchEventSource(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal, // 传递 signal 用于取消
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
              loading.value = false; // 出错时停止加载状态
              reader.value = null;
              throw new Error(`HTTP error! status: ${response.status}`);
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              loading.value = false;
              reader.value = null;
              throw new Error(msg.data);
            }

            const data = msg.data;

            try {
              console.log(data)

               const jsonData = JSON.parse(data);

               if (jsonData.error) {
                 loading.value = false;
                 reader.value = null;
                 return;
               }

               if (jsonData.choices && jsonData.choices[0]) {
                 const delta_content = jsonData.choices[0].message.content
                 if (delta_content) {
                   accumulatedText += delta_content;
                   noticeResult.value = accumulatedText;
                   scrollToBottom();
                 }
               }
               // 处理可能的输出格式差异
               if (jsonData.output && jsonData.output.text) {
                 accumulatedText += jsonData.output.text;
                 noticeResult.value = accumulatedText;
                 scrollToBottom();
               }


            } catch (error) {
              console.error('JSON parse error:', error);
            }
          },
          async onclose() {
            console.log('连接关闭');
            // onclose 可能在收到所有数据后触发
            if (loading.value) { // 只有在仍在加载时才设置，避免覆盖[DONE]设置的状态
              loading.value = false;
              reader.value = null;
            }
          },
          onerror(err) {
            console.error('Stream error:', err);
            if (err.name !== 'AbortError') {
            } else {
              console.log('Stream aborted by user.');
              // 用户取消，状态已在 cancelStream 中处理
            }
            // 确保状态被重置，即使在AbortError情况下，以防万一
            loading.value = false;
            reader.value = null;
          }
        });
      } catch (error) {
        console.error('请求错误:', error);
        loading.value = false;
        reader.value = null;
        ElMessage.error('请求出错，请重试');
      }
      trackingService.trackEvent('SpeakText')
    }

    // Markdown渲染
    const renderedMarkdown = (markdownContent) => {
      if (!markdownContent) return

      const md = new MarkdownIt()
      const htmlContent = md.render(markdownContent)
      return htmlContent
    }
    const fileList = ref([])
    const uploadRef = ref(null)
    const fileId = ref(null)
    // 处理文件变化
    const handleFileChange = async (file) => {
      if (file.status === 'ready') {
        // 更新文件列表，只保留最新的文件
        fileList.value = [file]

        // 上传文件获取fileId
        try {
          const formDataObj = new FormData()
          formDataObj.append('file', file.raw)

          const response = await getFileIdByUpload(formDataObj)
          console.log('response', response)
          if (response && response.code === 200) {
            fileId.value = response.data.id
            console.log('文件上传成功，fileId:', fileId.value)
          } else {
            ElMessage.error('文件上传失败')
            fileList.value = []
          }
        } catch (error) {
          console.error('文件上传错误:', error)
          ElMessage.error('文件上传失败')
          fileList.value = []
        }
      }
    }
    const dataUrl = ref(null)
    const outline = ref('')
    async function fetchTxtFileContent(url) {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const text = await response.text();
        console.log('文件内容:', text);
        return text;
      } catch (error) {
        console.error('获取文件内容时出错:', error);
      }
    }
    //   上传内容成功
    const handleSuccess = async (response, file, fileList) => {
      console.log('----上传成功-----')
      const formDataObj = new FormData()
      formDataObj.append('file', file.raw)
      const res = await getFileIdByUpload(formDataObj)
      if (res && res.code === 200) {
        fileId.value = res.data.id

      } else {

        fileList.value = []
      }

      if (response.code === 200) {

        dataUrl.value = response.data
        if (dataUrl.value) {
         const txt = await fetchTxtFileContent(dataUrl.value+'?token='+token.value)
          // 输出文档大纲内容
          outline.value = txt

        }
        ElMessage.success('文件上传成功')
      } else {
        ElMessage.error('文件上传失败')
      }
    }
    // 处理文件超出限制（直接覆盖旧文件）
    const handleExceed = (files) => {
      // 先清除当前文件
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
      }
      fileList.value = []
      fileId.value = null
      outline.value = ''
      // 添加新文件
      const file = files[0]
      uploadRef.value.handleStart(file)

      // 直接处理新文件
      handleFileChange(file)
    }
    // 处理文件移除
    const handleFileRemove = () => {
      fileList.value = []
      fileId.value = null
      outline.value = ''
    }
    const token = ref('')
    // 获取PPTtoken
const getPPTToken =async ()=>{
  const data = await createApiToken({
    uid:'text',
    limit: null
  })
  token.value = data.data
}
    return {
      formData,
      noticeTypeOptions,
      wordCountOptions,
      noticeResult,
      loading,
      isFormValid,
      handleGenerate,
      renderedMarkdown,
      containerRef,
      copyResult,
      CopyDocument,
      chatService,
      buildPrompt,
      savePrompt,
      alreadySaved,
      reinitializeChatSession,
      handleFileChange,
      handleExceed,
      fileList,
      uploadRef,
      fileId,
      serverUrl,
      store,
      getPPTToken,
      handleFileRemove,
      handleSuccess,
      showAiLoading
    }
  }
}
</script>

<style scoped lang="scss">
.article-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "📢";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 255, 0.08);
}

.article-form {
  width: 100%;
  margin: 0 auto;
}

.style-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-content {
  text-align: left;
  padding: 2rem;
  position: relative;
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.comment-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

.action-icons {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .article-container {
    width: 100%;
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0;
  }

  .comment-text {
    font-size: 1rem;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .form-container {
    margin: 2rem 0;
    padding: 1rem;
  }

  .result-content {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .comment-text {
    padding: 1rem;
  }
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

.file-uploader {


  :deep(.el-upload-dragger) {
    //padding: 0 !important;
    //height: 100%;
  }

  //:deep(.upload-content) {
  //  display: flex;
  //  align-items: center;
  //  flex-direction: row;
  //  justify-content: center;
  //}
  //:deep(.upload-icon){
  //  margin-bottom: 0;
  //  margin-right: 30px;
  //}
}


</style>
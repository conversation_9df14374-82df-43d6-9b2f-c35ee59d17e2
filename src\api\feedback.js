import request from './junheng'

// 字典类型
export function getDictData(params) {
  return request({
    url: `/ebmservice/v2/sysEnumValue/selectList?dicType=feedback_type`,
    method: 'get',
    params
  })
}

// 用户-新增意见反馈
export function addUserFeedback(data) {
  return request({
    url: `/ebmservice/v3/sys/suggestion`,
    method: 'post',
    data
  })
}

// 用户-查询意见反馈未读消息
export function getUserFeedbackCount(data) {
  return request({
    url: `/ebmservice/v3/sys/suggestion/messageNum`,
    method: 'post',
    data
  })
}

// 用户-查询意见反馈列表
export function getUserFeedbackList(data) {
  return request({
    url: `/ebmservice/v3/sys/suggestion/list`,
    method: 'post',
    data
  })
}

// 用户-满意度操作
export function operateUserFeedback(data) {
  return request({
    url: `/ebmservice/v3/sys/suggestion/isGoodPlay`,
    method: 'post',
    data
  })
}

// 用户-读取意见反馈
export function readUserFeedback(data) {
  return request({
    url: `/ebmservice/v3/sys/suggestion/isRead`,
    method: 'post',
    data
  })
}

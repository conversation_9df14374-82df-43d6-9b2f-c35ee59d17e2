import request from './request'

export function getAppList(params) {
  return request({
    url: '/junhengai/system/appStoreType/v1/qryAiStoreTree',
    method: 'get',
    params
  })
}


export function getAgentList(params) {
  return request({
    url: '/junhengai/system/store/api/appList',
    method: 'get',
    params: { storeType: 'Agent' }
  })
}

export function getToolList(params) {
  return request({
    url: '/junhengai/system/store/api/appList',
    method: 'get',
    params: { storeType: 'Tool' }
  })
}


// 根据用户id查询会话窗口
export function getChatList(data) {
  return request({
    url: '/junhengai/window/chat/chatList',
    method: 'post',
    data
  })
}

// 保存用户会话窗口
export function saveChat(data) {
  return request({
    url: '/junhengai/window/chat',
    method: 'post',
    data
  })
}

// 删除用户会话窗口
export function deleteChat(id) {
  return request({
    url: `/junhengai/window/chat/${id}`,
    method: 'delete',
  })
}

// 保存当前窗口的聊天记录
export function saveChatRecord(data) {
  return request({
    url: '/junhengai/window/chat/chatInfo',
    method: 'post',
    data
  })
}

// 会话记录明细删除
export function deleteChatRecord(id) {
  return request({
    url: `/junhengai/window/chat/chatInfo/${id}`,
    method: 'delete'
  })
}

// 上传文件->百炼，获取到file-id
export function getFileIdByUpload(data) {
  return request({
    url: '/junhengai/chat/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 获取上传附件返回纯文本
export function getPPTXText(data) {
  return request({
    url: '/junhengai/chat/extractUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}


// 智能体上传附件
export function getSessionFileId(data) {
  return request({
    url: '/junhengai/yxAgent/v2/upload/file',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 上传阿里云集成接口
export function uploadAliyun(data) {
  return request({
    url: `${import.meta.env.VITE_IMAGE_CDN_API}/api/tools/upload_aliyun_file`,
    method: 'post',
    data
  })
}

// 文件上传阿里云
export function uploadAliyunFile(data) {
  return request({
    url: '/junhengai/yxAgent/v2/upload/file',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}


//  文件上传阿里云状态查询
export function getAliyunFileStatus(params) {
  return request({
    url: '/junhengai/yxAgent/v2/file/status',
    method: 'get',
    params
  })
}


// 保存当前窗口的聊天记录-新版
export function saveChatRecordNew(data) {
  return request({
    url: '/junhengai/window/chat/chatInfo/add',
    method: 'post',
    data
  })
}

// 删除用户提高导肮记录
export function deleteChatNew(userId, category) {
  return request({
    url: `/junhengai/window/chat/chatInfo/delete/by/${userId}/${category}`,
    method: 'delete',
  })
}

// AI解题提示词
export function getAiQuestionPrompt(dictType) {
  return request({
    url: `/junhengai/system/dict/data/typeDictTips/${dictType}`,
    method: 'get'
  })
}
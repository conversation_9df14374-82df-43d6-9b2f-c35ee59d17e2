<template>
  <div class="article-container" ref="containerRef">
    <agent-history-entry
      category="ArticleAI"
      @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">文章润色助手</h1>
      <p class="subtitle">基于AI技术，一键润色优化文章内容，提升文章质量</p>
    </div>

    <div class="form-container">
      <el-form :model="formData" label-width="120px" class="article-form" size="large">
        <el-form-item label="原文输入" required>
          <el-input
            v-model="formData.originalText"
            type="textarea"
            :rows="8"
            placeholder="请输入需要润色的原文内容"
            resize="none"
          />
        </el-form-item>

        <el-form-item label="润色要求">
          <el-input
            v-model="formData.polishRequirement"
            type="textarea"
            :rows="3"
            placeholder="请输入润色要求，例如：增加文采、调整语气、修改结构等"
            resize="none"
          />
        </el-form-item>

        <el-form-item label="字数限制" required>
          <div class="style-buttons">
            <el-button-group>
              <el-button
                v-for="item in wordCountOptions"
                :key="item.value"
                :type="formData.wordCount === item.value ? 'primary' : ''"
                @click="formData.wordCount = item.value"
                round
              >
                {{ item.label }}
              </el-button>
            </el-button-group>
          </div>
        </el-form-item>

        <el-form-item label="文章风格" required>
          <div class="style-buttons">
            <el-button-group>
              <el-button
                v-for="item in styleOptions"
                :key="item.value"
                :type="formData.style === item.value ? 'primary' : ''"
                @click="formData.style = item.value"
                round
              >
                {{ item.label }}
              </el-button>
            </el-button-group>
          </div>
        </el-form-item>

        <div style="display: flex; justify-content: center; width: 100%;"  v-if="!loading">
          <el-button
            :type="loading ? 'warning' : 'primary'"
            size="large"
            v-click-throttle="handlePolish"
            :disabled="!isFormValid"
            round
          >
            {{ loading ? '中止润色' : '开始润色' }}
          </el-button>
        </div>
      </el-form>
    </div>

    <ai-loading :visible="showAiLoading" />

    <div v-if="polishResult" class="result-container">
      <div class="result-content">
        <div class="section-title-container">
          <div class="section-title">润色结果：</div>
          <div class="action-icons">
            <el-icon style="cursor: pointer;" @click="copyResult"><CopyDocument /></el-icon>
            <agent-save-button
              v-if="!loading && polishResult"
              category="ArticleAI"
              :user-question="savePrompt()"
              :model-answer="polishResult"
              :chat-service="chatService"
              :already-saved="alreadySaved"
              @save-success="alreadySaved = true"
            />
          </div>
        </div>
        <div class="comment-text" v-html="renderedMarkdown(polishResult)"></div>
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;" v-if="loading && polishResult">
          <el-button
            type="warning"
            size="large"
            v-click-throttle="handlePolish"
            round
          >中止润色</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick, watch, onMounted,inject } from 'vue'
import { CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import MarkdownIt from "markdown-it"
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { useStore } from 'vuex'
import AgentSaveButton from '@/components/common/AgentSaveButton.vue'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import AiLoading from '@/components/common/AiLoading.vue'

const roleSystem = `
角色设定：你是一名专业的文章润色专家，擅长优化各类文章的表达、结构和内容，使文章更加流畅、生动、有说服力。

## 润色要求
1. 根据用户选择的文章风格调整语言表达：
   - 正式风格：使用规范、严谨的语言，适合学术、公文等场合
   - 生动风格：使用形象、生动的语言，增加修辞手法，适合叙事、描写类文章
   - 简洁风格：使用精炼、直接的语言，减少冗余表达，适合说明、议论类文章
   - 幽默风格：适当加入诙谐、幽默的表达，活跃文章氛围
2. 字数必须严格控制在指定范围内，误差不超过±10%

## 输出要求
- 保持原文的核心内容和主旨不变
- 优化语言表达，提高文章的可读性和吸引力
- 根据用户的润色要求进行针对性调整
- 修正原文中的语法错误、表达不当等问题
- 优化文章结构，使逻辑更加清晰
- 增强文章的连贯性和流畅度
- 字数必须严格控制在指定范围内，误差不超过±10%

## 重要：输出格式
- 直接输出润色后的文章内容，不要添加任何前言、分析、说明或总结
- 不要使用“润色后的文章：”、“以下是润色结果：”等引导语
- 不要解释你做了哪些修改或为什么做这些修改
- 不要在文章后添加字数统计或其他注释
- 只输出润色后的文章正文内容，没有其他任何内容

请根据以上要求，对用户提供的文章进行专业润色，并确保字数严格符合要求。
`

export default {
  name: 'Article',
  components: {
    CopyDocument,
    AgentSaveButton,
    AgentHistoryEntry,
    AiLoading
  },
  setup() {
    const trackingService = inject('trackingService');
    const store = useStore()
    const formData = ref({
      originalText: '',
      polishRequirement: '',
      wordCount: 500,
      style: '正式'
    })

    const polishResult = ref(null)
    const loading = ref(false)
    const reader = ref(null)
    const containerRef = ref(null)
    const chatService = ref(null) // 聊天服务实例
    const alreadySaved = ref(false) // 是否已保存当前对话

    // 字数选项
    const wordCountOptions = [
      { value: 300, label: '300字' },
      { value: 500, label: '500字' },
      { value: 800, label: '800字' },
      { value: 1000, label: '1000字' },
      { value: 1500, label: '1500字' },
      { value: 2000, label: '2000字' }
    ]

    // 文章风格选项
    const styleOptions = [
      { value: '正式', label: '正式' },
      { value: '生动', label: '生动' },
      { value: '简洁', label: '简洁' },
      { value: '幽默', label: '幽默' }
    ]

    const showAiLoading = computed(() => loading.value && !polishResult.value)

    // 表单验证
    const isFormValid = computed(() => {
      return formData.value.originalText.trim() !== '' &&
             formData.value.wordCount &&
             formData.value.style
    })

    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };

    // 监听润色结果变化，触发滚动
    watch(polishResult, () => {
      scrollToBottom();
    });

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('ArticleAI', '文章润色助手会话')
          console.log('文章润色助手重新初始化完成')
        }
      } catch (error) {
        console.error('文章润色助手重新初始化错误:', error)
      }
    }

    // 初始化聊天服务
    onMounted(async () => {
      try {
        chatService.value = new AgentChatService(store)
        await chatService.value.initialize('ArticleAI', '文章润色助手会话')
        console.log('文章润色助手初始化完成')
      } catch (error) {
        console.error('初始化错误:', error)
      }
    })

    // 构建提示词方法
    const buildPrompt = () => {
      let prompt = `请对以下文章进行润色，字数控制在${formData.value.wordCount}字左右，误差不超过±10%，风格为${formData.value.style}风格。请直接输出润色后的文章内容，不要添加任何其他内容。`

      if (formData.value.polishRequirement) {
        prompt += `\n\n润色要求：${formData.value.polishRequirement}`
      }

      prompt += `\n\n原文内容：\n${formData.value.originalText}`

      return prompt
    }

    const savePrompt = () => {
      let prompt = ``

      if (formData.value.originalText) {
        const summary = formData.value.originalText
        prompt += `原文：${summary}，`
      }

      if (formData.value.polishRequirement) {
        prompt += `润色要求：${formData.value.polishRequirement}，`
      }

      if (formData.value.wordCount) {
        prompt += `字数限制：${formData.value.wordCount}字，`
      }

      if (formData.value.style) {
        prompt += `文章风格：${formData.value.style}`
      }

      return prompt
    }

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          // reader.value 现在是 AbortController
          reader.value.abort('用户取消了操作');
          console.log('Abort signal sent.');
        } catch (error) {
          console.error('取消流式输出错误:', error);
        } finally {
          // 立即重置状态，确保按钮恢复
          loading.value = false;
          reader.value = null; // 清理 controller 引用
        }
      }
    }

    // 润色文章
    const handlePolish = async () => {
      if (!isFormValid.value) {
        ElMessage.error('请填写必填项')
        return
      }

      if (loading.value && reader.value) {
        await cancelStream()
        return
      }

      polishResult.value = null
      loading.value = true
      alreadySaved.value = false // 重置保存状态

      // 创建AbortController用于取消请求
      const controller = new AbortController();
      reader.value = controller; // 将 controller 赋值给 reader 用于取消

      try {
        // 构建提示词
        let prompt = buildPrompt()

        // 构建请求体
        // let message = [{ "role": "system", "content": roleSystem }]

        let message = []
        message.push({
          "role": "user",
          "content": prompt
        })

        let requestBody = {
          messages: message,
          stream: true,
          modelType: 'open',
          formType:'article_ai'
        }

        let accumulatedText = '';
        // let baseUrl = import.meta.env.VITE_BASE_URL;
        // 使用fetchEventSource代替fetch
        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`
        fetchEventSource(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal, // 传递 signal 用于取消
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
              loading.value = false; // 出错时停止加载状态
              reader.value = null;
              throw new Error(`HTTP error! status: ${response.status}`);
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              loading.value = false;
              reader.value = null;
              throw new Error(msg.data);
            }

            const data = msg.data;

            try {
              const jsonData = JSON.parse(data);

              if (jsonData.error) {
                loading.value = false;
                reader.value = null;
                return;
              }

              if (jsonData.choices && jsonData.choices[0]) {
                const delta_content = jsonData.choices[0].message.content
                if (delta_content) {
                  accumulatedText += delta_content;
                  polishResult.value = accumulatedText;
                  scrollToBottom();
                }
              }
            } catch (error) {
              console.error('JSON parse error:', error);
            }
          },
          async onclose() {
            console.log('连接关闭');
            // onclose 可能在收到所有数据后触发
            if (loading.value) { // 只有在仍在加载时才设置，避免覆盖[DONE]设置的状态
              loading.value = false;
              reader.value = null;
            }
          },
          onerror(err) {
            console.error('Stream error:', err);
            if (err.name !== 'AbortError') {
            } else {
              console.log('Stream aborted by user.');
              // 用户取消，状态已在 cancelStream 中处理
            }
            // 确保状态被重置，即使在AbortError情况下，以防万一
            loading.value = false;
            reader.value = null;
          }
        });
      } catch (error) {
        console.error('请求错误:', error);
        loading.value = false;
        reader.value = null;
        ElMessage.error('请求出错，请重试');
      }
      trackingService.trackEvent('ArticleAI')

    }

    // 复制润色结果
    const copyResult = () => {
      if (!polishResult.value) return

      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div')
      tempElement.innerHTML = renderedMarkdown(polishResult.value)

      // 获取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText

      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = textContent
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 尝试复制到剪贴板
        document.execCommand('copy')
        ElMessage.success('润色结果已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败')
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }

    // Markdown渲染
    const renderedMarkdown = (markdownContent) => {
      if (!markdownContent) return

      const md = new MarkdownIt()
      const htmlContent = md.render(markdownContent)
      return htmlContent
    }

    return {
      formData,
      wordCountOptions,
      styleOptions,
      polishResult,
      loading,
      isFormValid,
      handlePolish,
      renderedMarkdown,
      containerRef,
      copyResult,
      CopyDocument,
      chatService,
      buildPrompt,
      savePrompt,
      alreadySaved,
      reinitializeChatSession,
      showAiLoading
    }
  }
}
</script>

<style scoped>
.article-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "✨";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 255, 0.08);
}

.article-form {
  width: 100%;
  margin: 0 auto;
}

.style-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-content {
  text-align: left;
  padding: 2rem;
  position: relative;
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.comment-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

.action-icons {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .article-container {
    width: 100%;
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0;
  }

  .comment-text {
    font-size: 1rem;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .form-container {
    margin: 2rem 0;
    padding: 1rem;
  }

  .result-content {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .comment-text {
    padding: 1rem;
  }
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}
</style>
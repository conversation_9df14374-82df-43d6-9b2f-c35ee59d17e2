<template>
  <div class="word-container" ref="containerRef">
    <!-- 单词跟读弹窗 -->
    <follow-reading-dialog
      v-model:visible="followReadingVisible"
      :selected-words="selectedWords"
      :bookId="bookId"
    />
    <!-- 单词听写弹窗 -->
    <dictation-dialog
      v-model:visible="dictationDialogVisible"
      :selected-words="selectedWords"
      :bookId="bookId"
    />
    <!-- 单词记录弹窗 -->
    <word-records-dialog
      v-model:visible="wordRecordsVisible"
      :bookId="bookId"
    />
    <!-- 单词消消乐弹窗 -->
    <word-matching-dialog
      v-model:visible="wordMatchingVisible"
      :selected-words="selectedWords"
      :bookId="bookId"
    />
    <!-- 悬浮球 -->
    <FloatingBall />
    <div class="header">
      <h1 class="title">AI学单词</h1>
      <p class="subtitle">AI学单词学习助手，单词跟读、单词听写等互动学习模式</p>
    </div>
    <div class="form-container">
      <el-form :model="formData" label-width="80px" class="word-form" size="large">
        <el-form-item label="年级">
          <div class="stage-grade-container">
            <el-button-group class="stage-button-group">
              <el-button
                v-for="stage in stageOptions"
                :key="stage.value"
                :type="formData.stage === stage.value ? 'primary' : ''"
                @click="selectOption('stage', stage.value)"
                round
              >{{ stage.label }}</el-button>
            </el-button-group>
            <div class="grade-buttons-container">
              <el-button
                v-for="grade in filteredGrades"
                :key="grade.value"
                :type="formData.grade === grade.value ? 'primary' : ''"
                @click="selectOption('grade', grade.value)"
                round
              >{{ grade.label }}</el-button>
            </div>
          </div>
        </el-form-item>

        <!-- 上下册选项 - 单独一行 -->
        <el-form-item label="上下册">
          <div class="semester-buttons-container">
            <el-button
              v-for="semester in semesterOptions"
              :key="semester.value"
              :type="formData.semester === semester.value ? 'primary' : ''"
              @click="selectOption('semester', semester.value)"
              round
            >{{ semester.label }}</el-button>
          </div>
        </el-form-item>

        <el-form-item label="章节">
          <div class="tag-buttons-container">
            <el-button
              v-for="chapter in filteredChapters"
              :key="chapter.id"
              :type="formData.chapter === chapter.id ? 'primary' : ''"
              @click="selectOption('chapter', chapter.id)"
              round
            >{{ chapter.name }}</el-button>
          </div>
        </el-form-item>

        <!-- 单元选项 - 仅初中显示 -->
        <el-form-item label="单元" v-if="formData.stage === '3' && formData.chapter">
          <div class="tag-buttons-container">
            <el-button
              v-for="unit in filteredUnits"
              :key="unit.id"
              :type="formData.unit === unit.id ? 'primary' : ''"
              @click="selectOption('unit', unit.id)"
              round
            >{{ unit.name }}</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div class="result-container">

      <!-- 章节导航 -->
      <div class="chapter-navigation-container">
        <!-- 小学：章节导航 -->
        <div class="chapter-navigation" v-if="formData.stage === '2' && currentChapter">
          <el-button @click="prevChapter" :disabled="!hasPrevChapter" class="nav-button">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <div class="chapter-title">{{ currentChapter.name }}</div>
          <el-button @click="nextChapter" :disabled="!hasNextChapter" class="nav-button">
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <!-- 初中：单元导航 -->
        <div class="chapter-navigation" v-if="formData.stage === '3' && currentUnit">
          <el-button @click="prevUnit" :disabled="!hasPrevUnit" class="nav-button">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <div class="chapter-title">{{ currentUnit.periodName }}</div>
          <el-button @click="nextUnit" :disabled="!hasNextUnit" class="nav-button">
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 单词选择 -->
      <div class="selection-mode-controls">
        <div class="selection-mode-left">
          <span class="mode-label">章节单词选择：</span>
          <div class="selection-checkboxes">
            <el-checkbox v-model="selectAllMode" @change="handleSelectAllModeChange" label="全选单词"></el-checkbox>
          </div>
          <div class="selected-word-count">
            已选 <span class="count-number">{{ totalSelectedWords }}</span> 个单词
          </div>
        </div>

        <div class="selection-mode-right">
          <el-button @click="speakSelected" class="action-button follow-reading-btn" :disabled="totalSelectedWords === 0">
            <el-icon class="action-icon"><Microphone /></el-icon>
            <span>单词跟读</span>
          </el-button>
          <el-button @click="dictationMode" class="action-button dictation-btn green-theme" :disabled="totalSelectedWords === 0">
            <el-icon class="action-icon"><Headset /></el-icon>
            <span>单词听写</span>
          </el-button>
          <el-button @click="startWordMatching" class="action-button matching-btn ocean-theme" :disabled="totalSelectedWords === 0">
            <el-icon class="action-icon"><Connection /></el-icon>
            <span>单词消消乐</span>
          </el-button>
          <el-button @click="showWordRecords" class="records-button">
            <el-icon class="records-icon"><Document /></el-icon>
            <span>我的单词记录</span>
          </el-button>
        </div>
      </div>

      <div v-if="wordListLoading" class="loading-container word-list-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载单词</span>
      </div>
      <div v-else-if="wordList.length === 0" class="empty-result">
        <el-empty description="章节无单词数据" image-size="200"></el-empty>
      </div>

      <div v-else class="word-card-container">
        <div v-for="word in wordList" :key="word.id" class="word-card" :class="{ 'selected': word.selected }">
          <div class="word-checkbox" v-if="selectAllMode || selectPartialMode">
            <el-checkbox v-model="word.selected" @change="handleWordSelection"></el-checkbox>
          </div>
          <div class="word-content" @click="(selectAllMode || selectPartialMode) ? toggleWordSelection(word) : null">
            <div class="word-english">{{ word.english }}</div>
            <!-- <div class="word-phonetic" v-if="word.ipa">{{ word.ipa }}</div> -->
            <div class="word-chinese">{{ word.chinese }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, ArrowRight, Microphone, Headset, Loading, Delete, Close, Document, Connection } from '@element-plus/icons-vue'
import FollowReadingDialog from '@/components/word/FollowReadingDialog.vue'
import DictationDialog from '@/components/word/DictationDialog.vue'
import WordRecordsDialog from '@/components/word/WordRecordsDialog.vue'
import WordMatchingDialog from '@/components/word/WordMatchingDialog.vue'
import { getWordChapters, getWordsByChapter, getUserLastOperation } from '@/api/word'
import { useStore } from 'vuex'
import FloatingBall from '../components/common/FloatingBall.vue'

export default {
  name: 'Word',
  components: {
    ArrowLeft,
    ArrowRight,
    Microphone,
    Headset,
    Loading,
    Delete,
    Close,
    Document,
    Connection,
    FollowReadingDialog,
    DictationDialog,
    WordRecordsDialog,
    WordMatchingDialog,
    FloatingBall
  },
  setup() {
    const trackingService = inject('trackingService');

    // 获取store
    const store = useStore()
    const userId = computed(() => store.getters.userId)

    // 表单数据
    const formData = ref({
      stage: '2',  // 2: 小学, 3: 初中, 4: 高中
      grade: '',
      semester: '',
      chapter: '',
      unit: ''  // 单元id，仅初中使用
    })

    // 单词记录弹窗可见性
    const wordRecordsVisible = ref(false)
    
    // 单词消消乐弹窗可见性
    const wordMatchingVisible = ref(false)

    const containerRef = ref(null)

    // 学段选项
    const stageOptions = [
      { value: '2', label: '小学' },
      { value: '3', label: '初中' },
      // { value: '4', label: '高中' }
    ]

    // 年级选项映射
    const gradeMap = {
      '2': [  // 小学
        { value: '5', label: '三年级' },
        { value: '6', label: '四年级' },
        { value: '7', label: '五年级' },
        { value: '8', label: '六年级' }
      ],
      '3': [  // 初中
        { value: '9', label: '初一' },
        // { value: '10', label: '初二' },
        // { value: '11', label: '初三' }
      ],
      '4': [  // 高中
        { value: '12', label: '高一' },
        { value: '13', label: '高二' },
        { value: '14', label: '高三' }
      ]
    }

    // 学期选项
    const semesterOptions = [
      { value: '1', label: '上册' },
      { value: '2', label: '下册' }
    ]

    // 根据当前选择的学段筛选年级
    const filteredGrades = computed(() => {
      return gradeMap[formData.value.stage] || []
    })

    // 章节数据
    const chaptersData = ref([])
    const chaptersLoading = ref(false)

    // 单元数据（仅初中使用）
    const unitsData = ref([])
    const unitsLoading = ref(false)

    // 课本数据
    const bookId = ref('')
    const bookName = ref('')

    // 获取章节数据
    const fetchChapters = async () => {
      if (!formData.value.stage || !formData.value.grade || !formData.value.semester) {
        chaptersData.value = []
        return
      }

      chaptersLoading.value = true
      try {
        // 构建请求参数
        const params = {
          phaseId: formData.value.stage,
          gradeId: formData.value.grade,
          subjectId: '2', // 学科固定为英语
          semester: formData.value.semester
        }

        // 调用API获取章节数据
        const response = await getWordChapters(params)
        const chatper_data = response.data
        bookId.value = chatper_data.id
        bookName.value = chatper_data.bookName
        if (chatper_data && chatper_data.children && Array.isArray(chatper_data.children)) {
          chaptersData.value = chatper_data.children.map(chapter => ({
            id: chapter.id,
            name: chapter.periodName,
            stage: formData.value.stage,
            grade: formData.value.grade,
            semester: formData.value.semester,
            children: chapter.children || [] // 保存单元数据（初中使用）
          }))
        } else {
          chaptersData.value = []
        }
      } catch (error) {
        console.error('获取章节数据错误:', error)
        chaptersData.value = []
      } finally {
        chaptersLoading.value = false
      }
    }

    // 章节列表
    const filteredChapters = computed(() => {
      return chaptersData.value
    })

    // 单元列表（仅初中使用）
    const filteredUnits = computed(() => {
      if (formData.value.stage !== '3' || !formData.value.chapter) {
        return []
      }
      const currentChapter = chaptersData.value.find(chapter => chapter.id === formData.value.chapter)

      if (currentChapter && currentChapter.children) {
        return currentChapter.children.map(unit => ({
          id: unit.id,
          name: unit.periodName,
          bookId: unit.bookId,
          parentId: unit.parentId
        }))
      }
      return []
    })

    // 当前选中的章节
    const currentChapter = computed(() => {
      if (!formData.value.chapter) return null
      return chaptersData.value.find(chapter => chapter.id === formData.value.chapter)
    })

    // 当前选中的单元（仅初中使用）
    const currentUnit = computed(() => {
      if (formData.value.stage !== '3' || !formData.value.unit || !currentChapter.value) return null
      if (!currentChapter.value.children) return null
      return currentChapter.value.children.find(unit => unit.id === formData.value.unit)
    })

    // 是否有上一章节
    const hasPrevChapter = computed(() => {
      if (!currentChapter.value) return false
      const currentIndex = filteredChapters.value.findIndex(chapter => chapter.id === currentChapter.value.id)
      return currentIndex > 0
    })

    // 是否有下一章节
    const hasNextChapter = computed(() => {
      if (!currentChapter.value) return false
      const currentIndex = filteredChapters.value.findIndex(chapter => chapter.id === currentChapter.value.id)
      return currentIndex < filteredChapters.value.length - 1
    })

    // 是否有上一单元（仅初中使用）
    const hasPrevUnit = computed(() => {
      if (formData.value.stage !== '3' || !currentUnit.value || !currentChapter.value) return false
      if (!currentChapter.value.children) return false
      const currentIndex = currentChapter.value.children.findIndex(unit => unit.id === currentUnit.value.id)
      return currentIndex > 0
    })

    // 是否有下一单元（仅初中使用）
    const hasNextUnit = computed(() => {
      if (formData.value.stage !== '3' || !currentUnit.value || !currentChapter.value) return false
      if (!currentChapter.value.children) return false
      const currentIndex = currentChapter.value.children.findIndex(unit => unit.id === currentUnit.value.id)
      return currentIndex < currentChapter.value.children.length - 1
    })

    // 切换到上一章节
    const prevChapter = () => {
      if (!hasPrevChapter.value) return

      const currentIndex = filteredChapters.value.findIndex(chapter => chapter.id === currentChapter.value.id)
      const prevChapterId = filteredChapters.value[currentIndex - 1].id

      if (formData.value.chapter === prevChapterId) {
        console.log('已经是当前章节，不做处理')
        return
      }

      // 先设置加载状态为true，确保显示加载效果
      wordListLoading.value = true

      // 延迟一下再切换章节，确保加载效果能够显示
      setTimeout(() => {
        formData.value.chapter = prevChapterId
        loadWordList()
      }, 0)
    }

    // 切换到下一章节
    const nextChapter = () => {
      if (!hasNextChapter.value) return

      const currentIndex = filteredChapters.value.findIndex(chapter => chapter.id === currentChapter.value.id)
      const nextChapterId = filteredChapters.value[currentIndex + 1].id

      if (formData.value.chapter === nextChapterId) {
        console.log('已经是当前章节，不做处理')
        return
      }

      // 先设置加载状态为true，确保显示加载效果
      wordListLoading.value = true

      // 延迟一下再切换章节，确保加载效果能够显示
      setTimeout(() => {
        formData.value.chapter = nextChapterId
        loadWordList()
      }, 0)
    }

    // 切换到上一单元（仅初中使用）
    const prevUnit = () => {
      if (!hasPrevUnit.value || !currentChapter.value) return

      const currentIndex = currentChapter.value.children.findIndex(unit => unit.id === currentUnit.value.id)
      const prevUnitId = currentChapter.value.children[currentIndex - 1].id

      if (formData.value.unit === prevUnitId) {
        console.log('已经是当前单元，不做处理')
        return
      }

      // 先设置加载状态为true，确保显示加载效果
      wordListLoading.value = true

      // 延迟一下再切换单元，确保加载效果能够显示
      setTimeout(() => {
        formData.value.unit = prevUnitId
        loadWordList()
      }, 0)
    }

    // 切换到下一单元（仅初中使用）
    const nextUnit = () => {
      if (!hasNextUnit.value || !currentChapter.value) return

      const currentIndex = currentChapter.value.children.findIndex(unit => unit.id === currentUnit.value.id)
      const nextUnitId = currentChapter.value.children[currentIndex + 1].id

      if (formData.value.unit === nextUnitId) {
        console.log('已经是当前单元，不做处理')
        return
      }

      // 先设置加载状态为true，确保显示加载效果
      wordListLoading.value = true

      // 延迟一下再切换单元，确保加载效果能够显示
      setTimeout(() => {
        formData.value.unit = nextUnitId
        loadWordList()
      }, 0)
    }

    // 单词列表
    const wordList = ref([])
    const wordListLoading = ref(false)

    // 选择模式状态
    const selectAllMode = ref(true)  // 默认勾中全选单词
    const selectPartialMode = ref(false)

    // 存储所有章节的已选中单词
    const allSelectedWords = ref([])

    // 已选中的单词（包括所有章节）
    const selectedWords = computed(() => {
      // 合并当前章节的选中单词和其他章节的选中单词
      return allSelectedWords.value
    })

    // 所有章节中已选中的单词总数
    const totalSelectedWords = computed(() => {
      return allSelectedWords.value.length
    })

    // 按章节统计已选中的单词数量
    const selectedWordsByChapter = ref({})

    // 根据章节ID获取章节名称
    const getChapterName = (chapterId) => {
      const chapter = chaptersData.value.find(c => c.id === chapterId)
      return chapter ? chapter.name : chapterId
    }

    // 更新选中单词的统计信息
    const updateSelectedWordsStats = () => {
      // 小学：需要选择章节，初中：需要选择章节和单元
      if (formData.value.stage === '2' && !formData.value.chapter) return
      if (formData.value.stage === '3' && (!formData.value.chapter || !formData.value.unit)) return

      // 获取当前学习单位的ID（小学用章节ID，初中用单元ID）
      const currentPeriodId = formData.value.stage === '3' ? formData.value.unit : formData.value.chapter

      // 获取当前学习单位选中的单词
      const currentPeriodSelectedWords = wordList.value.filter(word => word.selected)
      const count = currentPeriodSelectedWords.length

      // 更新学习单位单词计数
      if (count > 0) {
        selectedWordsByChapter.value[currentPeriodId] = count
      } else {
        delete selectedWordsByChapter.value[currentPeriodId]
      }

      // 强制更新响应式对象
      selectedWordsByChapter.value = { ...selectedWordsByChapter.value }

      // 更新所有选中单词列表
      // 1. 移除当前学习单位的旧单词
      allSelectedWords.value = allSelectedWords.value.filter(word => word.periodId !== currentPeriodId)

      // 2. 添加当前学习单位的新选中单词
      if (currentPeriodSelectedWords.length > 0) {
        allSelectedWords.value = [...allSelectedWords.value, ...currentPeriodSelectedWords]
      }

      // 3. 当用户手动操作复选框时，检测并更新选择模式
      // 只有在全选模式下，当不是所有单词都被选中时，才自动切换到部分选择模式
      if (wordList.value.length > 0 && selectAllMode.value) {
        const allSelected = wordList.value.every(word => word.selected)

        if (!allSelected) {
          selectAllMode.value = false
          selectPartialMode.value = true
        }
      }
    }

    // 处理全选模式变化
    const handleSelectAllModeChange = (checked) => {
      // 如果勾选了全选模式，取消勾选部分选择模式
      if (checked) {
        selectPartialMode.value = false
        // 选中所有单词
        wordList.value.forEach(word => {
          word.selected = true
        })
      } else {
        selectPartialMode.value = true
        // 取消选中所有单词
        wordList.value.forEach(word => {
          word.selected = false
        })
      }

      updateSelectedWordsStats()
    }

    // 处理部分选择模式变化
    const handleSelectPartialModeChange = (checked) => {
      // 如果勾选了部分选择模式，取消勾选全选模式
      if (checked) {
        selectAllMode.value = false
        // 清空所有单词的选中状态
        wordList.value.forEach(word => {
          word.selected = false
        })
      } else {
        // 取消选中所有单词
        wordList.value.forEach(word => {
          word.selected = false
        })
      }

      updateSelectedWordsStats()
    }

    // 处理单个单词选择状态变化
    const handleWordSelection = () => {
      // 更新选中状态
      updateSelectedWordsStats()

      // 注意：我们不再在这里自动切换选择模式
      // 让updateSelectedWordsStats函数处理必要的模式切换
      // 这样可以避免选择模式被自动切换，导致无法勾选"选择部分单词"
    }

    // 点击单词卡片切换选择状态
    const toggleWordSelection = (word) => {
      if (!selectAllMode.value && !selectPartialMode.value) return

      word.selected = !word.selected
      handleWordSelection()
    }

    // 检测当前学习单位是否所有单词都已选中，并相应更新选择模式
    const checkAndUpdateSelectionMode = () => {
      if (wordList.value.length === 0) return

      // 获取当前学习单位的ID（小学用章节ID，初中用单元ID）
      const currentPeriodId = formData.value.stage === '3' ? formData.value.unit : formData.value.chapter
      if (!currentPeriodId) return

      // 获取当前学习单位在allSelectedWords中已选中的单词ID列表
      const selectedWordIds = allSelectedWords.value
        .filter(word => word.periodId === currentPeriodId)
        .map(word => word.id)

      // 检查当前章节的所有单词是否都在选中列表中
      const allWordIds = wordList.value.map(word => word.id)
      const allWordsSelected = allWordIds.length > 0 && allWordIds.every(id => selectedWordIds.includes(id))

      if (allWordsSelected) {
        // 如果所有单词都已选中，设置为全选模式
        selectAllMode.value = true
        selectPartialMode.value = false
      } else if (selectedWordIds.length > 0) {
        // 如果有部分单词选中，设置为部分选择模式
        selectAllMode.value = false
        selectPartialMode.value = true
      } else {
        // 如果没有单词选中，清空选择模式
        selectAllMode.value = false
        selectPartialMode.value = false
      }
    }

    // 加载单词列表
    const loadWordList = async (isInitialLoad = false) => {
      // 小学：需要选择章节
      // 初中：需要选择章节和单元
      if (formData.value.stage === '2' && !formData.value.chapter) {
        wordList.value = []
        return
      }
      if (formData.value.stage === '3' && (!formData.value.chapter || !formData.value.unit)) {
        wordList.value = []
        return
      }

      wordListLoading.value = true
      try {
        // 构建请求参数
        // 小学：传章节ID，初中：传单元ID
        const periodId = formData.value.stage === '3' ? formData.value.unit : formData.value.chapter
        const params = {
          periodId: periodId
        }

        // 调用API获取单词数据
        const response = await getWordsByChapter(params)

        // 处理返回的数据
        if (response && response.data && Array.isArray(response.data)) {
          // 获取当前学习单位的ID（小学用章节ID，初中用单元ID）
          const currentPeriodId = formData.value.stage === '3' ? formData.value.unit : formData.value.chapter

          // 获取当前学习单位在allSelectedWords中已选中的单词ID列表
          const selectedWordIds = allSelectedWords.value
            .filter(word => word.periodId === currentPeriodId)
            .map(word => word.id)

          // 检查当前学习单位是否有已选中的单词
          const hasSelectedWords = selectedWordIds.length > 0

          // 如果是初次加载且当前学习单位没有已选中的单词，则自动全选该学习单位的单词
          const shouldAutoSelectAll = isInitialLoad && !hasSelectedWords

          // 将API返回的数据格式转换为组件需要的格式
          wordList.value = response.data.map(word => {
            // 检查该单词是否在已选中列表中，或者是否应该自动全选
            const isSelected = (hasSelectedWords && selectedWordIds.includes(word.id)) || shouldAutoSelectAll

            return {
              id: word.id,
              english: word.enWord,
              chinese: word.zhCn,
              ipa: word.ipa,
              periodId: currentPeriodId, // 使用当前学习单位的ID
              bookId: word.bookId,
              enVoicePath: word.enVoiceUrl,
              chVoicePath: word.chVoicePath,
              selected: isSelected // 根据是否在已选中列表中或是否应该自动全选来设置选中状态
            }
          })

          // 如果是自动全选，需要先更新选中单词统计，再检测选择模式
          if (shouldAutoSelectAll) {
            updateSelectedWordsStats()
          }

          // 检测并更新选择模式
          checkAndUpdateSelectionMode()

        } else {
          wordList.value = []
        }

        // 更新选中单词统计
        updateSelectedWordsStats()
      } catch (error) {
        console.error('获取单词数据错误:', error)
        wordList.value = []
      } finally {
        wordListLoading.value = false
      }
    }

    // 选择筛选选项
    const selectOption = async (field, value) => {
      // 保存旧值，用于比较是否发生变化
      const oldValue = formData.value[field]

      // 更新表单数据
      formData.value[field] = value

      // 如果学段、年级或学期发生变化，则清空已选单词
      if ((field === 'stage' || field === 'grade' || field === 'semester') && oldValue !== value) {
        // 清空已选单词
        clearSelectedWords()
      }

      // 当切换学段时自动选择第一个年级和上册
      if (field === 'stage') {
        // 获取当前学段的年级列表
        const grades = gradeMap[value] || []

        if (grades.length > 0) {
          // 自动选择第一个年级
          formData.value.grade = grades[0].value
          // 默认选择上册
          formData.value.semester = '1'
          formData.value.chapter = ''
          formData.value.unit = ''

          // 获取章节数据
          await fetchChapters()
          if (chaptersData.value.length > 0) {
            formData.value.chapter = chaptersData.value[0].id
            // 如果是初中，还需要选择第一个单元
            if (formData.value.stage === '3') {
              const firstChapter = chaptersData.value[0]
              if (firstChapter.children && firstChapter.children.length > 0) {
                formData.value.unit = firstChapter.children[0].id
              }
            }
            loadWordList()
          }
        } else {
          // 如果没有年级选项，则清空相关字段
          formData.value.grade = ''
          formData.value.semester = ''
          formData.value.chapter = ''
        }
      }

      // 当切换年级时自动选择上册
      if (field === 'grade') {
        formData.value.semester = '1' // 默认选择上册
        formData.value.chapter = ''
        formData.value.unit = ''

        // 如果学段和年级都已选择，则获取章节数据
        if (formData.value.stage && formData.value.grade) {
          await fetchChapters()
          if (chaptersData.value.length > 0) {
            formData.value.chapter = chaptersData.value[0].id
            // 如果是初中，还需要选择第一个单元
            if (formData.value.stage === '3') {
              const firstChapter = chaptersData.value[0]
              if (firstChapter.children && firstChapter.children.length > 0) {
                formData.value.unit = firstChapter.children[0].id
              }
            }
            loadWordList()
          }
        }
      }

      // 当切换学期时重置章节并获取章节数据
      if (field === 'semester') {
        formData.value.chapter = ''
        formData.value.unit = ''
        // 如果学段、年级和学期都已选择，则获取章节数据
        if (formData.value.stage && formData.value.grade && formData.value.semester) {
          await fetchChapters()
          if (chaptersData.value.length > 0) {
            formData.value.chapter = chaptersData.value[0].id
            // 如果是初中，还需要选择第一个单元
            if (formData.value.stage === '3') {
              const firstChapter = chaptersData.value[0]
              if (firstChapter.children && firstChapter.children.length > 0) {
                formData.value.unit = firstChapter.children[0].id
              }
            }
            loadWordList()
          }
        }
      }

      // 当选择章节时
      if (field === 'chapter') {
        if (oldValue === value) {
          console.log('点击了当前已选中的章节，不做处理')
          return
        }

        formData.value.unit = '' // 重置单元选择

        // 如果是初中，自动选择第一个单元
        if (formData.value.stage === '3') {
          const selectedChapter = chaptersData.value.find(chapter => chapter.id === value)
          if (selectedChapter && selectedChapter.children && selectedChapter.children.length > 0) {
            formData.value.unit = selectedChapter.children[0].id
          }
        }

        // 先设置加载状态为true，确保显示加载效果
        wordListLoading.value = true

        // 延迟一下再加载单词列表，确保加载效果能够显示
        setTimeout(() => {
          loadWordList()
        }, 0)
      }

      // 当选择单元时加载单词列表（仅初中）
      if (field === 'unit') {
        if (oldValue === value) {
          console.log('点击了当前已选中的单元，不做处理')
          return
        }

        // 先设置加载状态为true，确保显示加载效果
        wordListLoading.value = true

        // 延迟一下再加载单词列表，确保加载效果能够显示
        setTimeout(() => {
          loadWordList()
        }, 0)
      }
    }

    // 跟读对话框可见性
    const followReadingVisible = ref(false)

    // 听写对话框可见性
    const dictationDialogVisible = ref(false)

    // 预加载音频文件
    const preloadAudio = async (audioUrls) => {
      const preloadPromises = audioUrls.map(url => {
        return new Promise((resolve) => {
          if (!url) {
            resolve()
            return
          }

          const audio = new Audio()
          
          // 设置超时，避免某个音频加载时间过长
          const timeout = setTimeout(() => {
            console.warn('音频预加载超时:', url)
            resolve()
          }, 5000) // 5秒超时

          audio.oncanplaythrough = () => {
            clearTimeout(timeout)
            resolve()
          }

          audio.onerror = () => {
            clearTimeout(timeout)
            console.error('音频预加载失败:', url)
            resolve()
          }

          audio.onabort = () => {
            clearTimeout(timeout)
            console.warn('音频预加载中断:', url)
            resolve()
          }

          try {
            audio.src = url
            audio.load()
          } catch (error) {
            clearTimeout(timeout)
            console.error('音频预加载异常:', url, error)
            resolve()
          }
        })
      })

      // 等待所有音频预加载完成（或超时）
      try {
        await Promise.all(preloadPromises)
        console.log('所有音频预加载完成')
      } catch (error) {
        console.error('音频预加载过程中出现错误:', error)
      }
    }

    // 打开跟读对话框
    const speakSelected = () => {
      if (selectedWords.value.length === 0) {
        ElMessage.warning('请先选择单词')
        return
      }

      // 打开跟读对话框
      followReadingVisible.value = true
      trackingService.trackEvent('WordAI')

      // 异步预加载音频文件，不阻塞界面
      const audioUrls = selectedWords.value
        .map(word => word.enVoicePath)
        .filter(url => url) // 过滤掉空的URL

      if (audioUrls.length > 0) {
        console.log('开始后台预加载音频，数量:', audioUrls.length)
        preloadAudio(audioUrls).then(() => {
          console.log('后台音频预加载完成')
        }).catch(error => {
          console.error('后台音频预加载失败:', error)
        })
      }
    }

    // 听写模式
    const dictationMode = () => {
      if (selectedWords.value.length === 0) {
        ElMessage.warning('请先选择单词')
        return
      }

      // 打开听写对话框
      dictationDialogVisible.value = true
      trackingService.trackEvent('WordAI')

    }

    // 显示单词记录
    const showWordRecords = () => {
      wordRecordsVisible.value = true
      trackingService.trackEvent('WordAI')

    }

    // 清空所有已选单词
    const clearSelectedWords = () => {
      // 将所有单词的选中状态设为false
      wordList.value.forEach(word => {
        word.selected = false
      })

      // 清空章节选中统计
      selectedWordsByChapter.value = {}

      // 清空所有选中单词列表
      allSelectedWords.value = []

      // 重置选择模式
      selectAllMode.value = false
      selectPartialMode.value = false
    }

    // 清空特定章节的已选单词
    const clearChapterWords = (chapterId) => {
      // 如果当前正在查看这个章节，则将所有单词的选中状态设为false
      if (currentChapter.value && currentChapter.value.id === chapterId) {
        wordList.value.forEach(word => {
          word.selected = false
        })

        // 重置选择模式
        selectAllMode.value = false
        selectPartialMode.value = false
      }

      // 从章节选中统计中移除该章节
      delete selectedWordsByChapter.value[chapterId]

      // 强制更新响应式对象
      selectedWordsByChapter.value = { ...selectedWordsByChapter.value }

      // 从所有选中单词列表中移除该章节的单词
      allSelectedWords.value = allSelectedWords.value.filter(word => word.periodId !== chapterId)
    }

    // 获取用户上次操作的学段、年级、学期、章节
    const fetchUserLastOperation = async () => {
      if (!userId.value) return false

      try {
        const params = {
          uid: userId.value
        }
        let response = await getUserLastOperation(params)

        if (response && response.data) {
          const { phaseId, gradeId, semester, periodId } = response.data

          // 设置学段、年级、学期
          if (phaseId) formData.value.stage = phaseId

          // 确保年级选项已加载
          if (gradeId && gradeMap[formData.value.stage]?.some(grade => grade.value === gradeId)) {
            formData.value.grade = gradeId
          } else if (filteredGrades.value.length > 0) {
            formData.value.grade = filteredGrades.value[0].value
          }

          // 设置学期
          if (semester && (semester === '1' || semester === '2')) {
            formData.value.semester = semester
          } else {
            const currentMonth = new Date().getMonth() // 0-11
            // 9月到次年2月为上半学期
            // 9月(8), 10月(9), 11月(10), 12月(11), 1月(0), 2月(1)
            if ([8, 9, 10, 11, 0, 1].includes(currentMonth)) {
              formData.value.semester = '1'  // 上册
            } else {
              formData.value.semester = '2'  // 下册
            }
          }

          // 获取章节数据
          await fetchChapters()

          // 根据学段处理periodId
          if (periodId) {
            if (formData.value.stage === '2') {
              // 小学：periodId是章节ID
              if (chaptersData.value.some(chapter => chapter.id === periodId)) {
                formData.value.chapter = periodId
                loadWordList(true) // 标记为初次加载，自动全选该章节的单词
                return true
              }
            } else if (formData.value.stage === '3') {
              // 初中：periodId是单元ID，需要找到对应的章节和单元
              for (const chapter of chaptersData.value) {
                if (chapter.children) {
                  const unit = chapter.children.find(u => u.id === periodId)
                  if (unit) {
                    formData.value.chapter = chapter.id
                    formData.value.unit = unit.id
                    loadWordList(true) // 标记为初次加载，自动全选该单元的单词
                    return true
                  }
                }
              }
            }
          }
        }
        return false
      } catch (error) {
        console.error('获取用户上次操作数据失败:', error)
        return false
      }
    }

    // 打开单词消消乐弹窗
    const startWordMatching = () => {
      if (selectedWords.value.length === 0) {
        ElMessage.warning('请先选择单词')
        return
      }

      // 打开单词消消乐弹窗
      wordMatchingVisible.value = true
      trackingService.trackEvent('WordAI')
    }

    // 组件挂载时初始化
    onMounted(async () => {
      // 获取用户上次操作的数据
      const hasLastOperation = await fetchUserLastOperation()

      // 如果没有上次操作数据或获取失败，则使用默认值
      if (!hasLastOperation) {
        // 设置默认选项
        if (formData.value.stage && filteredGrades.value.length > 0) {
          if (!formData.value.grade) {
            formData.value.grade = filteredGrades.value[0].value
          }
          if (!formData.value.semester) {
            const currentMonth = new Date().getMonth() // 0-11
            // 9月到次年2月为上半学期
            // 9月(8), 10月(9), 11月(10), 12月(11), 1月(0), 2月(1)
            if ([8, 9, 10, 11, 0, 1].includes(currentMonth)) {
              formData.value.semester = '1'  // 上册
            } else {
              formData.value.semester = '2'  // 下册
            }
          }

          // 获取章节数据
         await fetchChapters()

          if (chaptersData.value.length > 0) {
            formData.value.chapter = chaptersData.value[0].id
            // 如果是初中，还需要选择第一个单元
            if (formData.value.stage === '3') {
              const firstChapter = chaptersData.value[0]
              if (firstChapter.children && firstChapter.children.length > 0) {
                formData.value.unit = firstChapter.children[0].id
              }
            }
            loadWordList(true) // 标记为初次加载，自动全选该学习单位的单词
          }
        }
      }
    })

    return {
      formData,
      containerRef,
      stageOptions,
      semesterOptions,
      filteredGrades,
      chaptersData,
      chaptersLoading,
      unitsData,
      unitsLoading,
      filteredUnits,
      bookId,
      bookName,
      filteredChapters,
      currentChapter,
      currentUnit,
      hasPrevChapter,
      hasNextChapter,
      hasPrevUnit,
      hasNextUnit,
      prevChapter,
      nextChapter,
      prevUnit,
      nextUnit,
      wordList,
      wordListLoading,
      selectedWords,
      allSelectedWords,
      totalSelectedWords,
      selectedWordsByChapter,
      getChapterName,
      selectAllMode,
      selectPartialMode,
      handleSelectAllModeChange,
      handleSelectPartialModeChange,
      handleWordSelection,
      toggleWordSelection,
      selectOption,
      speakSelected,
      dictationMode,
      showWordRecords,
      clearSelectedWords,
      clearChapterWords,
      checkAndUpdateSelectionMode,
      preloadAudio,
      followReadingVisible,
      dictationDialogVisible,
      wordRecordsVisible,
      wordMatchingVisible,
      startWordMatching
    }
  }
}
</script>

<style scoped>
.word-container {
  width: 80vw;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: #333;
  position: relative;
}

.subtitle {
  font-size: 1.1rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
}

.form-container {
  margin-bottom: 2rem;
  background-color: #fff;
  border-radius: 0.8rem;
  padding: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.tag-buttons-container,
.grade-buttons-container,
.semester-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}
.tag-buttons-container .el-button {
  margin-left: 0 !important;
}

.stage-grade-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.stage-button-group {
  margin-right: 0.5rem;
  display: inline-flex;
}

.grade-buttons-container {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.el-button.is-round.el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.el-button.is-round:not(.el-button--primary) {
  background-color: #fff;
  border-color: #dcdfe6;
  color: #606266;
}

.result-container {
  margin-top: 0 !important;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
}

.selected-words-summary {
  background-color: #f5f7fa;
  border-radius: 0.8rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e4e7ed;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #ebeef5;
}

.summary-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.summary-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.clear-button {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.9rem;
  padding: 0.3rem 0.8rem;
  height: auto;
  border-radius: 0.5rem;
}

.clear-icon {
  font-size: 1rem;
}

.chapter-summary-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.chapter-summary-item {
  background-color: #ecf5ff;
  border-radius: 0.5rem;
  padding: 0.5rem 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid #d9ecff;
  position: relative;
}

.chapter-name {
  font-weight: 500;
  color: #409eff;
}

.word-count {
  color: #606266;
  font-size: 0.9rem;
}

.clear-chapter-icon {
  margin-left: 0.3rem;
  color: #909399;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-chapter-icon:hover {
  color: #f56c6c;
  transform: scale(1.1);
}

.selection-mode-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem 0;
  margin-bottom: 0.5rem;
}

.selection-mode-left {
  display: flex;
  align-items: center;
}

.selected-word-count {
  display: flex;
  align-items: center;
  margin-left: 1.5rem;
  font-size: 1.2rem;
  color: #333;
}


.count-number {
  font-weight: 700;
  color: #409eff;
  font-size: 1.3rem;
  margin: 0 0.3rem;
}

.selection-checkboxes {
  display: flex;
  gap: 1.5rem;
}

/* 增大选择模式复选框的大小和字体 */
.selection-checkboxes :deep(.el-checkbox) {
  height: auto;
  margin-right: 0;
}

.selection-checkboxes :deep(.el-checkbox__input) {
  transform: scale(1.3);
  margin-right: 0.5rem;
}

.selection-checkboxes :deep(.el-checkbox__label) {
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1.5;
}

.selection-mode-right {
  display: flex;
  align-items: center;
  gap: 0;
}

.mode-label {
  margin-right: 1.2rem;
  font-weight: 600;
  color: #606266;
  font-size: 1.2rem;
}

.chapter-navigation-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chapter-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background-color: #f0f7ff;
  border-radius: 1rem;
  border: 1px solid #d9ecff;
  flex: 1;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.action-button, .records-button {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-weight: 600;
  font-size: 1.2rem;
  padding: 0.9rem 1.5rem;
  height: auto;
  border-radius: 0.8rem;
  transition: all 0.3s;
  min-width: 140px;
}

.follow-reading-btn {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.follow-reading-btn:hover {
  background-color: #3a8ee6;
  border-color: #3a8ee6;
  color: white;
  transform: translateY(-2px);
}

.follow-reading-btn:disabled {
  background-color: #a0cfff !important;
  border-color: #a0cfff !important;
  color: white !important;
  cursor: not-allowed;
}

/* 听写按钮绿色主题 */
.green-theme.dictation-btn {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: white !important;
}

.green-theme.dictation-btn:hover {
  background-color: #529b2e !important;
  border-color: #529b2e !important;
  transform: translateY(-2px);
}

.green-theme.dictation-btn:disabled {
  background-color: #b3e19d !important;
  border-color: #b3e19d !important;
  color: white !important;
  cursor: not-allowed;
}

/* 消消乐按钮海洋主题 */
.ocean-theme.matching-btn {
  background: linear-gradient(135deg, #1e88e5, #26c6da) !important;
  border-color: #1e88e5 !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(30, 136, 229, 0.3);
}

.ocean-theme.matching-btn:hover {
  background: linear-gradient(135deg, #1976d2, #00acc1) !important;
  border-color: #1976d2 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 136, 229, 0.4);
}

.ocean-theme.matching-btn:disabled {
  background: linear-gradient(135deg, #90caf9, #80deea) !important;
  border-color: #90caf9 !important;
  color: white !important;
  cursor: not-allowed;
  box-shadow: none;
}

.records-button {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.records-button:hover {
  background-color: #3a8ee6;
  border-color: #3a8ee6;
  transform: translateY(-2px);
  color: white;
}

.action-icon, .records-icon {
  font-size: 1.4rem;
}

.nav-button {
  background-color: #fff;
  border-color: #409eff;
  color: #409eff;
  font-size: 2.5rem;
  padding: 0.8rem 1.2rem;
  border-radius: 0.8rem;
  transition: all 0.3s;
  min-width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button:hover {
  background-color: #ecf5ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.chapter-title {
  font-size: 2.2rem;
  font-weight: 600;
  color: #303133;
  padding: 0 1rem;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
}

.action-group {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

.empty-result {
  text-align: center;
  padding: 2rem 0;
  color: #999;
  font-size: 1.1rem;
}

.word-card-container {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.6rem;
}

.word-card {
  background-color: #fff;
  border-radius: 0.6rem;
  padding: 0.6rem 0.4rem;
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.word-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.word-card.selected {
  background-color: #ecf5ff;
  border: 1px solid #409eff;
}

.word-checkbox {
  margin-right: 0.5rem;
}

/* 增大单词卡片复选框的大小 */
.word-checkbox :deep(.el-checkbox__input) {
  transform: scale(1.2);
}

.word-content {
  flex: 1;
  text-align: center;
}

.word-english {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
  color: #333;
}

.word-phonetic {
  font-size: 0.85rem;
  color: #606266;
  font-style: italic;
  margin-bottom: 0.2rem;
}

.word-chinese {
  font-size: 1.1rem;
  color: #666;
  margin-top: 0.2rem;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  color: #409eff;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.loading-container .el-icon {
  margin-right: 0.5rem;
  font-size: 1.2rem;
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.word-list-loading {
  min-height: 350px;
  font-size: 1.2rem;
  position: relative;
  z-index: 10;
}

.empty-chapters {
  padding: 1rem;
}

@media (max-width: 1600px) {
  .word-card-container {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1200px) {
  .word-card-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .word-card-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .word-card-container {
    grid-template-columns: 1fr;
  }
  .selected-word-count{
    white-space: nowrap;
  }
  .chapter-title{
    font-size: 1.2rem;
  }

  .word-container {
    width: 100vw;
    padding: 1rem;
  }
  .selection-mode-controls{
    flex-wrap: wrap;
    overflow-x: scroll;
  }
  .selection-mode-left{
    font-size:12px;
  }
  .selection-mode-left .mode-label{
    font-size:14px;
    white-space: nowrap;
  }
  .selection-mode-left .selection-checkboxes{
    font-size:12px;
  }
}
</style>
import axios from 'axios'
import store from '@/store'
import router from '@/router'
import { ElMessage } from 'element-plus'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 2.5 * 60 * 1000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    if (!config.url.includes('https://www.yxjtj.cn')) {
      config.headers['Content-Language'] = `zh_CN`
    }
    const aiToken = localStorage.getItem('aiToken')
    if (aiToken) {
      config.headers['Authorization'] = `Bearer ${aiToken}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)


const excel_url = [
  '/junhengai/evaluation/import/template',
  '/junhengai/evaluation/export',
  '/junhengai/chat/random/v1/download-list',
  '/junhengai/chat/random/v1/template/excel',
  `${import.meta.env.VITE_IMAGE_CDN_API}/api/tools/upload_image`,
  `${import.meta.env.VITE_IMAGE_CDN_API}/api/tools/upload_aliyun_file`
]

// 响应拦截器cl
service.interceptors.response.use(
  response => {
    const res = response.data
    // 判断响应状态
    if (res.code === 200 || res.code == 0 || excel_url.includes(response.config.url)) {
      return res
    } else if (res.code == 401) {
      ElMessage({
        message: res.msg,
        type: 'warning'
      })
      store.dispatch('user/logout').then(() => {
        router.push('/')
      })
      return Promise.reject(false)
    } else {
      // 显示错误信息
      // 直接使用对象形式，确保HTML能正确渲染
      ElMessage({
        message: res.msg,
        type: 'error',
        dangerouslyUseHTMLString: true
      })
      return Promise.reject(new Error(res.msg))
    }
  },
  error => {
    console.error('响应错误:', error)
    
    // 处理401认证失败情况
    if (error.response && error.response.status === 401) {
      ElMessage({
        message: '因长时间未操作，您的登录状态已失效，请重新登录',
        type: 'warning'
      })
      store.dispatch('user/logout').then(() => {
        router.push('/')
      })
    } else {
      // 其他错误情况
      ElMessage({
        message: error.msg || '请求失败',
        type: 'error'
      })
    }
    
    return Promise.reject(error)
  }
)

// 上传文件到minIO
export const uploadToMinIO = async (file) => {
  let fileName = file.name
  fileName = fileName.replaceAll(',', '')
  fileName = fileName.replaceAll('，', '')

  try {
    // 第一步：获取minIO bucket信息
    const bucketResponse = await axios.get('https://test-zyzx.chinatiye.cn/resource_api/storage/getPresignedPostFormData', {
     params: {
      fileName: fileName
     }
    })

    if (!bucketResponse.data || !bucketResponse.data.data) {
      throw new Error('获取bucket信息失败')
    }
    
    const bucketInfo = bucketResponse.data.data
        
    // 第二步：构建表单数据上传到minIO
    const formData = new FormData()
    formData.append('bucket', bucketInfo.bucket)
    formData.append('key', bucketInfo.key)
    formData.append('policy', bucketInfo.policy)
    formData.append('Content-Type', file.type)
    formData.append('Content-Disposition', `filename=${fileName.replaceAll(',', '，')}`)
    formData.append('x-amz-algorithm', bucketInfo['x-amz-algorithm'])
    formData.append('x-amz-credential', bucketInfo['x-amz-credential'])
    formData.append('x-amz-date', bucketInfo['x-amz-date'])
    formData.append('x-amz-signature', bucketInfo['x-amz-signature'])
    formData.append('file', file)
    
    const uploadResponse = await axios.post(bucketInfo.postURL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 10 * 60 *1000 // 10分钟
    })
    
    // 构建文件访问URL
    const fileUrl = `${bucketInfo.postURL}/${bucketInfo.key}`
    
    return {
      url: fileUrl,
      key: bucketInfo.key,
      fileName: fileName,
      fileSize: file.size,
      fileExt: fileName.substr(fileName.lastIndexOf('.') + 1, fileName.length),
    }
    
  } catch (error) {
    console.error('上传文件到minIO失败:', error)
  }
}

export default service 
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { fileURLToPath } from 'url';
import { NodeSSH } from 'node-ssh';

// 获取当前文件的目录路径（在ES模块中）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置信息
const CONFIG = {
  ssh: {
    host: '**************',
    port: 22,
    username: 'root',
    password: 'lQKINgJYUQc^1sg@YwlQX5vc@KvD',
  },
  remotePath: '/data/tiye_project/project_junhengyun_web_test/ai_tools_system',
  buildCommand: 'npm run build:test',
  distPath: path.resolve(__dirname, '../dist')
};

// 连接SSH
async function connectSSH() {
  console.log('正在连接远程服务器...');
  const ssh = new NodeSSH();
  try {
    await ssh.connect(CONFIG.ssh);
    console.log('SSH连接成功!');
    return ssh;
  } catch (error) {
    console.error('SSH连接失败:', error.message);
    throw error;
  }
}

// 执行构建命令
function build() {
  return new Promise((resolve, reject) => {
    console.log(`正在执行构建命令: ${CONFIG.buildCommand}`);
    
    exec(CONFIG.buildCommand, { cwd: path.resolve(__dirname, '..') }, (error, stdout, stderr) => {
      if (error) {
        console.error(`构建失败: ${error.message}`);
        return reject(error);
      }
      
      console.log('构建成功!');
      console.log(stdout);
      
      if (stderr) {
        console.warn(stderr);
      }
      
      resolve();
    });
  });
}

// 上传文件到服务器
async function uploadFiles(ssh) {
  if (!fs.existsSync(CONFIG.distPath)) {
    throw new Error(`dist目录不存在: ${CONFIG.distPath}`);
  }

  console.log(`正在上传文件到远程目录: ${CONFIG.remotePath}`);
  
  // 确保远程目录存在
  await ssh.execCommand(`mkdir -p ${CONFIG.remotePath}`);
  
  // 上传dist目录中的所有文件
  const failed = [];
  const successful = [];
  
  try {
    // 检查源目录是否存在
    await ssh.putDirectory(
      CONFIG.distPath, 
      CONFIG.remotePath,
      {
        recursive: true,
        concurrency: 10,
        validate: (itemPath) => {
          const baseName = path.basename(itemPath);
          return baseName.charAt(0) !== '.'; // 不上传隐藏文件
        },
        tick: (localPath, remotePath, error) => {
          if (error) {
            failed.push(localPath);
          } else {
            successful.push(localPath);
          }
        }
      }
    );
    
    console.log('上传完成!');
    console.log(`成功: ${successful.length} 文件`);
    
    if (failed.length > 0) {
      console.error(`失败: ${failed.length} 文件`);
    }
  } catch (error) {
    console.error('上传过程出错:', error.message);
    throw error;
  }
}

// 主函数
async function deploy() {
  console.log('开始自动化部署流程...');
  
  try {
    // 1. 构建项目
    await build();
    
    // 2. 连接SSH
    const ssh = await connectSSH();
    
    // 3. 上传文件
    await uploadFiles(ssh);
    
    // 4. 关闭SSH连接
    ssh.dispose();
    
    console.log('部署完成!');
  } catch (error) {
    console.error('部署失败:', error.message);
    process.exit(1);
  }
}

// 执行部署
deploy();

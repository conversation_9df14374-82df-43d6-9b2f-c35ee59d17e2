import axios from 'axios'
import store from '@/store'
import router from '@/router'
import { ElMessage } from 'element-plus'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_JUNHENG_BASE_URL,
  timeout: 2.5 * 60 * 1000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器cl
service.interceptors.response.use(
  response => {
    const res = response.data
    // 判断响应状态
    if (res.code === 200 || res.code == 0) {
      return res
    } else if (res.code == 401) {
      ElMessage({
        message: res.msg,
        type: 'warning'
      })
      store.dispatch('user/logout').then(() => {
        router.push('/')
      })
      return Promise.reject(false)
    } else {
      // 显示错误信息
      // 直接使用对象形式，确保HTML能正确渲染
      ElMessage({
        message: res.msg,
        type: 'error',
        dangerouslyUseHTMLString: true
      })
      return Promise.reject(new Error(res.msg))
    }
  },
  error => {
    console.error('响应错误:', error)
    
    // 处理401认证失败情况
    if (error.response && error.response.status === 401) {
      ElMessage({
        message: '因长时间未操作，您的登录状态已失效，请重新登录',
        type: 'warning'
      })
      store.dispatch('user/logout').then(() => {
        router.push('/')
      })
    } else {
      // 其他错误情况
      ElMessage({
        message: error.msg || '请求失败',
        type: 'error'
      })
    }
    
    return Promise.reject(error)
  }
)

export default service 
<template>
  <div class="comments-integrated-container">
    <component 
      :is="currentComponent" 
      @switch-component="handleSwitchComponent"
    ></component>
  </div>
</template>

<script setup>
import { ref, markRaw, defineAsyncComponent,inject } from 'vue';

// 异步加载子组件以提高性能
const Comments = defineAsyncComponent(() => import('./Comments.vue'));
const BacthComments = defineAsyncComponent(() => import('./BacthComments.vue'));

// 使用markRaw避免不必要的响应式包装
const components = {
  'comments': markRaw(Comments),
  'batchComments': markRaw(BacthComments)
};

// 当前显示的组件
const currentComponent = ref(components.comments);

// 处理组件切换
const handleSwitchComponent = (componentName) => {
  if (componentName === 'comments' || componentName === 'batchComments') {
    currentComponent.value = components[componentName];
  }
};
</script>

<style scoped>
.comments-integrated-container {
  width: 100%;
  height: 100%;
}
</style> 
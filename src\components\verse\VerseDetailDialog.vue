<template>
  <div v-if="visible" class="verse-detail-overlay" @click="handleOverlayClick">
    <div class="verse-detail-container">
      <!-- 背景封面 -->
      <div 
        v-if="verseDetail?.coverImage" 
        class="background-cover"
        :style="{ backgroundImage: `url(${verseDetail.coverImage})` }"
      ></div>
      
      <!-- 主要内容区域 -->
      <div class="main-content" :class="{ 'no-cover': !verseDetail?.coverImage }">
        <!-- 关闭按钮 -->
        <div class="close-button" @click="closeDialog">
          关闭
        </div>
        
        <!-- 仅古诗模式 -->
        <div v-if="currentView === 'poem'" class="poem-only-view">
          <!-- 古诗内容 -->
          <div class="poem-info" :style="poemColorStyle">
            <h1 class="poem-title" :class="poemSizeClass">{{ verseDetail?.name }}</h1>
            <div class="poem-meta" :class="poemSizeClass">
              <span class="dynasty">{{ verseDetail?.dynasty }}</span>
              <span class="author" @click="showAuthorInfo">{{ verseDetail?.author }}</span>
            </div>
            <div class="poem-content" :class="poemSizeClass">
              <p v-for="(line, index) in poemLines" :key="index" class="poem-line">
                {{ line }}
              </p>
            </div>
          </div>
        </div>

        <!-- 数字人区域 - 浮动显示 -->
        <div v-show="currentView === 'poem' && hasDigitalHuman" class="digital-human-floating">
          <div class="digital-human-video-wrapper">
            <video
              ref="digitalHumanRef"
              :src="verseDetail.authorInfo.videoUrl"
              class="digital-human-video"
              muted
              loop
              playsinline
              @loadeddata="onDigitalHumanLoaded"
              @error="onDigitalHumanError"
            >
              您的浏览器不支持视频播放
            </video>
          </div>

          <!-- 数字人交互区域 -->
          <div class="digital-human-interaction">
            <SoundLine v-show="showVoiceLoading" :text="talkingText"/>

            <!-- 语音识别按钮 - 当合成音频没有播放时显示 -->
            <ChatBar
              v-show="!isAudioPlaying"
              class="digital-human-chat-bar"
              :disabled="shouldDisableVoiceRecognition"
              @onTalkStart="handleTalkStart"
              @onTalking="handleTalking"
              @onTalkEnd="handleTalkEnd"
              @onKeyboardClick="handleKeyboardClick"
            />

            <!-- 停止按钮 - 当合成音频正在播放时显示 -->
            <div
              v-show="isAudioPlaying"
              class="stop-button-container"
              @click="handleStopSpeaking"
              title="停止说话"
            >
              <div class="stop-button">
                <svg  style="width: 36px; height: 36px;" width="40" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="6" y="6" width="12" height="12" rx="2" fill="currentColor"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 左右布局模式 -->
        <div v-if="currentView !== 'poem'" class="split-view">
          <!-- 左侧古诗 -->
          <div class="left-poem" :style="poemColorStyle">
            <h1 class="poem-title-small" :class="poemSizeClass">{{ verseDetail?.name }}</h1>
                         <div class="poem-meta-small" :class="poemSizeClass">
               <span class="dynasty">{{ verseDetail?.dynasty }}</span>
               <span class="author" @click="showAuthorInfo">{{ verseDetail?.author }}</span>
             </div>
            <div class="poem-content-small" :class="poemSizeClass">
              <p v-for="(line, index) in poemLines" :key="index" class="poem-line">
                {{ line }}
              </p>
            </div>
          </div>
          
          <!-- 右侧内容 -->
          <div class="right-content" :style="poemColorStyle">
            <!-- 作者介绍 -->
            <div v-if="currentView === 'author'" class="content-panel">
              <div class="content-text">
                <div class="content-section-title">作者介绍</div>
                {{ verseDetail?.authContent }}
              </div>
            </div>
            
            <!-- 视频播放 -->
            <div v-else-if="currentView === 'video'" class="content-panel">
              <div class="content-text">
                <div class="content-section-title">视频讲解</div>
                <div class="video-container">
                  <video 
                    ref="videoRef"
                    :src="verseDetail?.videoUrl" 
                    controls 
                    autoplay
                    playsinline
                    class="video-player"
                    @loadstart="onVideoLoadStart"
                    @error="onVideoError"
                    @ended="onVideoEnded"
                  >
                    您的浏览器不支持视频播放
                  </video>
                </div>
              </div>
            </div>
            
            <!-- 译文 -->
            <div v-else-if="currentView === 'meaning'" class="content-panel">
              <div class="content-text">
                <div class="content-section-title">译文</div>
                {{ verseDetail?.meaning }}
              </div>
            </div>
            
            <!-- 赏析 -->
            <div v-else-if="currentView === 'appreciation'" class="content-panel">
              <div class="content-text">
                <div class="content-section-title">赏析</div>
                {{ verseDetail?.explainContent }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作按钮 -->
      <div class="bottom-actions">
        <el-button
          v-if="currentView !== 'poem'"
          type="primary"
          @click="backToPoem"
          class="action-button"
        >
          正文
        </el-button>
        <el-button
          v-if="verseDetail?.audio"
          :type="isPlaying ? 'warning' : 'primary'"
          :disabled="shouldDisableAudioButton"
          @click="toggleAudio"
          class="action-button"
        >
          {{ audioButtonText }}
        </el-button>
        
        <el-button 
          v-if="verseDetail?.videoUrl" 
          type="primary" 
          :disabled="shouldDisableAudioButton"
          @click="showVideo"
          class="action-button"
        >
          视频
        </el-button>
        
        <el-button 
          v-if="verseDetail?.meaning" 
          type="primary" 
          @click="showMeaning"
          class="action-button"
        >
          译文
        </el-button>
        
        <el-button 
          v-if="verseDetail?.explainContent" 
          type="primary" 
          @click="showAppreciation"
          class="action-button"
        >
          赏析
        </el-button>
      </div>
      
      <!-- 音频元素 -->
      <audio 
        v-if="verseDetail?.audio"
        ref="audioRef" 
        :src="verseDetail.audio" 
        @ended="onAudioEnded"
        @error="onAudioError"
        preload="metadata"
      ></audio>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import ChatBar from '@/views/components/ChatBar.vue'
import SoundLine from '@/views/components/soundLine.vue'
import {stripMarkdown, splitTextToSentences, forceSplitText} from '@/utils/tools'
import { getTTSLL } from '@/api/verse'

export default {
  name: 'VerseDetailDialog',
  components: {
    Close,
    ChatBar,
    SoundLine
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    verseDetail: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const currentView = ref('poem')
    const isPlaying = ref(false)
    const audioRef = ref(null)
    const videoRef = ref(null)
    const digitalHumanRef = ref(null)

    // 数字人交互相关状态
    const isDigitalHumanSpeaking = ref(false)
    const digitalHumanResponse = ref('')
    const controller = ref(null)

    // 语音识别相关状态
    const showVoiceLoading = ref(false)
    const talkingText = ref('')

    // 音频合成和播放相关状态
    const audioQueue = ref([]) // 音频URL队列
    const isAudioPlaying = ref(false) // 是否正在播放音频
    const currentAudio = ref(null) // 当前播放的音频对象
    const speechBuffer = ref('') // 流式文本缓冲区
    const synthQueue = ref(Promise.resolve()) // 音频合成串行队列
    const isSpeechSessionActive = ref(false) // 是否有语音会话正在进行
    const currentSessionId = ref(0) // 当前会话ID，用于标识和取消过期的音频合成
    
    const audioButtonText = computed(() => {
      if (isPlaying.value) {
        return '暂停'
      }
      if (audioRef.value && audioRef.value.currentTime > 0 && !audioRef.value.ended) {
        return '继续朗读'
      }
      return '朗读'
    })

    // 检查是否有数字人资源
    const hasDigitalHuman = computed(() => {
      return props.verseDetail?.authorInfo?.videoUrl
    })

    // 检查是否应该禁用语音识别（现在允许随时开始新对话）
    const shouldDisableVoiceRecognition = computed(() => {
      return false // 允许随时开始新的语音识别
    })

    // 检查是否应该禁用朗读按钮（当数字人正在说话时）
    const shouldDisableAudioButton = computed(() => {
      return isSpeechSessionActive.value || isAudioPlaying.value // 数字人正在说话或有音频在播放
    })

    // 处理诗词行
    const poemLines = computed(() => {
      if (!props.verseDetail?.poemLine) return []
      return props.verseDetail.poemLine.split('\n').filter(line => line.trim())
    })
    
    // 根据诗句行数确定样式类
    const poemSizeClass = computed(() => {
      const lineCount = poemLines.value.length
      if (lineCount <= 4) return 'poem-size-4'
      if (lineCount <= 6) return 'poem-size-5-6'
      if (lineCount <= 8) return 'poem-size-7-8'
      if (lineCount <= 10) return 'poem-size-9-10'
      return 'poem-size-11-plus'
    })

    // 根据字段调整字体颜色
    const poemColorStyle = computed(() => {
      return `color: ${props.verseDetail.color || 'white'}`
    })
    
    // 关闭弹窗
    const closeDialog = () => {
      if (audioRef.value) {
        audioRef.value.pause()
        audioRef.value.currentTime = 0
        isPlaying.value = false
      }

      if (videoRef.value) {
        videoRef.value.pause()
        videoRef.value.currentTime = 0
      }

      // 停止所有音频播放和数字人状态
      stopAllAudio()

      // 中断SSE连接
      if (controller.value) {
        controller.value.abort()
      }

      currentView.value = 'poem'
      emit('close')
    }
    
    // 点击遮罩层关闭
    const handleOverlayClick = (e) => {
      if (e.target.classList.contains('verse-detail-overlay')) {
        closeDialog()
      }
    }
    
    // 显示作者介绍
    const showAuthorInfo = () => {
      if (props.verseDetail?.authContent) {
        currentView.value = 'author'
      }
    }
    
    // 切换音频播放
    const toggleAudio = () => {
      if (!audioRef.value) return

      // 如果数字人正在说话，禁用朗读功能
      if (shouldDisableAudioButton.value && !isPlaying.value) {
        console.log('数字人正在说话，无法播放朗读音频')
        return
      }

      if (isPlaying.value) {
        audioRef.value.pause()
        isPlaying.value = false
        // 停止数字人说话动画
        stopDigitalHumanSpeaking()
      } else {
        // 停止数字人语音播放
        stopAllAudio()

        // 如果视频正在播放，先停止视频
        if (videoRef.value) {
          videoRef.value.pause()
          videoRef.value.currentTime = 0
          // 如果在全屏状态，退出全屏
          if (document.fullscreenElement) {
            document.exitFullscreen().catch(err => {
              console.error("退出全屏失败:", err)
            })
          }
          // 如果在视频页面，切换回诗词页面
          if (currentView.value === 'video') {
            currentView.value = 'poem'
          }
        }

        audioRef.value.play().then(() => {
          isPlaying.value = true
          // 开始数字人说话动画
          playDigitalHumanSpeaking()
        }).catch(error => {
          console.error('音频播放失败:', error)
        })
      }
    }
    
    // 显示视频
    const showVideo = async () => {
      currentView.value = 'video'
      // 如果音频正在播放，停止音频
      if (audioRef.value && isPlaying.value) {
        audioRef.value.pause()
        audioRef.value.currentTime = 0
        isPlaying.value = false
      }
      
      // 等待下一帧确保视频元素已渲染
      await nextTick()
      
      if (videoRef.value) {
        try {
          // 自动播放视频
          await videoRef.value.play()
          console.log('视频开始播放')
          
          // 延迟一下再尝试全屏，确保播放已开始
          setTimeout(async () => {
            try {
              if (videoRef.value && !document.fullscreenElement) {
                if (videoRef.value.requestFullscreen) {
                  await videoRef.value.requestFullscreen()
                  console.log('全屏成功')
                } else if (videoRef.value.webkitRequestFullscreen) {
                  await videoRef.value.webkitRequestFullscreen()
                  console.log('全屏成功 (webkit)')
                } else if (videoRef.value.mozRequestFullScreen) {
                  await videoRef.value.mozRequestFullScreen()
                  console.log('全屏成功 (moz)')
                } else if (videoRef.value.msRequestFullscreen) {
                  await videoRef.value.msRequestFullscreen()
                  console.log('全屏成功 (ms)')
                }
              }
            } catch (fullscreenError) {
              console.log('全屏请求失败:', fullscreenError)
              // 全屏失败不影响视频播放
            }
          }, 500)
          
        } catch (playError) {
          console.error('视频自动播放失败:', playError)
          ElMessage.warning('视频自动播放失败，请手动点击播放')
        }
      }
    }
    
    // 显示译文
    const showMeaning = () => {
      currentView.value = 'meaning'
    }
    
    // 显示赏析
    const showAppreciation = () => {
      currentView.value = 'appreciation'
    }
    
    // 音频播放结束
    const onAudioEnded = () => {
      isPlaying.value = false
      // 停止数字人说话动画
      stopDigitalHumanSpeaking()
    }

    // 视频播放结束
    const onVideoEnded = () => {
      if (videoRef.value) {
        videoRef.value.currentTime = 0
        if (document.fullscreenElement) {
          document.exitFullscreen().catch(err => {
            console.error("退出全屏失败:", err);
          });
        }
      }
    }
    
    // 音频加载错误
    const onAudioError = () => {
      isPlaying.value = false
    }
    
    // 视频加载开始
    const onVideoLoadStart = () => {
      console.log('视频开始加载')
    }
    
    // 视频加载错误
    const onVideoError = () => {
      console.error('视频加载失败')
    }

    // 数字人视频加载完成
    const onDigitalHumanLoaded = () => {
      if (digitalHumanRef.value) {
        // 设置视频到第一帧（嘴巴闭合状态）
        digitalHumanRef.value.currentTime = 0
      }
    }

    // 数字人视频加载错误
    const onDigitalHumanError = () => {
      console.error('数字人视频加载失败')
    }

    // 播放数字人说话动画
    const playDigitalHumanSpeaking = () => {
      if (digitalHumanRef.value) {
        digitalHumanRef.value.currentTime = 0.1 // 跳过第一帧，显示张嘴状态
        digitalHumanRef.value.play().catch(error => {
          console.error('数字人视频播放失败:', error)
        })
      }
    }

    // 停止数字人说话动画
    const stopDigitalHumanSpeaking = () => {
      if (digitalHumanRef.value) {
        digitalHumanRef.value.pause()
        digitalHumanRef.value.currentTime = 0 // 回到第一帧（嘴巴闭合状态）
      }
    }

    // 开始语音会话
    const startSpeechSession = () => {
      if (!isSpeechSessionActive.value) {
        isSpeechSessionActive.value = true
        // 只有在没有会话ID时才分配新的ID（避免覆盖handleTalkStart中设置的ID）
        if (!currentSessionId.value) {
          currentSessionId.value = Date.now()
          console.log('分配新会话ID:', currentSessionId.value)
        } else {
          console.log('使用现有会话ID:', currentSessionId.value)
        }
        // 开始数字人说话动画（只在会话开始时调用一次）
        playDigitalHumanSpeaking()
        // 开始队列监控
        startQueueMonitor()
        console.log('语音会话开始，会话ID:', currentSessionId.value)
      } else {
        console.log('语音会话已激活，会话ID:', currentSessionId.value)
      }
    }

    // 结束语音会话
    const endSpeechSession = () => {
      if (isSpeechSessionActive.value) {
        isSpeechSessionActive.value = false
        // 停止数字人说话动画
        stopDigitalHumanSpeaking()
        console.log('语音会话结束')
      }
    }

    // 音频合成队列管理函数
    const enqueueSpeechSegment = async (sentence) => {
      if (!sentence || !sentence.trim()) return

      // 开始语音会话（如果还没开始）
      startSpeechSession()

      // 记录当前会话ID，用于后续检查
      const sessionId = currentSessionId.value
      console.log('enqueueSpeechSegment - 当前会话ID:', sessionId, '句子:', sentence.substring(0, 20) + '...')

      // 将任务加入串行队列，按顺序合成音频
      synthQueue.value = synthQueue.value.then(async () => {
        try {
          // 检查会话是否仍然有效（防止过期会话的音频合成）
          if (sessionId !== currentSessionId.value) {
            console.log('会话已过期，跳过音频合成:', sentence, '当前会话ID:', currentSessionId.value, '任务会话ID:', sessionId)
            return
          }

          console.log('开始合成音频:', sentence, '会话ID:', sessionId)
          const res = await getTTSLL({
            text: stripMarkdown(sentence),
            speakerId: props.verseDetail.authorInfo.speakerCode,
            speed: props.verseDetail.authorInfo.speed,
            volume: props.verseDetail.authorInfo.volume,
            pitch: props.verseDetail.authorInfo.pitch
          })

          const url = res.data?.fileUrl
          if (url) {
            // 再次检查会话是否仍然有效（防止合成完成后会话已切换）
            if (sessionId === currentSessionId.value && isSpeechSessionActive.value) {
              audioQueue.value.push(url)
              console.log('音频合成完成并加入队列:', sentence, '队列长度:', audioQueue.value.length)

              // 如果当前没有播放音频，立即开始播放
              if (!isAudioPlaying.value) {
                console.log('当前无音频播放，立即开始处理队列，队列长度:', audioQueue.value.length)
                // 使用 setTimeout 确保状态更新后再处理队列
                setTimeout(() => {
                  console.log('延迟检查播放状态:', {
                    播放状态: isAudioPlaying.value,
                    队列长度: audioQueue.value.length,
                    会话状态: isSpeechSessionActive.value
                  })
                  if (!isAudioPlaying.value && audioQueue.value.length > 0 && isSpeechSessionActive.value) {
                    console.log('满足播放条件，开始处理队列')
                    processAudioQueue().catch(error => {
                      console.error('延迟队列处理失败:', error)
                    })
                  } else {
                    console.log('不满足播放条件，跳过队列处理')
                  }
                }, 5)
              } else {
                console.log('当前有音频播放中，等待队列处理，播放状态:', isAudioPlaying.value)
              }
            } else {
              console.log('🚫 音频合成完成但会话已过期，跳过播放')
              console.log('   句子:', sentence.substring(0, 50) + '...')
              console.log('   当前会话ID:', currentSessionId.value)
              console.log('   任务会话ID:', sessionId)
              console.log('   会话状态:', isSpeechSessionActive.value)
              console.log('   ID匹配:', currentSessionId.value === sessionId)
            }
          } else {
            console.error('音频合成返回空URL:', sentence)
          }
        } catch (error) {
          console.error('音频合成失败:', sentence, error)
          // 确保即使合成失败，Promise链也能继续
        }
      }).catch(error => {
        console.error('Promise链执行失败:', sentence, error)
        // 返回一个resolved Promise，确保链不会中断
        return Promise.resolve()
      })
    }

    // 处理音频播放队列
    const processAudioQueue = async () => {
      try {
        console.log('processAudioQueue被调用，当前状态:', {
          队列长度: audioQueue.value.length,
          播放状态: isAudioPlaying.value,
          会话状态: isSpeechSessionActive.value,
          当前音频: currentAudio.value ? '存在' : '不存在',
          会话ID: currentSessionId.value
        })

        // 防止重复调用
        if (audioQueue.value.length === 0 || isAudioPlaying.value) {
          console.log('队列处理跳过 - 队列长度:', audioQueue.value.length, '播放状态:', isAudioPlaying.value)
          return
        }

        isAudioPlaying.value = true
        const url = audioQueue.value.shift()

        if (!url) {
          console.log('队列中没有有效URL')
          isAudioPlaying.value = false
          // 延迟检查，给音频合成一些时间
          setTimeout(() => checkSpeechSessionEnd(), 100)
          return
        }

        console.log('开始播放音频:', 'url', '剩余队列长度:', audioQueue.value.length)

        // 验证URL的有效性
        if (!url || typeof url !== 'string' || url.trim() === '') {
          console.error('无效的音频URL:', 'url')
          isAudioPlaying.value = false
          setTimeout(() => processAudioQueue(), 10)
          return
        }

        console.log('创建音频对象，URL:', 'url')
        const audio = new Audio(url)
        currentAudio.value = audio

        console.log('音频对象创建成功，设置事件监听器')

        // 添加音频加载事件监听
        audio.onloadstart = () => {
          console.log('音频开始加载:','url')
        }

        audio.oncanplay = () => {
          console.log('音频可以播放:', 'url')
        }

        audio.onloadeddata = () => {
          console.log('音频数据加载完成:', 'url')
        }

        audio.onplay = () => {
          console.log('音频开始播放事件触发:', 'url')
        }

        audio.onplaying = () => {
          console.log('音频正在播放:', 'url')
        }

      audio.onended = () => {
        console.log('音频播放结束:', '剩余队列长度:', audioQueue.value.length)

        // 使用 setTimeout 避免同步调用导致的问题，但在setTimeout内部再次检查状态
        setTimeout(() => {
          isAudioPlaying.value = false
          currentAudio.value = null
          console.log('音频播放结束后处理，当前播放状态:', isAudioPlaying.value)
          processAudioQueue()
          checkSpeechSessionEnd()
        }, 10)
      }

      audio.onerror = (event) => {
        console.error('音频播放出错:', event, '剩余队列长度:', audioQueue.value.length)

        // 使用 setTimeout 避免同步调用导致的问题，但在setTimeout内部再次检查状态
        setTimeout(() => {
          isAudioPlaying.value = false
          currentAudio.value = null
          console.log('音频播放出错后处理，当前播放状态:', isAudioPlaying.value)
          processAudioQueue()
          checkSpeechSessionEnd()
        }, 10)
      }

        console.log('准备调用audio.play()，URL:', 'url', '当前播放状态:', isAudioPlaying.value)

        // 检查音频对象状态
        console.log('音频对象状态:', {
          readyState: audio.readyState,
          networkState: audio.networkState,
          paused: audio.paused,
          ended: audio.ended,
          duration: audio.duration,
          currentTime: audio.currentTime
        })

        try {
          const playPromise = audio.play()
          console.log('audio.play()调用完成，返回Promise:', playPromise)

          if (playPromise !== undefined) {
            await playPromise
            console.log('音频播放开始成功:', 'url')
          } else {
            console.log('audio.play()返回undefined，可能是旧版浏览器')
          }
        } catch (error) {
          console.error('音频播放失败:', 'url', error, '剩余队列长度:', audioQueue.value.length)

          // 检查是否是用户交互权限问题
          if (error.name === 'NotAllowedError') {
            console.error('音频播放被阻止，可能需要用户交互')
          } else if (error.name === 'NotSupportedError') {
            console.error('音频格式不支持')
          } else if (error.name === 'AbortError') {
            console.error('音频播放被中止')
          }

        // 使用 setTimeout 避免同步调用导致的问题，但在setTimeout内部再次检查状态
        setTimeout(() => {
          isAudioPlaying.value = false
          currentAudio.value = null
          console.log('音频播放失败后处理，当前播放状态:', isAudioPlaying.value)
          processAudioQueue()
          checkSpeechSessionEnd()
        }, 10)
      }
    } catch (error) {
      console.error('processAudioQueue执行失败:', error)
      isAudioPlaying.value = false
      currentAudio.value = null
      // 尝试继续处理队列
      setTimeout(() => {
        if (audioQueue.value.length > 0) {
          processAudioQueue().catch(e => console.error('队列恢复失败:', e))
        }
      }, 100)
    }
  }

    // 检查是否需要结束语音会话
    const checkSpeechSessionEnd = () => {
      console.log('检查语音会话结束条件:', {
        队列长度: audioQueue.value.length,
        音频播放中: isAudioPlaying.value,
        数字人说话中: isDigitalHumanSpeaking.value,
        会话活跃: isSpeechSessionActive.value
      })

      // 如果队列为空且没有音频在播放，且流式输出已结束，则结束语音会话
      if (audioQueue.value.length === 0 && !isAudioPlaying.value && !isDigitalHumanSpeaking.value && isSpeechSessionActive.value) {
        console.log('满足结束条件，结束语音会话')
        endSpeechSession()
      } else {
        console.log('不满足结束条件，继续等待')
      }
    }

    // 监控队列状态，防止队列卡住
    const monitorAudioQueue = () => {
      if (audioQueue.value.length > 0 && !isAudioPlaying.value && isSpeechSessionActive.value) {
        console.warn('检测到队列卡住，尝试重新启动播放', {
          队列长度: audioQueue.value.length,
          播放状态: isAudioPlaying.value,
          会话状态: isSpeechSessionActive.value,
          当前音频: currentAudio.value ? '存在' : '不存在'
        })

        // 强制重置播放状态
        if (currentAudio.value) {
          try {
            currentAudio.value.pause()
            currentAudio.value = null
          } catch (e) {
            console.error('清理当前音频失败:', e)
          }
        }

        isAudioPlaying.value = false

        // 尝试重新启动队列处理
        setTimeout(() => {
          processAudioQueue().catch(error => {
            console.error('队列恢复失败:', error)
          })
        }, 100)
      }
    }

    // 定期监控队列状态
    let queueMonitorTimer = null
    const startQueueMonitor = () => {
      if (queueMonitorTimer) {
        clearInterval(queueMonitorTimer)
      }
      queueMonitorTimer = setInterval(monitorAudioQueue, 2000) // 每2秒检查一次
    }

    const stopQueueMonitor = () => {
      if (queueMonitorTimer) {
        clearInterval(queueMonitorTimer)
        queueMonitorTimer = null
      }
    }

    // 停止所有音频播放
    const stopAllAudio = () => {
      // 停止当前播放的音频
      if (currentAudio.value) {
        currentAudio.value.pause()
        currentAudio.value.src = ''
        currentAudio.value = null
      }

      // 清空音频队列
      audioQueue.value = []
      isAudioPlaying.value = false

      // 重置音频合成队列，防止正在合成的音频继续播放
      synthQueue.value = Promise.resolve()

      // 停止队列监控
      stopQueueMonitor()

      // 结束语音会话
      endSpeechSession()

      // 清空语音缓冲区
      speechBuffer.value = ''

      console.log('已停止所有音频播放和合成')
    }

    // 数字人交互相关方法
    const handleTalkStart = (message) => {
      console.log('开始语音识别，准备新对话:', message)

      // 立即停止所有音频播放（包括古诗朗读和数字人音频）

      // 1. 停止古诗朗读音频
      if (audioRef.value) {
        audioRef.value.pause()
        audioRef.value.currentTime = 0
        isPlaying.value = false
      }

      // 2. 停止数字人音频播放和动画
      stopAllAudio()

      // 3. 生成新的会话ID，使之前的音频合成任务失效
      currentSessionId.value = Date.now()
      // 激活语音会话状态，避免后续startSpeechSession重复生成ID
      isSpeechSessionActive.value = true
      console.log('生成新会话ID并激活会话:', currentSessionId.value)

      // 4. 中断当前的SSE连接（如果有的话）
      if (controller.value) {
        controller.value.abort()
        controller.value = null
      }

      // 5. 重置数字人状态
      isDigitalHumanSpeaking.value = false
      digitalHumanResponse.value = ''
      speechBuffer.value = ''

      // 5. 确保数字人回到第一帧（闭嘴状态）
      if (digitalHumanRef.value) {
        digitalHumanRef.value.pause()
        digitalHumanRef.value.currentTime = 0
      }

      // 6. 启动队列监控
      startQueueMonitor()

      console.log('所有音频已停止，准备开始新的语音识别')

      showVoiceLoading.value = true
      talkingText.value = ''
    }

    const handleTalking = (text) => {
      console.log('语音识别中:', text)
      talkingText.value = text
    }

    const handleTalkEnd = async (text) => {
      console.log('语音识别结束:', text, '当前会话ID:', currentSessionId.value)
      showVoiceLoading.value = false
      talkingText.value = text
      if (text && text.trim()) {
        console.log('开始处理用户输入，会话ID:', currentSessionId.value)
        await handleDigitalHumanChat(text.trim())
      }
    }

    const handleKeyboardClick = () => {
      console.log('键盘输入被禁用')
    }

    // 停止数字人说话
    const handleStopSpeaking = () => {
      console.log('用户点击停止按钮，停止数字人说话')

      // 执行与 handleTalkStart 相同的停止逻辑
      // 1. 停止古诗朗读音频
      if (audioRef.value) {
        audioRef.value.pause()
        audioRef.value.currentTime = 0
        isPlaying.value = false
      }

      // 2. 停止数字人音频播放和动画
      stopAllAudio()

      // 3. 中断当前的SSE连接（如果有的话）
      if (controller.value) {
        controller.value.abort()
        controller.value = null
      }

      // 4. 重置数字人状态
      isDigitalHumanSpeaking.value = false
      digitalHumanResponse.value = ''
      speechBuffer.value = ''

      // 5. 确保数字人回到第一帧（闭嘴状态）
      if (digitalHumanRef.value) {
        digitalHumanRef.value.pause()
        digitalHumanRef.value.currentTime = 0
      }

      // 6. 停止语音识别显示
      showVoiceLoading.value = false
      talkingText.value = ''

      console.log('数字人说话已停止')
    }

    // 与数字人对话的核心方法
    const handleDigitalHumanChat = async (userInput) => {
      if (!userInput || userInput.trim() === '') {
        return
      }

      console.log('🎯 开始数字人对话处理')
      console.log('   用户输入:', userInput)
      console.log('   当前会话ID:', currentSessionId.value)
      console.log('   会话状态:', isSpeechSessionActive.value)

      // 停止古诗朗读音频
      if (audioRef.value && isPlaying.value) {
        audioRef.value.pause()
        audioRef.value.currentTime = 0
        isPlaying.value = false
      }

      // 停止之前的数字人音频播放
      stopAllAudio()

      // 开始数字人说话动画
      isDigitalHumanSpeaking.value = true

      // 创建新的控制器
      controller.value = new AbortController()

      const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`

      const requestBody = {
        messages: [{
          role: 'system',
          content: `${props.verseDetail?.authorInfo?.prompt}，你正在课堂上与用户互动，互动的古诗是：《${props.verseDetail.name}》，古诗内容：${props.verseDetail.poemLine}基于这首诗进行回答。`
        }, {
          role: 'user',
          content: userInput
        }],
        modelType: 'agent',
        formType: 'digital-c​ommon'
      }

      let accumulatedText = ''
      digitalHumanResponse.value = ''
      speechBuffer.value = '' // 重置语音缓冲区

      try {
        await fetchEventSource(apiUrl, {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify(requestBody),
          signal: controller.value.signal,
          openWhenHidden: true,

          async onopen(response) {
            if (!response.ok) throw new Error('网络请求失败')
            console.log('SSE连接已建立')
          },

          async onmessage(msg) {
            if (msg.event === 'FatalError') throw new Error(msg.data)

            const data = msg.data
            const jsonData = JSON.parse(data)

            const finishReason = jsonData.choices[0].finish_reason

            if (finishReason === 'stop') {
              console.log('数字人回答完成:', accumulatedText)
              digitalHumanResponse.value = accumulatedText
              isDigitalHumanSpeaking.value = false

              // 处理剩余的文本缓冲区
              if (speechBuffer.value.trim()) {
                const remainingSentences = forceSplitText(speechBuffer.value.trim())
                for (const sentence of remainingSentences) {
                  await enqueueSpeechSegment(sentence)
                }
                speechBuffer.value = ''
              }

              // 检查是否需要结束语音会话
              checkSpeechSessionEnd()

              return
            }

            const content = jsonData.choices[0].message.content

            if (content) {
              accumulatedText += content
              digitalHumanResponse.value = accumulatedText
              speechBuffer.value += content

              // 实时分割文本并合成音频
              const result = splitTextToSentences(speechBuffer.value)
              speechBuffer.value = result.remaining

              // 为每个完整句子合成音频
              for (const sentence of result.sentences) {
                await enqueueSpeechSegment(sentence)
              }
            }
          },

          async onclose() {
            console.log('SSE连接已关闭')
            isDigitalHumanSpeaking.value = false

            // 处理剩余的文本缓冲区
            if (speechBuffer.value.trim()) {
              const remainingSentences = forceSplitText(speechBuffer.value.trim())
              for (const sentence of remainingSentences) {
                await enqueueSpeechSegment(sentence)
              }
              speechBuffer.value = ''
            }

            // 检查是否需要结束语音会话
            checkSpeechSessionEnd()
          },

          onerror(err) {
            console.error('数字人对话出错:', err)
            isDigitalHumanSpeaking.value = false
            stopAllAudio()
            throw new Error(err)
          }
        })
      } catch (error) {
        console.error('数字人对话请求失败:', error)
        isDigitalHumanSpeaking.value = false
        stopAllAudio()
      }
    }

    // 返回正文
    const backToPoem = () => {
      currentView.value = 'poem'
    }
    
    // 监听弹窗显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        currentView.value = 'poem'
        isPlaying.value = false
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    })
    
    // 调试函数：打印当前完整状态
    const debugCurrentState = () => {
      console.log('=== 当前完整状态调试信息 ===')
      console.log('音频队列:', audioQueue.value)
      console.log('播放状态:', isAudioPlaying.value)
      console.log('会话状态:', isSpeechSessionActive.value)
      console.log('会话ID:', currentSessionId.value)
      console.log('当前音频对象:', currentAudio.value)
      console.log('数字人说话状态:', isDigitalHumanSpeaking.value)
      console.log('语音缓冲区:', speechBuffer.value)
      if (currentAudio.value) {
        console.log('当前音频详细状态:', {
          src: currentAudio.value.src,
          readyState: currentAudio.value.readyState,
          networkState: currentAudio.value.networkState,
          paused: currentAudio.value.paused,
          ended: currentAudio.value.ended,
          duration: currentAudio.value.duration,
          currentTime: currentAudio.value.currentTime,
          volume: currentAudio.value.volume,
          muted: currentAudio.value.muted
        })
      }
      console.log('=== 状态调试信息结束 ===')
    }

    // 暴露调试函数到全局，方便控制台调用
    if (typeof window !== 'undefined') {
      window.debugAudioState = debugCurrentState
    }

    // 组件卸载时清理资源
    onUnmounted(() => {
      console.log('组件卸载，清理所有音频资源')
      stopAllAudio()

      // 清理定时器
      stopQueueMonitor()

      // 清理音频对象
      if (currentAudio.value) {
        try {
          currentAudio.value.pause()
          currentAudio.value.src = ''
          currentAudio.value = null
        } catch (e) {
          console.error('清理音频对象失败:', e)
        }
      }

      // 中断SSE连接
      if (controller.value) {
        controller.value.abort()
      }

      // 清理全局调试函数
      if (typeof window !== 'undefined') {
        delete window.debugAudioState
      }
    })

    return {
      currentView,
      isPlaying,
      audioRef,
      videoRef,
      digitalHumanRef,
      hasDigitalHuman,
      shouldDisableVoiceRecognition,
      shouldDisableAudioButton,
      isDigitalHumanSpeaking,
      digitalHumanResponse,
      showVoiceLoading,
      talkingText,
      audioQueue,
      isAudioPlaying,
      currentAudio,
      speechBuffer,
      isSpeechSessionActive,
      currentSessionId,
      poemLines,
      poemSizeClass,
      poemColorStyle,
      audioButtonText,
      closeDialog,
      handleOverlayClick,
      showAuthorInfo,
      toggleAudio,
      showVideo,
      showMeaning,
      showAppreciation,
      onAudioEnded,
      onAudioError,
      onVideoLoadStart,
      onVideoError,
      onVideoEnded,
      onDigitalHumanLoaded,
      onDigitalHumanError,
      playDigitalHumanSpeaking,
      stopDigitalHumanSpeaking,
      startSpeechSession,
      endSpeechSession,
      checkSpeechSessionEnd,
      monitorAudioQueue,
      startQueueMonitor,
      stopQueueMonitor,
      enqueueSpeechSegment,
      processAudioQueue,
      stopAllAudio,
      handleTalkStart,
      handleTalking,
      handleTalkEnd,
      handleKeyboardClick,
      handleStopSpeaking,
      handleDigitalHumanChat,
      debugCurrentState,
      backToPoem
    }
  }
}
</script>

<style scoped lang="scss">
.verse-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verse-detail-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.background-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.main-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.main-content.no-cover {
  background: #000;
}

.close-button {
  position: absolute;
  bottom: 1rem;
  right: 3rem;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.8rem 2.5rem;
  border-radius: 30px;
  cursor: pointer;
  font-size: 1.6rem;
  font-weight: bold;
  transition: all 0.3s ease;
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.close-button:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* 仅古诗模式样式 */
.poem-only-view {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.poem-info {
  text-align: center;
  color: white;
}

/* 数字人浮动样式 */
.digital-human-floating {
  position: absolute;
  top: 50%;
  right: 5%;
  transform: translateY(-50%);
  z-index: 5;
}

.digital-human-video-wrapper {
  width: 280px;
  height: 498px; /* 9:16 比例 */
  position: relative;
}

.digital-human-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 停止按钮容器 - 与语音识别按钮在同一位置 */
.stop-button-container {
  position: fixed;
  bottom: -100px;
  right: 30%;
  cursor: pointer;
  z-index: 99;
  user-select: none;
  touch-action: none;
}

.stop-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 25px rgba(255, 71, 87, 0.4),
    0 0 0 4px rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.stop-button:hover {
  transform: translateY(-3px) scale(1.05);
  background: linear-gradient(135deg, #ff3742 0%, #b33a57 100%);
  box-shadow:
    0 12px 35px rgba(255, 71, 87, 0.6),
    0 0 0 6px rgba(255, 255, 255, 0.15);
}

.stop-button:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow:
    0 6px 20px rgba(255, 71, 87, 0.5),
    0 0 0 4px rgba(255, 255, 255, 0.1);
}

.stop-button svg {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.stop-button:hover svg {
  transform: scale(1.2);
}

.digital-human-interaction {
  ::v-deep {
  .voice-recognition-display {
    bottom: 40px !important;
  }
  .digital-human-chat-bar {
      bottom: -100px;
      right: 30% !important;
      .keyboard-button {
        display: none;
      }
    }
  }
}

.poem-title {
  font-size: 7rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: bold;
  margin: 0;
}

.poem-meta {
  font-size: 3.5rem;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.poem-content {
  margin-top: 10px;
  font-size: 4rem;
  line-height: 1.8;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: bold;
  max-height: 75vh;
  overflow-y: auto;
  border-radius: 1rem;
}

/* 左右布局模式样式 */
.split-view {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 3rem;
  align-items: stretch;
}

.left-poem {
  flex: 0 0 45%;
  color: white;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.poem-title-small {
  font-size: 5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: bold;
  margin: 0;
}

.poem-meta-small {
  font-size: 3rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: bold;
}

.poem-content-small {
  margin-top: 10px;
  font-size: 3rem;
  line-height: 1.8;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: bold;
}

.right-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.content-panel {
  width: 100%;
  text-align: center;
}

.content-text {
  font-size: 2rem;
  line-height: 1.8;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: bold;
  text-align: left;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  max-height: 70vh;
  overflow-y: auto;
  white-space: pre-line;
}

.content-section-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* 通用样式 */
.dynasty {
  position: relative;
}

/* 根据诗句行数调整样式 */
/* 4行及以下（当前默认样式已适配） */
.poem-title.poem-size-4 {
  font-size: 7rem;
}

.poem-meta.poem-size-4 {
  font-size: 3.5rem;
}

.poem-content.poem-size-4 {
  font-size: 4rem;
  line-height: 1.8;
}

.poem-title-small.poem-size-4 {
  font-size: 5rem;
}

.poem-meta-small.poem-size-4 {
  font-size: 3rem;
}

.poem-content-small.poem-size-4 {
  font-size: 3rem;
  line-height: 1.8;
}

/* 5-6行 */
.poem-title.poem-size-5-6 {
  font-size: 6rem;
}

.poem-meta.poem-size-5-6 {
  font-size: 3rem;
}

.poem-content.poem-size-5-6 {
  font-size: 3.5rem;
  line-height: 1.4;
}

.poem-title-small.poem-size-5-6 {
  font-size: 4.5rem;
}

.poem-meta-small.poem-size-5-6 {
  font-size: 2.8rem;
}

.poem-content-small.poem-size-5-6 {
  font-size: 2.8rem;
  line-height: 1.4;
}

/* 7-8行 */
.poem-title.poem-size-7-8 {
  font-size: 5rem;
}

.poem-meta.poem-size-7-8 {
  font-size: 2.8rem;
}

.poem-content.poem-size-7-8 {
  font-size: 2.8rem;
  line-height: 1.2;
}

.poem-title-small.poem-size-7-8 {
  font-size: 4rem;
}

.poem-meta-small.poem-size-7-8 {
  font-size: 2.5rem;
}

.poem-content-small.poem-size-7-8 {
  font-size: 2.5rem;
  line-height: 1.2;
}

/* 9-10行 */
.poem-title.poem-size-9-10 {
  font-size: 4.5rem;
}

.poem-meta.poem-size-9-10 {
  font-size: 2.5rem;
}

.poem-content.poem-size-9-10 {
  font-size: 2.5rem;
  line-height: 1.2;
}

.poem-title-small.poem-size-9-10 {
  font-size: 3.8rem;
}

.poem-meta-small.poem-size-9-10 {
  font-size: 2.3rem;
}

.poem-content-small.poem-size-9-10 {
  font-size: 2.3rem;
  line-height: 1.2;
}

/* 11行以上 */
.poem-title.poem-size-11-plus {
  font-size: 4rem;
}

.poem-meta.poem-size-11-plus {
  font-size: 2rem;
}

.poem-content.poem-size-11-plus {
  font-size: 2rem;
  line-height: 1.2;
  height: 75vh;
}

.poem-title-small.poem-size-11-plus {
  font-size: 3.5rem;
}

.poem-meta-small.poem-size-11-plus {
  font-size: 2rem;
}

.poem-content-small.poem-size-11-plus {
  font-size: 2rem;
  line-height: 1.2;
}

.dynasty::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -2rem;
  transform: translateY(-50%);
  width: 10px;
  height: 10px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.author {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 3rem;
  padding: 0.5rem 1.5rem;
  border: 1px solid #a0cfff;
  border-radius: 20px;
}

.author:hover {
  color: #ffd700;
  transform: scale(1.05);
  border-color: #ffd700;
  background-color: rgba(255, 255, 255, 0.1);
}

.poem-line {
  margin: 0.5rem 0;
}

.video-container {
  width: 100%;
}

.video-player {
  width: 100%;
  height: 350px;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.bottom-actions {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 3rem;
  z-index: 10;
}

.action-button {
  font-size: 1.6rem;
  padding: 1.8rem 3.5rem;
  border-radius: 2.5rem;
  font-weight: bold;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }
  
  /* 移动端统一较小的字体 */
  .poem-title,
  .poem-title.poem-size-4,
  .poem-title.poem-size-5-6,
  .poem-title.poem-size-7-8,
  .poem-title.poem-size-9-10,
  .poem-title.poem-size-11-plus {
    font-size: 2.5rem;
  }
  
  .poem-meta,
  .poem-meta.poem-size-4,
  .poem-meta.poem-size-5-6,
  .poem-meta.poem-size-7-8,
  .poem-meta.poem-size-9-10,
  .poem-meta.poem-size-11-plus {
    font-size: 1.2rem;
  }
  
  .poem-content,
  .poem-content.poem-size-4,
  .poem-content.poem-size-5-6,
  .poem-content.poem-size-7-8,
  .poem-content.poem-size-9-10,
  .poem-content.poem-size-11-plus {
    font-size: 1.5rem;
    line-height: 2;
    max-height: 40vh;
    padding: 0.8rem;
  }
  
  .split-view {
    flex-direction: column;
    gap: 2rem;
  }
  
  .left-poem {
    flex: none;
    padding: 1rem;
  }
  
  .poem-title-small,
  .poem-title-small.poem-size-4,
  .poem-title-small.poem-size-5-6,
  .poem-title-small.poem-size-7-8,
  .poem-title-small.poem-size-9-10,
  .poem-title-small.poem-size-11-plus {
    font-size: 2rem;
  }
  
  .poem-meta-small,
  .poem-meta-small.poem-size-4,
  .poem-meta-small.poem-size-5-6,
  .poem-meta-small.poem-size-7-8,
  .poem-meta-small.poem-size-9-10,
  .poem-meta-small.poem-size-11-plus {
    font-size: 1.2rem;
  }
  
  .poem-content-small,
  .poem-content-small.poem-size-4,
  .poem-content-small.poem-size-5-6,
  .poem-content-small.poem-size-7-8,
  .poem-content-small.poem-size-9-10,
  .poem-content-small.poem-size-11-plus {
    font-size: 1.2rem;
    line-height: 1.8;
  }
  
  .right-content {
    padding: 1rem;
  }
  
  .content-text {
    font-size: 1.1rem;
    padding: 1.5rem;
    max-height: 40vh;
  }
  
  .video-player {
    height: 200px;
  }
  
  .bottom-actions {
    bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .action-button {
    font-size: 1rem;
    padding: 0.8rem 1.5rem;
  }
  
  .close-button {
    bottom: 1rem;
    right: 1rem;
    font-size: 1rem;
    padding: 0.6rem 1.2rem;
  }
  
  .dynasty::after {
    width: 5px;
    height: 5px;
    right: -0.6rem;
  }
  
  .author {
    margin-left: 1.2rem;
  }

  /* 移动端数字人样式调整 */
  .digital-human-floating {
    position: fixed;
    top: auto;
    bottom: 120px;
    right: 10px;
    transform: none;
  }

  .digital-human-video-wrapper {
    width: 120px;
    height: 214px; /* 保持9:16比例，移动端稍小 */
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
  }

  .stop-button-container {
    bottom: 120px;
    right: 20px;
  }

  .stop-button {
    width: 60px;
    height: 60px;
    border-width: 2px;
  }

  .stop-button svg {
    width: 20px;
    height: 20px;
  }

  .digital-human-interaction {
    ::v-deep {
      .digital-human-chat-bar {
        bottom: 120px !important;
        right: 20px !important;

        .voice-button {
          width: 60px;
          height: 60px;
        }

        .voice-icon {
          font-size: 24px;
        }
      }
    }
  }
}
</style> 
<template>
  <div class="comments-container">
    <div class="header">
      <h1 class="title">批量生成学生评语</h1>
      <p class="subtitle">基于AI技术，一键生成全班学生评语，高效省时</p>
      <div class="page-navigation">
        <el-button type="primary" class="navigate-button" @click="$emit('switch-component', 'comments')" size="large">
          学生评语助手
        </el-button>
      </div>
    </div>


    <div class="form-container">
      <div class="record-button-container">
        <el-button
          type="primary"
          size="large"
          class="record-button"
          @click="openRecordDialog"
        >
          <el-icon style="font-size: 20px; margin-right: 5px;"><Document /></el-icon>
          我的评语记录
        </el-button>
      </div>
      <el-form :model="formData" label-width="120px" class="comment-form" size="large">
        <div class="grade-section">
          <el-form-item label="学段/年级" required>
            <div class="grade-selection-container">
              <div class="stage-buttons">
                <el-button-group>
                  <el-button
                    v-for="item in gradeOptions"
                    :key="item.value"
                    :type="formData.selectedStage === item.value ? 'primary' : ''"
                    @click="selectStage(item.value)"
                    round
                  >
                    {{ item.label }}
                  </el-button>
                </el-button-group>
              </div>

              <div class="grade-buttons-container" v-if="formData.selectedStage">
                <span class="required-star">*</span>
                <div class="grade-buttons">
                  <el-button
                    v-for="item in currentGradeOptions"
                    :key="item.value"
                    :type="formData.selectedGrade === item.value ? 'primary' : 'default'"
                    @click="selectGrade(item.value)"
                    class="option-button"
                    round
                  >
                    {{ item.label }}
                  </el-button>
                </div>
<!--                <div class="desc" v-if="!formData.selectedGrade"><el-icon color="#f56c6c"><WarningFilled /></el-icon>生成评语前请先选择学段/年级</div>-->
              </div>
            </div>

          </el-form-item>

        </div>

        <div class="upload-section" style="padding-top: 6px;">
          <el-form-item label="批量数据" required>
            <div class="upload-container">
              <div class="upload-box">
                <el-upload
                  @click="handleUploadClick"
                  ref="uploadRef"
                  class="excel-uploader drag-uploader"
                  action="#"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :limit="1"
                  :before-upload="beforeUpload"
                  accept=".xlsx, .xls"
                  :file-list="fileList"
                  :multiple="false"
                  :on-exceed="handleExceed"
                  drag
                  :disabled="!formData.selectedStage || !formData.selectedGrade"
                >
                  <div class="upload-drag-area">
                    <img src="@/assets/upload-icon.svg" class="upload-icon" alt="上传图标" />
                    <div class="upload-text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="upload-tip">
                      请下载
                      <el-popover
                        placement="top"
                        :width="300"
                        trigger="hover"
                        content="点击下载模板文件，填写后上传"
                      >
                        <template #reference>
                          <el-link type="primary" :underline="true" @click.stop.prevent="downloadTemplate">批量生成学生评语.xlsx</el-link>
                        </template>
                      </el-popover>
                      填入学生姓名、表扬项、不足项、限制字数，点击批量生成评语
                    </div>
                  </div>
                </el-upload>
              </div>
            </div>
          </el-form-item>

          <div v-if="generateStatus === 'processing'" class="progress-container">
            <el-progress :percentage="progress" :stroke-width="10" striped striped-flow :duration="10" />
          </div>

          <div class="action-buttons-container">
            <el-button
              :type="loading ? 'warning' : (generateStatus === 'success' ? 'primary' : 'primary')"
              size="large"
              v-click-throttle="batchGenerateComments"
              :loading="loading || generateStatus === 'processing'"
              round
            >
              <template v-if="generateStatus === 'processing'">
                正在生成评语
              </template>
              <template v-else-if="generateStatus === 'success'">
                重新生成评语
              </template>
              <template v-else>
                批量生成评语
              </template>
            </el-button>

            <template v-if="generateStatus === 'success'">
              <el-button
                style="margin-left: 10px;"
                type="primary"
                size="large"
                v-click-throttle="previewGeneratedResults"
                round
              >
                <el-icon style="margin-right: 3px;"><View /></el-icon>
                预览
              </el-button>
              <el-button
                type="success"
                size="large"
                class="download-button"
                v-click-throttle="downloadGeneratedResults"
                round
              >
                <el-icon style="margin-right: 3px;"><Download /></el-icon>
                下载学生评语
              </el-button>
            </template> 
          </div>

          <!-- 生成成功后显示的提示信息 -->
          <div v-if="generateStatus === 'success'" class="download-hint">
            <el-icon class="hint-icon"><Download /></el-icon>
            <span>学生评语已生成，点击“预览”，可浏览学生评语，点击“下载学生评语”，可下载学生评语</span>
          </div>
        </div>

      </el-form>
    </div>

    <!-- 评语记录弹窗 -->
    <el-dialog
      v-model="recordDialogVisible"
      title="我的评语记录"
      width="800px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-table
        :data="recordList"
        border
        style="width: 100%"
        v-loading="recordLoading"
        :cell-style="{fontSize: '14px', padding: '12px 0'}"
        :header-cell-style="{fontSize: '15px', fontWeight: 'bold', background: '#f5f7fa', padding: '12px 0'}"
      >
        <el-table-column prop="fileName" label="导入文件" min-width="200" align="center">
          <template #default="scope">
            <el-popover
              placement="top"
              :width="170"
              trigger="hover"
              content="点击下载原始导入文件"
            >
              <template #reference>
                <el-link type="primary" :underline="false" @click="downloadRecord(scope.row, 1)">{{ scope.row.fileName }}</el-link>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="fileName" label="评语文件" min-width="200" align="center">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; gap: 1rem;">
              <el-popover
                placement="top"
                :width="210"
                trigger="hover"
                content="点击下载生成的学生评语文件"
              >
                <template #reference>
                  <el-link type="primary" :underline="false" @click="downloadRecord(scope.row, 2)">学生评语.xlsx</el-link>
                </template>
              </el-popover>
              <el-button style="margin-left: 5px;" type="success" link @click="previewRecord(scope.row)">预览</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="生成时间" width="180" align="center" />
      </el-table>
      <template #footer>
        <el-button class="dialog-exit-button" @click="recordDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 预览弹窗 -->
    <PreviewDialog v-model="previewDialogVisible" :preview-data="previewData" :loading="previewLoading" />
  </div>
</template>

<script>
import { ref, computed, onUnmounted,inject } from 'vue'
import { CopyDocument, Upload, Download, Document, Link, View,WarningFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import MarkdownIt from "markdown-it"
import { downloadTemplate as fetchTemplate, importData, getImportStatus, downloadResult, getImportList, getExcelData } from '@/api/comments'
import PreviewDialog from '@/components/comments/PreviewDialog.vue'

export default {
  name: 'Comments',
  components: {
    CopyDocument,
    Upload,
    Download,
    Document,
    Link,
    View,
    PreviewDialog,
    WarningFilled
  },
  setup() {
    const formData = ref({
      selectedStage: '小学',
      selectedGrade: ''
    })
    const trackingService = inject('trackingService');
    const loading = ref(false)
    const uploadRef = ref(null) // 上传组件引用
    const fileList = ref([])
    const importId = ref(null)
    const generateStatus = ref('') // pending, processing, success, error
    const progress = ref(0) // 生成进度
    const polling = ref(null) // 轮询定时器

    // 预览弹窗相关
    const previewDialogVisible = ref(false)
    const previewData = ref([])
    const previewLoading = ref(false)

    // 评语记录弹窗相关
    const recordDialogVisible = ref(false)
    const recordList = ref([])
    const recordLoading = ref(false)

    // 学段选项
    const gradeOptions = [
      { value: '小学', label: '小学' },
      { value: '初中', label: '初中' },
      { value: '高中', label: '高中' }
    ]

    // 年级选项（按学段分组）
    const gradeMap = {
      '小学': [
        { value: '一年级', label: '一年级' },
        { value: '二年级', label: '二年级' },
        { value: '三年级', label: '三年级' },
        { value: '四年级', label: '四年级' },
        { value: '五年级', label: '五年级' },
        { value: '六年级', label: '六年级' }
      ],
      '初中': [
        { value: '初一', label: '初一' },
        { value: '初二', label: '初二' },
        { value: '初三', label: '初三' }
      ],
      '高中': [
        { value: '高一', label: '高一' },
        { value: '高二', label: '高二' },
        { value: '高三', label: '高三' }
      ]
    }

    // 当前可选的年级选项
    const currentGradeOptions = computed(() => {
      if (!formData.value.selectedStage) return []
      return gradeMap[formData.value.selectedStage] || []
    })

    // 表单验证
    const isFormValid = computed(() => {
      return formData.value.selectedStage &&
             formData.value.selectedGrade &&
             fileList.value.length > 0
    })

    // 选择学段
    const selectStage = (stage) => {
      formData.value.selectedStage = stage
      formData.value.selectedGrade = ''
    }

    // 选择年级
    const selectGrade = (grade) => {
      formData.value.selectedGrade = grade
    }

    // 处理文件上传前的验证
    const beforeUpload = (file) => {
      if (!formData.value.selectedStage || !formData.value.selectedGrade) {
        ElMessage.warning('请选择学段/年级')
        return false
      }

      // 检查文件类型
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel' ||
                      file.name.endsWith('.xlsx') ||
                      file.name.endsWith('.xls')

      if (!isExcel) {
        ElMessage.warning('只能上传 Excel 文件（.xlsx, .xls）')
        return false
      }

      return true
    }

    // 处理文件超出限制时的操作（用于替换文件）
    const handleExceed = (files) => {
      // 如果没有选择学段/年级，不允许上传
      if (!formData.value.selectedStage || !formData.value.selectedGrade) {
        ElMessage.warning('请选择学段/年级')
        return
      }

      // 先清除当前文件
      uploadRef.value.clearFiles()
      fileList.value = []

      // 重置生成状态
      if (generateStatus.value === 'success') {
        generateStatus.value = ''
      }

      // 添加新文件
      const file = files[0]
      uploadRef.value.handleStart(file)

      // 更新文件列表
      fileList.value = [{
        name: file.name,
        raw: file
      }]
    }

    // 处理文件变化
    const handleFileChange = (file, _) => {
      // 如果没有选择学段/年级，不允许上传
      if (!formData.value.selectedStage || !formData.value.selectedGrade) {
        ElMessage.warning('请选择学段/年级')
        // 清空文件列表
        setTimeout(() => {
          uploadRef.value.clearFiles()
          fileList.value = []
        }, 0)
        return false
      }

      // 更新文件列表
      if (file && file.status === 'ready') {
        fileList.value = [{
          name: file.name,
          raw: file.raw
        }]

        // 重置生成状态
        if (generateStatus.value === 'success') {
          generateStatus.value = ''
        }
      }
    }

    // 移除文件
    const handleFileRemove = () => {
      fileList.value = []
      // 重置生成状态，如果之前有生成过
      if (generateStatus.value === 'success') {
        generateStatus.value = ''
      }
    }

    // 下载模板
    const downloadTemplate = async () => {
      try {
        const response = await fetchTemplate()
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = '批量生成学生评语.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        ElMessage.success('批量生成学生评语.xlsx下载成功')
      } catch (error) {
        console.error('下载模板失败:', error)
      }
    }

    // 批量生成评语
    const batchGenerateComments = async () => {
      if (!formData.value.selectedStage || !formData.value.selectedGrade) {
        return ElMessage.warning('请选择学段/年级')
      }

      // 如果文件列表为空，但上传组件有文件，则同步文件列表
      if (fileList.value.length === 0 && uploadRef.value?.uploadFiles?.length > 0) {
        const file = uploadRef.value.uploadFiles[0]
        fileList.value = [{
          name: file.name,
          raw: file.raw
        }]
      }

      if(fileList.value.length == 0) {
        return ElMessage.warning('请上传excel文件')
      }

      // 停止之前的轮询（如果有）
      stopPolling()

      loading.value = true
      generateStatus.value = 'pending'

      try {
        const formDataObj = new FormData()
        formDataObj.append('file', fileList.value[0].raw)
        formDataObj.append('gradeName', formData.value.selectedGrade)

        const response = await importData(formDataObj)

        if (response && response.code === 200) {
          importId.value = response.data
          generateStatus.value = 'processing'
          // 开始轮询查询状态
          progress.value = 0
          startPolling()

          ElMessage.success('文件上传成功，正在生成评语')
        } else {
          generateStatus.value = 'error'
        }
      } catch (error) {
        console.error('批量生成评语失败:', error)
        generateStatus.value = 'error'
      } finally {
        loading.value = false
      }
      trackingService.trackEvent('CommAI')

    }

    // 开始轮询查询状态
    const startPolling = () => {
      // 设置5秒的轮询间隔
      polling.value = setInterval(checkImportStatus, 5000)
    }

    // 停止轮询
    const stopPolling = () => {
      if (polling.value) {
        clearInterval(polling.value)
        polling.value = null
      }
    }

    // 检查导入状态
    const checkImportStatus = async () => {
      try {
        const response = await getImportStatus()

        if (response && response.code === 200) {
          const data = response.data
          const total = data.total
          const processed = (data.success || 0) + (data.fail || 0)

          if (total > 0) {
            progress.value = Math.round((processed / total) * 100)
          } else {
            progress.value = 0 // 避免除以零
          }

          if (processed >= total) {
            // 处理完成
            generateStatus.value = 'success'
            progress.value = 100 // 确保进度为100%
            stopPolling()
            ElMessage.success(`评语生成完成：成功 ${data.success || 0} 条，失败 ${data.fail || 0} 条`)
          } else {
            // 仍在处理中
            generateStatus.value = 'processing'
          }
        } else {
          // 接口返回错误或code不为200，也应停止轮询并标记错误
          generateStatus.value = 'error'
          stopPolling()
        }
      } catch (error) {
        console.error('查询导入状态失败:', error)
        generateStatus.value = 'error' // 网络或其他错误
        stopPolling()
      }
    }

    // 下载生成结果
    const downloadGeneratedResults = async () => {
      try {
        const response = await downloadResult({})
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = '学生评语.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        ElMessage.success('学生评语.xlsx下载成功')
      } catch (error) {
        console.error('下载结果失败:', error)
      }
    }

    // 打开评语记录弹窗
    const openRecordDialog = async () => {
      recordDialogVisible.value = true
      await fetchRecordList()
    }

    // 获取评语记录列表
    const fetchRecordList = async () => {
      recordLoading.value = true
      try {
        const response = await getImportList()
        if (response && response.code === 200) {
          recordList.value = response.data || []
        } else {
          recordList.value = []
        }
      } catch (error) {
        console.error('获取评语记录失败:', error)
        recordList.value = []
      } finally {
        recordLoading.value = false
      }
    }

    // 下载评语记录
    const downloadRecord = async (row, type) => {
      if (type === 1) {
        // 使用隐藏iframe下载，避免页面闪烁
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = row.path;
        document.body.appendChild(iframe);

        // 监听iframe加载完成事件
        iframe.onload = () => {
          // 加载完成后移除iframe
          setTimeout(() => {
            document.body.removeChild(iframe);
          }, 1000); // 给下载一些时间启动
        };

        // 如果加载失败，也要移除iframe
        iframe.onerror = () => {
          document.body.removeChild(iframe);
          ElMessage.error('下载文件失败');
        };
      } else {
        try {
          const response = await downloadResult({ evaId: row.evaId })
              // 创建Blob对象
          const blob = new Blob([response], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });

          // 创建下载链接
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = '学生评语.xlsx';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } catch (error) {
          console.error('下载评语记录失败:', error)
        }
      }
    }

    const previewGeneratedResults = () => {
      previewRecord()
    }

    const previewRecord = async (row) => {
      previewDialogVisible.value = true
      previewLoading.value = true
      try {
        const data = {}
        if (row && row.evaId) {
          data.evaId = row.evaId
        }
        const response = await getExcelData(data)
        if (response && response.data) {
          const apiData = response.data
          previewData.value = apiData || []
        } else {
          previewData.value = []
        }
      } catch (error) {
        console.error('获取预览数据失败:', error)
      } finally {
        previewLoading.value = false
      }
    }

    const handleUploadClick = () => {
      if (!formData.value.selectedStage || !formData.value.selectedGrade) {
        ElMessage.warning('请选择学段/年级')
        return
      }
    }

    // 组件卸载时清理轮询
    onUnmounted(() => {
      stopPolling()
    })


    return {
      formData,
      gradeOptions,
      currentGradeOptions,
      CopyDocument,
      Upload,
      Download,
      Document,
      Link,
      selectStage,
      selectGrade,
      fileList,
      uploadRef,
      beforeUpload,
      handleExceed,
      handleFileChange,
      handleFileRemove,
      downloadTemplate,
      batchGenerateComments,
      isFormValid,
      loading,
      generateStatus,
      progress,
      downloadGeneratedResults,
      // 评语记录相关
      recordDialogVisible,
      recordList,
      recordLoading,
      openRecordDialog,
      downloadRecord,
      previewGeneratedResults,
      previewRecord,
      // 预览
      previewDialogVisible,
      previewData,
      previewLoading,
      handleUploadClick
    }
  }
}
</script>

<style scoped>
.comments-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "✨";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 255, 0.08);
  position: relative;
}

.record-button-container {
  position: absolute;
  top: 2.5rem;
  right: 1rem;
  z-index: 10;
}

.comment-form {
  width: 100%;
  margin: 0 auto;
}

.full-width {
  width: 100%;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-content {
  text-align: left;
  padding: 2rem;
  position: relative;
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.comment-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.25s ease;
  background-color: rgba(44, 134, 255, 0.1);
  color: #2c86ff;
  width: 38px;
  height: 38px;
  padding: 0;
}

.copy-btn:hover {
  transform: translateY(-2px);
  background-color: rgba(44, 134, 255, 0.2);
  box-shadow: 0 4px 12px rgba(44, 134, 255, 0.2);
}

.copy-btn:active {
  transform: translateY(0);
}

.button-group-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.option-button {
  margin-right: 0;
  transition: all 0.3s;
}

.option-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tag-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-button {
  margin-left: 0 !important;
  margin-right: 0;
  transition: all 0.3s;
  min-width: 90px;
}

.tag-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selected-count {
  font-size: 0.9rem;
  color: #909399;
  margin-top: 12px;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-block;
}

/* 自定义el-radio-button样式，使其更圆润 */
:deep(.el-radio-button__inner) {
  padding: 10px 20px;
  border-radius: 20px !important;
  margin: 0 2px;
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 20px !important;
}

:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 20px !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
  padding: 0 15px;
}

:deep(.el-form-item) {
  margin-bottom: 25px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .comments-container {
    width: 100vw;
    padding: 1.5rem 1rem;
  }
  .record-button-container{
    top: -2.5rem;
  }
  .upload-box{
    min-width: 100px !important;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0;
  }

  .comment-text {
    font-size: 1rem;
  }

  .tag-buttons-container {
    gap: 8px;
  }

  .tag-button {
    font-size: 0.9rem;
    padding: 6px 10px;
    min-width: 70px;
  }

  :deep(.el-radio-button__inner) {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .button-group-container {
    gap: 8px;
  }

  .selection-hint {
    padding-left: 0;
  }

  .upload-container {
    flex-direction: column;
    gap: 15px;
  }

  .upload-box {
    width: 100%;
  }

  .template-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .form-container {
    margin: 2rem 0;
    padding: 1rem;
  }

  .result-content {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .comment-text {
    padding: 1rem;
  }

  .button-group-container, .tag-buttons-container {
    gap: 6px;
  }

  .tag-button {
    font-size: 0.8rem;
    padding: 4px 8px;
    margin-bottom: 4px;
    min-width: 60px;
  }

  :deep(.el-radio-button__inner) {
    padding: 6px 10px;
    font-size: 0.8rem;
  }

  .template-actions {
    flex-direction: column;
    gap: 10px;
  }

  .template-actions .el-button {
    width: 100%;
  }
}

.grade-section {
  margin-bottom: 20px;
}

.grade-selection-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.stage-buttons {
  display: flex;
  justify-content: flex-start;
}

.grade-buttons-container {
  position: relative;
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  .desc{
    position: absolute;
    left: 0;
    bottom: -32px;
    margin-left: 30px;
    color: #f56c6c;
    letter-spacing: 2px;
    display: flex;
    align-items: center;
    white-space: nowrap;
  }
}

.grade-buttons {
  display: flex;
  flex-wrap: wrap;
}

.required-star {
  color: #f56c6c;
  font-size: 16px;
  font-weight: bold;
  margin-right: 5px;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}

.selection-hint {
  padding: 0 0 15px 120px;
}

.hint-warning {
  color: #e6a23c;
  font-size: 0.9rem;
  margin: 0;
}

.hint-success {
  color: #67c23a;
  font-size: 0.9rem;
  margin: 0;
}

.selection-count-divider {
  margin: 0 10px;
  color: #909399;
}

:deep(.el-alert__title) {
  font-size: 0.95rem;
}

:deep(.el-alert--warning.is-light) {
  background-color: #fdf6ec;
  border: 1px solid rgba(230, 162, 60, 0.2);
}

:deep(.el-alert--success.is-light) {
  background-color: #f0f9eb;
  border: 1px solid rgba(103, 194, 58, 0.2);
}

/* 文件上传相关样式 */
.upload-section {
  margin-bottom: 20px;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
}

.upload-box {
  flex: 1;
  min-width: 300px;
}

.excel-uploader {
  width: 100%;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
  line-height: 1.5;
}

/* 拖拽上传区域样式 */
.drag-uploader :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 10px;
  background-color: #fafafa;
  transition: all 0.3s;
}

.drag-uploader :deep(.el-upload-dragger:hover) {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.upload-drag-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
  color: #c0c4cc;
}

.upload-text {
  font-size: 14px;
  color: #606266;
  text-align: center;
  margin-bottom: 8px;
}

.upload-text em {
  color: #409EFF;
  font-style: normal;
  font-weight: 600;
}

:deep(.el-link) {
  font-weight: 500;
}

:deep(.el-link:hover) {
  text-decoration: underline;
}

.page-navigation {
  margin-top: 1.5rem;
}
.navigate-button {
  padding: 0.8rem 1.5rem;
  font-size: 1.1rem;
  transition: all 0.3s;
}

.navigate-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 150, 0.15);
}

.progress-container {
  width: 60%;
  margin: 20px 0 0 120px;
}

.action-buttons-container .el-button {
  transition: all 0.3s;
}

.action-buttons-container .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-buttons-container .el-link {
  transition: color 0.3s, transform 0.3s;
}

.action-buttons-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 20px;
  align-items: center;
  gap: 20px;
}

.action-buttons-container .el-button {
  transition: all 0.3s;
}

.action-buttons-container .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.download-button {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: white !important;
  font-weight: bold !important;
  font-size: 1.1rem !important;
  padding: 12px 24px !important;
  position: relative;
  overflow: hidden;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

.download-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  padding: 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
  color: #67c23a;
}

.hint-icon {
  margin-right: 8px;
  font-size: 18px;
}

.record-button {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  transition: all 0.3s;
  border-radius: 20px;
}

.record-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 150, 0.15);
}
</style>

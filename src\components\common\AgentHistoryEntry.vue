<template>
  <div class="history-entry-container">
    <el-button
      type="primary"
      class="history-entry-button"
      @click="showHistory"
      size="large"
    >
      <el-icon class="history-icon"><ChatDotRound /></el-icon>
      我的对话记录
    </el-button>

    <agent-history-dialog
      v-model:visible="historyVisible"
      :category="category"
      @history-cleared="handleHistoryCleared"
    />
  </div>
</template>

<script>
import { ref, defineComponent } from 'vue'
import AgentHistoryDialog from './AgentHistoryDialog.vue'
import { ChatDotRound } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'AgentHistoryEntry',
  components: {
    AgentHistoryDialog,
    ChatDotRound
  },
  props: {
    category: {
      type: String,
      required: true
    }
  },
  emits: ['history-cleared'],
  setup(props, { emit }) {
    const historyVisible = ref(false)

    // 显示历史记录
    const showHistory = () => {
      historyVisible.value = true
    }

    // 处理历史记录清空事件
    const handleHistoryCleared = () => {
      // 将事件传递给父组件
      emit('history-cleared')
    }

    return {
      historyVisible,
      showHistory,
      handleHistoryCleared
    }
  }
})
</script>

<style scoped>
.history-entry-container {
  position: absolute;
  top: 25px;
  right: 25px;
  z-index: 10;
}

.history-entry-button {
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 0, 150, 0.2);
  border-radius: 25px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  background-color: #4c85f2;
  border-color: #4c85f2;
  padding: 12px 20px;
  font-size: 16px;
}

.history-entry-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 150, 0.25);
  background-color: #3a75e0;
}

.history-icon {
  margin-right: 8px;
  font-size: 20px;
}

@media (max-width: 768px) {
  .history-entry-container {
    top: 15px;
    right: 15px;
  }

  .history-entry-button {
    padding: 8px 15px;
    font-size: 14px;
    border-radius: 20px;
  }

  .history-icon {
    margin-right: 6px;
    font-size: 18px;
  }
}
</style>
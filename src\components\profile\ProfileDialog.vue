<template>
  <el-dialog
    v-model="dialogVisible"
    title="个人中心"
    width="600px"
    :before-close="handleClose"
    class="profile-dialog"
  >
    <!-- 登录状态下的页面 -->
    <div v-if="isLoggedIn" class="profile-content">
      <div class="avatar-section">
        <el-avatar :size="100" :src="userInfo.avatar || defaultAvatar" class="user-avatar"></el-avatar>
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :http-request="uploadAvatar"
          accept=".jpg,.jpeg,.png,.gif,.webp"
        >
          <el-button type="primary" link :icon="EditPen">更换头像</el-button>
        </el-upload>
      </div>
      
      <div class="info-section">
        <el-form :model="userForm" label-width="80px" ref="formRef">
          <el-form-item label="手机号">
            <el-input v-model="userInfo.phonenumber" disabled></el-input>
          </el-form-item>
          <el-form-item label="昵称" prop="nickName">
            <div style="width: 100%; display: flex; align-items: center; gap: 10px;">
              <el-input style="flex: 1;" v-model="userForm.nickName" placeholder="请输入昵称"></el-input>
              <el-button type="primary" @click="saveUserInfo" :loading="loading">保存</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      
      <div class="logout-section">
        <el-button size="large" plain type="danger" @click="handleLogout">退出登录</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, defineEmits, defineProps } from 'vue';
import { useStore } from 'vuex';
import { ElMessage, ElMessageBox } from 'element-plus';
import { editUserName, editUserAvatar } from '@/api/user';
import { EditPen } from '@element-plus/icons-vue';
import defaultAvatar from '@/assets/avatar.png';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'login-success', 'logout']);

// 控制对话框显示
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

const store = useStore();
const loading = ref(false);
const uploadLoading = ref(false);

// 从store获取用户信息
const userInfo = computed(() => store.state.user.userInfo || {});
const isLoggedIn = computed(() => store.getters.isLoggedIn);

// 表单数据
const userForm = reactive({
  nickName: ''
});

// 监听登录状态变化，更新表单数据
watch(isLoggedIn, (newVal) => {
  if (newVal) {
    userForm.nickName = userInfo.value.nickName || '';
  } else {
    userForm.nickName = '';
  }
}, { immediate: true });

// 在组件挂载时初始化表单数据
onMounted(() => {
  if (isLoggedIn.value) {
    userForm.nickName = userInfo.value.nickName || '';
  }
});

// 表单引用
const formRef = ref(null);

// 保存用户信息
const saveUserInfo = async () => {
  loading.value = true;
  try {
    const res = await editUserName({ nickName: userForm.nickName });
    if (res.code === 200) {
      store.dispatch('user/refreshUserInfo');
      ElMessage.success('保存成功');
    }
  } catch (error) {
    console.error('保存失败', error);
  } finally {
    loading.value = false;
  }
};

// 自定义上传
const uploadAvatar = async (options) => {
  const file = options.file;
  uploadLoading.value = true;
  
  try {
    // 创建FormData对象，直接传递文件
    const formData = new FormData();
    formData.append('file', file);
    
    // 调用API修改头像
    const res = await editUserAvatar(formData);
    if (res.code === 200) {
      // 刷新用户信息
      store.dispatch('user/refreshUserInfo');
      ElMessage.success('头像更新成功');
    }
  } catch (error) {
    console.error('头像上传失败', error);
  } finally {
    uploadLoading.value = false;
  }
};

// 退出登录
const handleLogout = () => {
  emit('logout');
};

const goToLogin = () => {
  dialogVisible.value = false;
  emit('login-success');
};

const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.profile-dialog :deep(.el-dialog__header) {
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.profile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.user-avatar {
  border: 2px solid #eee;
}

.avatar-uploader .el-button {
  margin-top: 5px;
}

.info-section {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

.info-section .el-form-item:last-child {
  margin-bottom: 0;
  text-align: center;
}

.logout-section {
  width: 100%;
  text-align: center;
}
</style>
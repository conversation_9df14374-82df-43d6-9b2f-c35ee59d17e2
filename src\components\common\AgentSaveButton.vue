<template>
  <div class="save-button-container">
    <el-button
      :type="saving ? 'warning' : 'primary'"
      class="save-button"
      @click="saveConversation"
      :disabled="!canSave || saving || alreadySaved"
      :title="!canSave ? (alreadySaved ? '已保存' : '无内容可保存') : (saving ? '保存中...' : '保存对话')"
      size="large"
    >
      <el-icon v-if="saving" class="is-loading"><Loading /></el-icon>
      <el-icon v-else-if="alreadySaved" class="saved-icon"><Check /></el-icon>
      <el-icon v-else class="save-icon"><Star /></el-icon>
      {{ saving ? '保存中' : (alreadySaved ? '已保存' : '保存对话') }}
    </el-button>
  </div>
</template>

<script>
import { ref, defineComponent, computed } from 'vue'
import { Upload, Loading, Star, Check } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

export default defineComponent({
  name: 'AgentSaveButton',
  components: {
    Upload,
    Loading,
    Star,
    Check
  },
  props: {
    category: {
      type: String,
      required: true
    },
    userQuestion: {
      type: String,
      default: ''
    },
    modelAnswer: {
      type: String,
      default: ''
    },
    chatService: {
      type: Object,
      required: true
    },
    alreadySaved: {
      type: Boolean,
      default: false
    }
  },
  emits: ['save-success', 'save-error'],
  setup(props, { emit }) {
    const saving = ref(false)

    // 是否可以保存（有问题和回答，且未保存过）
    const canSave = computed(() => {
      return !!props.userQuestion && !!props.modelAnswer && !props.alreadySaved
    })

    // 保存对话
    const saveConversation = async () => {
      if (!canSave.value) {
        ElMessage.warning('无内容可保存')
        return
      }

      saving.value = true
      try {
        const result = await props.chatService.saveMessages(
          props.userQuestion,
          props.modelAnswer
        )

        if (result) {
          ElMessage.success('对话保存成功')
          emit('save-success')
        } else {
          emit('save-error')
        }
      } catch (error) {
        console.error('保存对话错误:', error)
        emit('save-error')
      } finally {
        saving.value = false
      }
    }

    return {
      saving,
      canSave,
      saveConversation
    }
  }
})
</script>

<style scoped>
.save-button-container {
  display: inline-flex;
  align-items: center;
}

.save-button {
  background-color: #4c85f2;
  border-color: #4c85f2;
  color: white;
  border-radius: 22px;
  transition: all 0.3s;
  margin-left: 12px;
  font-weight: 600;
  padding: 12px 24px;
  height: auto;
}

.save-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 150, 0.2);
  background-color: #3a75e0;
  border-color: #3a75e0;
}

.save-button:active:not(:disabled) {
  transform: translateY(0);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.save-icon, .saved-icon {
  margin-right: 8px;
  font-size: 16px;
  vertical-align: middle;
}

.saved-icon {
  color: #67C23A; /* Element Plus success color */
}

.is-loading {
  animation: rotating 2s linear infinite;
  margin-right: 8px;
  font-size: 16px;
  vertical-align: middle;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .save-button {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 18px;
  }

  .save-icon, .is-loading {
    margin-right: 6px;
    font-size: 14px;
  }
}
</style>
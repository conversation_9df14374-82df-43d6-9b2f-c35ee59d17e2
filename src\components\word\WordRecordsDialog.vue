<template>
  <el-dialog
    v-model="dialogVisible"
    title="我的单词记录"
    width="800px"
    top="10vh"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="records-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="单词跟读记录" name="read">
          <div class="table-container">
            <el-table
              v-loading="readLoading"
              class="table-list"
              :data="readRecords"
              style="width: 100%"
              border
            >
            <el-table-column
              prop="wordCount"
              label="单词章节"
              align="center"
              min-width="200px"
            >
            <template #default="scope">
              <p>{{ scope.row.gradeName }} {{ scope.row.semesterName }} {{ scope.row.periodName }}</p>
            </template>
            </el-table-column>
            <el-table-column
              prop="wordCount"
              label="单词数"
              align="center"
              width="100px"
            />
            <el-table-column
              prop="operTime"
              label="保存时间"
              align="center"
              min-width="120px"
            />
            <el-table-column
              label="操作"
              width="150"
              align="center"
            >
              <template #default="scope">
                <el-link
                  type="primary"
                  @click="showWordDetails(scope.row)"
                >
                  详情
                </el-link>
              </template>
            </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="单词听写记录" name="write">
          <div class="table-container">
            <el-table
              v-loading="writeLoading"
              class="table-list"
              :data="writeRecords"
              style="width: 100%"
              border
            >
            <el-table-column
              prop="wordCount"
              label="单词章节"
              align="center"
              min-width="200px"
            >
            <template #default="scope">
              <p>{{ scope.row.gradeName }} {{ scope.row.semesterName }} {{ scope.row.periodName }}</p>
            </template>
            </el-table-column>
              <el-table-column
                prop="wordCount"
                label="单词数"
                align="center"
                width="100px"
              />
              <el-table-column
                prop="operTime"
                label="保存时间"
                align="center"
                min-width="120px"
              />
              <el-table-column
                label="操作"
                width="150"
                align="center"
              >
                <template #default="scope">
                  <el-link
                    type="primary"
                    @click="showWordDetails(scope.row)"
                  >
                    详情
                  </el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="单词消消乐记录" name="game">
          <div class="table-container">
            <el-table
              v-loading="gameLoading"
              class="table-list"
              :data="gameRecords"
              style="width: 100%"
              border
            >
              <el-table-column
                prop="wordCount"
                label="单词章节"
                align="center"
                min-width="200px"
              >
                <template #default="scope">
                  <p>{{ scope.row.gradeName }} {{ scope.row.semesterName }} {{ scope.row.periodName }}</p>
                </template>
              </el-table-column>
              <el-table-column
                prop="wordCount"
                label="单词数"
                align="center"
                width="100px"
              />
              <el-table-column
                prop="operTime"
                label="保存时间"
                align="center"
                min-width="120px"
              />
              <el-table-column
                label="操作"
                width="150"
                align="center"
              >
                <template #default="scope">
                  <el-link
                    type="primary"
                    @click="showWordDetails(scope.row)"
                  >
                    详情
                  </el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <el-button class="dialog-exit-button" @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 单词详情弹窗 -->
  <el-dialog
    v-model="detailsVisible"
    title="单词详情"
    width="1200px"
    top="5vh"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="detailsLoading" class="word-details-container">
      <div v-if="detailWords.length > 0" class="word-modules">
        <div v-for="(words, moduleName) in groupedWords" :key="moduleName" class="module-group">
          <div class="module-header">{{ moduleName }}</div>
          <div class="word-cards">
            <div v-for="word in words" :key="word.id" class="word-card">
              <div class="word-english">{{ word.enWord }}</div>
              <!-- <div class="word-phonetic" v-if="word.ipa">{{ word.ipa }}</div> -->
              <div class="word-chinese">{{ word.zhCn }}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="!detailsLoading" class="empty-details">
        暂无单词数据
      </div>
    </div>
    <template #footer>
      <el-button class="dialog-exit-button" @click="detailsVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { ref, defineComponent, watch, onMounted, computed } from 'vue'
import { useStore } from 'vuex'
import { getDictationRecordList, getDictationRecordDetail } from '@/api/word'
import { ElLoading } from 'element-plus'

export default defineComponent({
  name: 'WordRecordsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bookId: {
      type: String,
      required: true
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const store = useStore()
    const dialogVisible = ref(props.visible)
    const userId = computed(() => store.getters.userId)

    const activeTab = ref('read')
    const readRecords = ref([])
    const writeRecords = ref([])
    const gameRecords = ref([])
    const readLoading = ref(false)
    const writeLoading = ref(false)
    const gameLoading = ref(false)

    // 单词详情相关状态
    const detailsVisible = ref(false)
    const detailsLoading = ref(false)
    const detailWords = ref([])

    // 按模块分组的单词
    const groupedWords = computed(() => {
      const groups = {}

      detailWords.value.forEach(word => {
        const moduleName = word.moduleName
        
        if (!groups[moduleName]) {
          groups[moduleName] = []
        }
        groups[moduleName].push(word)
      })

      return groups
    })

    // 获取跟读记录
    const fetchReadRecords = async () => {
      if (!userId.value) return

      readLoading.value = true
      try {
        const params = {
          bookId: props.bookId,
          type: 'read'
        }
        const response = await getDictationRecordList(userId.value, params)
        if (response && response.data) {
          readRecords.value = response.data.map(record => ({
            ...record,
            wordCount: record.wordCount || 0
          }))
        } else {
          readRecords.value = []
        }
      } catch (error) {
        console.error('获取跟读记录失败:', error)
        readRecords.value = []
      } finally {
        readLoading.value = false
      }
    }

    // 获取听写记录
    const fetchWriteRecords = async () => {
      if (!userId.value) return

      writeLoading.value = true
      try {
        const params = {
          bookId: props.bookId,
          type: 'write'
        }
        const response = await getDictationRecordList(userId.value, params)
        if (response && response.data) {
          writeRecords.value = response.data.map(record => ({
            ...record,
            wordCount: record.wordCount || 0
          }))
        } else {
          writeRecords.value = []
        }
      } catch (error) {
        console.error('获取听写记录失败:', error)
        writeRecords.value = []
      } finally {
        writeLoading.value = false
      }
    }

    // 获取游戏记录
    const fetchGameRecords = async () => {
      if (!userId.value) return

      gameLoading.value = true
      try {
        const params = {
          bookId: props.bookId,
          type: 'game'
        }
        const response = await getDictationRecordList(userId.value, params)
        if (response && response.data) {
          gameRecords.value = response.data.map(record => ({
            ...record,
            wordCount: record.wordCount || 0
          }))
        } else {
          gameRecords.value = []
        }
      } catch (error) {
        console.error('获取游戏记录失败:', error)
        gameRecords.value = []
      } finally {
        gameLoading.value = false
      }
    }

    // 监听对话框可见性变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal
      if (newVal) {
        fetchReadRecords()
        fetchWriteRecords()
        fetchGameRecords()
      }
    })

    watch(() => dialogVisible.value, (newVal) => {
      emit('update:visible', newVal)
    })

    // 显示单词记录详情
    const showWordDetails = async (record) => {
      if (!record || !record.id) {
        console.error('记录ID不存在')
        return
      }

      // 打开详情弹窗
      detailsVisible.value = true
      detailsLoading.value = true
      detailWords.value = []

      try {
        // 调用API获取单词详情
        const response = await getDictationRecordDetail(record.id)
        if (response && response.data) {
          detailWords.value = response.data
        } else {
          detailWords.value = []
        }
      } catch (error) {
        console.error('获取单词详情失败:', error)
        detailWords.value = []
      } finally {
        detailsLoading.value = false
      }
    }

    return {
      dialogVisible,
      activeTab,
      readRecords,
      writeRecords,
      gameRecords,
      readLoading,
      writeLoading,
      gameLoading,
      showWordDetails,
      detailsVisible,
      detailsLoading,
      detailWords,
      groupedWords
    }
  }
})
</script>

<style scoped lang="scss">
.records-container {
  padding: 0 1rem;
}

.table-container {
  margin-top: 1rem;
  .table-list {
    max-height: 600px;
    overflow-y: auto;
  }
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 2rem 0;
  font-size: 1.1rem;
}

.word-details-container {
  min-height: 300px;
  padding: 1rem 0;
}

.word-modules {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.module-group {
  border: 1px solid #ebeef5;
  border-radius: 0.8rem;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.module-header {
  background-color: #f0f7ff;
  padding: 0.8rem 1.2rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #409eff;
  border-bottom: 1px solid #e0e9f6;
}

.word-cards {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.8rem;
  padding: 1rem;
  background-color: #fff;
}

.word-card {
  background-color: #fff;
  border-radius: 0.6rem;
  padding: 0.8rem;
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.word-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.word-english {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
  color: #333;
  text-align: center;
}

.word-phonetic {
  font-size: 0.85rem;
  color: #606266;
  font-style: italic;
  margin-bottom: 0.3rem;
  text-align: center;
}

.word-chinese {
  font-size: 1rem;
  color: #666;
  text-align: center;
}

.empty-details {
  text-align: center;
  color: #909399;
  padding: 4rem 0;
  font-size: 1.2rem;
}

@media (max-width: 1600px) {
  .word-cards {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1200px) {
  .word-cards {
    grid-template-columns: repeat(3, 1fr);
  }

  .module-header {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .word-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .module-header {
    font-size: 1rem;
    padding: 0.6rem 1rem;
  }
}
</style>

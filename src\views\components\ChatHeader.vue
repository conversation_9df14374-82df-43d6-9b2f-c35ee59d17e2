<script setup lang="ts">

</script>

<template>
  <div class="header">
    <div class="digitalHumans p-2">
      <el-image round src="/src/assets/aiUser/liqingzhao.png" class="avatar" alt="柳宗元"></el-image>
      <div class="name-box">
        <span class="name">李清照</span>
      </div>
    </div>
    <div class="operation">
      <!--      静音-->
      <div class="item">
        <wd-icon name="sound" color="#ffffff" size="1.5rem"></wd-icon>
      </div>
      <!--      历史对话-->
      <div class="item">
        <wd-icon name="history" color="#ffffff" size="1.5rem"></wd-icon>
      </div>
      <!--      新建对话-->
      <div class="item">
        <wd-icon name="add" color="#ffffff" size="1.5rem"></wd-icon>
      </div>

    </div>
  </div>
</template>

<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  padding: 10px;
  justify-content: space-between;
  background-color: rgba(0, 0, 0, 0.5);

  .digitalHumans {
    display: flex;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 60px;

    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
    }

    .name-box {
      margin-left: 10px;
      display: flex;
      align-items: center;


      .name {
        font-size: 14px;
        font-weight: bold;
        color: #fff;
      }


    }
  }
  .operation{
    display: flex;
    align-content: center;
    .item{
      padding: 0 10px;
    }

  }


}
</style>

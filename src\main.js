import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import router from './router'
import store from './store'
import App from './App.vue'
import './style.css'
import TrackingPlugin from './plugins/tracking/trackingPlugin.js'
import { registerDirectives } from './utils/directives.js'

const app = createApp(App)

app.use(ElementPlus, {
  locale: zhCn
})
app.use(router)
app.use(store)
app.use(TrackingPlugin, )

// 注册全局指令
registerDirectives(app)

// 应用启动时立即加载应用数据
store.dispatch('app/loadAppData');

app.mount('#app')

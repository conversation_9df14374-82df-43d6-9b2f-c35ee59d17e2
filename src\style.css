* {
  box-sizing: border-box;
}

:root {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #304156;
  background-color: #f5f5f5;
}

html, body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

#app {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  width: 100%;
}

a {
  color: #409eff;
  text-decoration: none;
}

a:hover {
  color: #66b1ff;
}


/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式隐藏滚动条 */
@media (max-width: 768px) {
  .content-wrapper {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .content-wrapper::-webkit-scrollbar {
    display: none;
  }
}

.el-input__inner {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.el-textarea__inner {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.el-button--large {
  font-size: 16px;
  font-weight: 600;
}

.el-button--large span{
  font-size: 16px;
  font-weight: 600;
}


.el-form .el-form-item__label {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #333 !important;
}

.el-image-viewer__wrapper  .el-image-viewer__close{
  font-size: 50px !important;
  top: unset !important;
  right: 40px !important;
  bottom: 40px !important;
  width: 130px !important;
  height: 65px !important;
  border-radius: 30px !important;
}
.el-image-viewer__wrapper  .el-image-viewer__close::after{
  content: "关闭" !important;
  font-size: 25px !important;
  font-weight: bold !important;
  color: #FFF !important;
}

.el-image-viewer__close .el-icon {
  display: none !important;
}


.el-dialog  .el-dialog__close {
  font-size: 24px !important;
}

.dialog-exit-button {
  background: #909399;
  font-size: 16px !important;
  color: #FFF !important;
}

.dialog-exit-button:hover {
  background: #909399 !important;
  border-color: #909399 !important;
}

.el-radio .el-radio__label {
  letter-spacing: 1px;
}
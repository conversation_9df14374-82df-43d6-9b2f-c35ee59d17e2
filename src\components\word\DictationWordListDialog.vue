<template>
  <el-dialog
    v-model="dialogVisible"
    width="100%"
    :close-on-click-modal="false"
    custom-class="word-list-dialog"
    top="0"
    fullscreen
    destroy-on-close
    :show-close="false"
  >
    <template #header>
      <div class="custom-dialog-header">
        <div class="dialog-title">听写单词列表</div>
      </div>
    </template>

    <div class="word-list-container">
      <div class="word-list-info">
        <div class="info-item">
          <span class="info-label">播报模式：</span>
          <span class="info-value">{{ playbackMode === 'random' ? '随机播报' : '顺序播报' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">单词数量：</span>
          <span class="info-value">{{ words.length }}</span>
        </div>
      </div>

      <div class="word-grid">
        <div v-for="(word, index) in words" :key="word.id" class="word-item">
          <div class="word-number">{{ index + 1 }}</div>
          <div class="word-card">
            <div class="word-english">{{ word.english }}</div>
            <div class="word-chinese">{{ word.chinese }}</div>
            <div class="word-phonetic" v-if="word.phonetic">{{ word.phonetic }}</div>
          </div>
        </div>
      </div>

      <!-- 底部控制栏 -->
      <div class="bottom-controls">
        <div class="control-buttons">
          <el-button
            size="large"
            @click="handleClose"
            class="close-button"
            round
          >
            关闭
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, defineComponent, watch, onMounted, onBeforeUnmount } from 'vue'

export default defineComponent({
  name: 'DictationWordListDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    words: {
      type: Array,
      required: true
    },
    playbackMode: {
      type: String,
      default: 'order'
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const dialogVisible = ref(props.visible)

    // 处理浏览器回退按钮
    const handlePopState = (event) => {
      if (dialogVisible.value) {
        // 如果对话框是打开的，执行关闭操作
        handleClose()

        // 阻止默认的回退行为，因为我们已经处理了关闭操作
        event.preventDefault()
        return false
      }
    }

    // 监听对话框可见性变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal

      if (newVal) {
        // 添加历史记录状态，用于处理浏览器回退
        window.history.pushState({ wordListDialog: true }, '')

        // 添加popstate事件监听器
        window.addEventListener('popstate', handlePopState)
      } else {
        // 对话框关闭时，移除事件监听器
        window.removeEventListener('popstate', handlePopState)
      }
    })

    watch(() => dialogVisible.value, (newVal) => {
      emit('update:visible', newVal)
    })

    const handleClose = () => {
      // 移除事件监听器
      window.removeEventListener('popstate', handlePopState)

      dialogVisible.value = false
      emit('update:visible', false)
    }

    // 组件挂载时初始化
    onMounted(() => {
      if (dialogVisible.value) {
        // 添加历史记录状态，用于处理浏览器回退
        window.history.pushState({ wordListDialog: true }, '')

        // 添加popstate事件监听器
        window.addEventListener('popstate', handlePopState)
      }
    })

    // 组件卸载前清理
    onBeforeUnmount(() => {
      // 确保移除事件监听器
      window.removeEventListener('popstate', handlePopState)
    })

    return {
      dialogVisible,
      handleClose
    }
  }
})
</script>

<style scoped>
/* 单词列表对话框样式 */
.word-list-dialog {
  display: flex;
  flex-direction: column;
}

.word-list-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 6rem);
  width: 100%;
  box-sizing: border-box;
}

.word-list-info {
  display: flex;
  gap: 3rem;
  margin-bottom: 2rem;
  margin-top: 1rem;
  margin-left: 1rem;
  margin-right: 1rem;
  padding: 1rem 2rem;
  background-color: #f0f7ff;
  border-radius: 0.8rem;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
}

.info-value {
  font-size: 1.4rem;
  color: #409eff;
  font-weight: 600;
}

.word-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  overflow-y: auto;
  padding: 1rem;
  flex: 1;
  margin-bottom: 0;
  align-content: start;
}

.word-item {
  position: relative;
  border-radius: 0.8rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 140px;
  min-height: 140px;
  max-height: 140px;
}

.word-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.15);
}

.word-number {
  position: absolute;
  top: 0.8rem;
  left: 0.8rem;
  background-color: #409eff;
  color: white;
  width: 2.4rem;
  height: 2.4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  z-index: 1;
}

.word-card {
  background-color: white;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  height: 100%;
  border: 1px solid #e0e9f6;
  border-radius: 0.8rem;
  box-sizing: border-box;
}

.word-english {
  font-size: 1.6rem;
  font-weight: 600;
  color: #333;
  text-align: center;
  line-height: 1.2;
  word-break: break-word;
}

.word-chinese {
  font-size: 1.2rem;
  color: #606266;
  text-align: center;
  line-height: 1.2;
}

.word-phonetic {
  font-size: 1rem;
  color: #909399;
  font-style: italic;
  text-align: center;
  line-height: 1.2;
}

/* 底部控制栏 */
.bottom-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2rem;
  padding: 1rem;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.close-button {
  padding: 2rem 2rem !important;
  height: 3.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  background-color: #909399;
  border-color: #909399;
  color: white;
}

:deep(.close-button span){
  font-size: 1.5rem;
}

.close-button:hover {
  background-color: #7d7d7d;
  border-color: #7d7d7d;
  color: white;
}

/* Custom dialog header styles */
.custom-dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* Override Element Plus dialog styles */
:deep(.el-dialog) {
  margin: 0 !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: auto;
  padding: 0;
}

/* Responsive design for different screen sizes */
@media (max-width: 1200px) {
  .word-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 992px) {
  .word-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }
  
  .bottom-controls {
    gap: 2rem;
    padding: 1.2rem 1.5rem;
  }
  
  .word-item {
    height: 120px;
    min-height: 120px;
    max-height: 120px;
  }
  
  .word-english {
    font-size: 1.4rem;
  }

  .word-chinese {
    font-size: 1.1rem;
  }
  
  .word-phonetic {
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .dialog-title {
    font-size: 2.2rem;
  }

  .word-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.8rem;
  }
  
  .word-item {
    height: 100px;
    min-height: 100px;
    max-height: 100px;
  }

  .word-english {
    font-size: 1.2rem;
  }

  .word-chinese {
    font-size: 1rem;
  }
  
  .word-phonetic {
    font-size: 0.8rem;
  }
  
  .bottom-controls {
    padding: 1rem;
  }
  
  .close-button {
    padding: 0 1.5rem !important;
    height: 3rem;
    font-size: 1rem;
  }
  
  :deep(.close-button span){
    font-size: 1rem;
  }
  
  .word-card {
    padding: 1rem;
    gap: 0.3rem;
  }
}
</style>

<script setup lang="ts">
import {ref,defineProps, onMounted} from 'vue';
const props = defineProps({
  text: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <div class="voice-recognition-display" v-if="props.text">
    <div class="recognition-content">
      <div class="voice-indicator">
        <div class="pulse-ring"></div>
                 <div class="voice-icon">
           <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
             <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
           </svg>
         </div>
      </div>
      <div class="recognition-text">
         {{ props.text }}
       </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.voice-recognition-display {
  position: fixed;
  bottom: 280px; /* 调整到语音按钮上方 */
  right: 20px;
  z-index: 100; /* 提高层级确保不被遮挡 */
  max-width: 320px;
  min-width: 200px;
  animation: slideInFromRight 0.3s ease-out;
}

.recognition-content {
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.85) 0%, 
    rgba(20, 20, 30, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 16px 20px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(59, 130, 246, 0.2);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  overflow: hidden;

  /* 微光效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(59, 130, 246, 0.1), 
      transparent);
    animation: shimmer 2s infinite;
  }
}

.voice-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #56d8e4 0%, #9f01ea 100%);
  flex-shrink: 0;
  margin-top: 2px;
}

.pulse-ring {
  position: absolute;
  width: 48px;
  height: 48px;
  border: 2px solid rgba(159, 1, 234, 0.6);
  border-radius: 50%;
  animation: pulse 1.5s ease-out infinite;
}

.voice-icon {
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  
  svg {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }
}

.recognition-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.5;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  word-wrap: break-word;
  font-weight: 500;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 1;
  
  /* 文字动画效果 */
  animation: fadeInText 0.5s ease-out 0.2s both;
}

/* 动画效果 */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes fadeInText {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voice-recognition-display {
    right: 16px;
    bottom: 140px; /* 移动端也要调整位置 */
    max-width: 280px;
    min-width: 180px;
  }
  
  .recognition-content {
    padding: 12px 16px;
  }
  
  .recognition-text {
    font-size: 13px;
  }
}

/* 暗色主题优化 */
@media (prefers-color-scheme: dark) {
  .recognition-content {
    background: linear-gradient(135deg, 
      rgba(0, 0, 0, 0.9) 0%, 
      rgba(10, 10, 20, 0.95) 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }
}
</style>

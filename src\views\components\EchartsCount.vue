<template>
  <el-row style="width:100%;">
    <el-col>
      <el-card shadow="never" style="border: none">
        <template #header>
          <div class="card-header">
            <!--            按月查询 -->
            <el-date-picker v-model="month" @change="changeMonth" value-format="YYYY-MM" type="month"
                            placeholder="选择月"
                            style="width: 200px;" :disabled-date="disabledDate"></el-date-picker>
            <el-button type="primary" style="margin-left: 20px;" @click="changeMonth">刷新</el-button>
          </div>
        </template>
        <div ref="chartContainer" style="width: 100%; height: 300px;"></div>
        <div ref="chartContainerClass" style="width: 100%; height: 600px;margin-top: 50px"></div>

      </el-card>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import {ref, onMounted, onBeforeUnmount} from 'vue'
import * as echarts from 'echarts'
import {countLogDay, countLogClass} from '@/api/verse'
import {formatDate} from '@/utils/tools'

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}
// 定义组件名称（Vue Devtools 调试时可见）

const chartContainer = ref<HTMLDivElement | null>(null)
const chartContainerClass = ref<HTMLDivElement | null>(null)
let chartInstance = null
let chartInstanceM = null

const month = ref(formatDate(new Date(), 'YYYY-MM'))
const initChart = async () => {
  if (!chartContainer.value) return
  console.log(formatDate(new Date(), 'YYYY-MM'))
  const res = await countLogDay({month: month.value})
  const data = res.data || []

  chartInstance = echarts.init(chartContainer.value)

  const option: echarts.EChartsOption = {
    title: {
      text: '总点击量'
    },
    grid: {
      left: '3%',
      right: '2%',
      bottom: '7%',
    },
    tooltip: {},
    xAxis: {
      data: data.map(a => a.date.slice(5, 10))
    },
    yAxis: {

      minInterval:1
    },
    series: [
      {
        name: '点击量',
        type: 'line',
        data: data.map(a => a.count)
      }
    ]
  }

  chartInstance.setOption(option)

}
const initChartClass = async () => {
  if (!chartContainerClass.value) return
  const res = await countLogClass({month: month.value})
  const data = res.data || []
  chartInstanceM = echarts.init(chartContainerClass.value)
  const option: echarts.EChartsOption = {
    title: {
      text: '智能体点击量',
      top: '0%'
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      backgroundColor: 'rgba(50,50,50,0.8)',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      type: 'scroll',
      data: data.legend,
      left: 'center',
      top: '8%',
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      textStyle: {
        fontSize: 12
      },
      pageButtonItemGap: 5,
      pageButtonGap: 10,
      pageIconSize: 12
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        bottom: '5%',
        start: 0,
        end: 100,
        height: 20
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        start: 0,
        end: 100
      }
    ],
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.xaxis.map(a => a.slice(5, 10)),
      axisLabel: {
        fontSize: 11,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      axisLabel: {
        fontSize: 11
      }
    },
    series: (data.series || []).map((a, i) => {
      return {
        name: data.legend[i],
        type: 'line',
        data: a.data,
        lineStyle: {
          width: 2
        },
        symbol: 'circle',
        symbolSize: 4,
        emphasis: {
          focus: 'series'
        }
      }
    })
  }

  chartInstanceM.setOption(option)
}
const changeMonth = async () => {
  await initChart()
  await initChartClass()
  chartInstanceM?.resize()
  chartInstance?.resize()
}
onMounted(() => {
  changeMonth()
  window.onresize = () => {
    console.log('====')
    chartInstanceM?.resize()
    chartInstance?.resize()
  }

})

onBeforeUnmount(() => {
})
</script>

<style scoped>
/* 根据需要添加样式 */
</style>

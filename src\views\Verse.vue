<template>
  <div class="verse-container" ref="containerRef">
    <div class="header">
      <h1 class="title">AI古诗词</h1>
      <p class="subtitle">基于中国传统诗词文化，快速检索经典诗词作品，按作者、朝代、主题等多维度智能筛选</p>
      
      <div class="page-navigation">
        <el-button type="primary" class="navigate-button" @click="switchToQuestionPage"  size="large">
          前往古诗词出题助手
        </el-button>
      </div>
    </div>
    
    <div class="form-container">
      <el-form :model="formData" label-width="120px"  class="verse-form" size="large">
        <el-form-item label="朝代">
          <div class="tag-buttons-container">
            <el-button 
              :type="formData.dynasty === '不限' ? 'primary' : ''" 
              @click="selectOption('dynasty', '不限')"
              round
            >不限</el-button>
            <el-button 
              v-for="dynasty in verseFilter.dynasties" 
              :key="dynasty"
              :type="formData.dynasty === dynasty ? 'primary' : ''" 
              @click="selectOption('dynasty', dynasty)"
              round
            >{{ dynasty }}</el-button>
          </div>
        </el-form-item>
        <el-form-item label="作者">
          <div class="tag-buttons-container">
            <el-button 
              :type="formData.author === '不限' ? 'primary' : ''" 
              @click="selectOption('author', '不限')"
              round
            >不限</el-button>
            <el-button 
              v-for="author in verseFilter.authors" 
              :key="author"
              :type="formData.author === author ? 'primary' : ''" 
              @click="selectOption('author', author)"
              round
            >{{ author }}</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="主题">
          <div class="tag-buttons-container">
            <el-button 
              :type="formData.theme === '不限' ? 'primary' : ''" 
              @click="selectOption('theme', '不限')"
              round
            >不限</el-button>
            <el-button 
              v-for="theme in verseFilter.themes" 
              :key="theme"
              :type="formData.theme === theme ? 'primary' : ''" 
              @click="selectOption('theme', theme)"
              round
            >{{ theme }}</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="学段/年级">
          <div class="tag-buttons-container">
            <el-button
              :type="formData.period === '不限' ? 'primary' : 'default'"
              @click="selectOption('period', '不限')"
              round
            >
              不限
            </el-button>
            <el-button
              v-for="item in verseFilter.periodList"
              :key="item.value"
              :type="formData.period === item.value ? 'primary' : 'default'"
              @click="selectOption('period', item.value)"
              round
            >
              {{ item.label }}
            </el-button>
            <template v-if="formData.period !== '不限'">
              <el-button
                v-for="grade in filteredGrades"
                :key="grade"
                :type="formData.grade === grade ? 'primary' : 'default'"
                @click="selectOption('grade', grade)"
                round
              >
                {{ grade }}
              </el-button>
              <el-button 
              v-for="term in verseFilter.terms" 
              :key="term"
              :type="formData.term === term ? 'primary' : ''" 
              @click="selectOption('term', term)"
              round
            >{{ term }}</el-button>
            </template>            
          </div>
        </el-form-item>

        <div class="search-container">
            <el-input
              v-model="formData.keyword"
              placeholder="输入关键词搜索诗词..."
              class="search-input"
              clearable
              :prefix-icon="Search"
            >
            </el-input>
            <el-button 
              type="primary" 
              @click="handleSearch" 
              class="search-button"
              round
            >
              搜索
            </el-button>
          </div>
      </el-form>
    </div>
    
    <div class="result-container">
      <div v-if="verseList.length === 0" class="empty-result">
        <p>暂无筛选结果</p>
      </div>
      
      <div v-else class="verse-card-container">
        <div v-for="verse in verseList" :key="verse.id" class="verse-card" @click="showVerseDetail(verse)">
          <div class="verse-header">
            <h3 class="verse-title">{{ verse.name }}</h3>
            <div class="card-actions">
              <el-tooltip content="复制" placement="top">
                <el-icon class="copy-icon" @click.stop="copyVerseContent(verse.name, verse.author, formatPoemContent(verse))">
                  <CopyDocument />
                </el-icon>
              </el-tooltip>
            </div>
          </div>
          <div class="verse-author">{{ verse.dynasty }}·{{ verse.author }} 
            <span v-if="verse.tags" class="tag-dynasty">{{ verse.tags.replace(',', ' ') }}</span>
          </div>
  
          <div class="verse-content">
            <div v-if="verse.poemLine" class="poem-lines">
              <p v-for="(line, index) in verse.poemLine.split('\\n').filter(line => line.trim())" :key="index" class="poem-line">
                {{ line.trim() }}
              </p>
            </div>
            <p v-else>{{ formatPoemContent(verse) }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 古诗详情弹窗 -->
    <VerseDetailDialog 
      :visible="detailDialogVisible"
      :verse-detail="selectedVerseDetail"
      @close="closeDetailDialog"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick,inject } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { CopyDocument, Search } from '@element-plus/icons-vue'
import { getVerseFilter, getVerseList, getVerseDetail, getAiTalkDetail } from '@/api/verse'
import VerseDetailDialog from '@/components/verse/VerseDetailDialog.vue'

export default {
  name: 'Verse',
  components: {
    CopyDocument,
    Search,
    VerseDetailDialog
  },
  emits: ['switch-component'],
  setup(props, { emit }) {
    const trackingService = inject('trackingService');
    const formData = ref({
      author: '不限',
      dynasty: '不限',
      theme: '不限',
      period: '不限',
      grade: '不限',
      term: '',
      keyword: ''
    })

    const containerRef = ref(null)

    // 古诗筛选条件
    const verseFilter = ref({
      dynasties: [],
      authors: [],
      authorDynasty: [],
      themes: [],
      periods: [],
      gradeMap: new Map(),
      terms: [],
      periodList: []
    })

    // 古诗列表
    const verseList = ref([])

    // 加载状态
    const loading = ref(false)

    // 古诗详情弹窗
    const detailDialogVisible = ref(false)
    const selectedVerseDetail = ref(null)

    // 获取筛选条件
    const fetchFilters = async () => {
      try {
        const loadingInstance = ElLoading.service({
          target: '.form-container',
          text: '加载筛选项...'
        })
        
        const res = await getVerseFilter()
        if (res && res.code === 200) {
          const filterData = res.data || []
          let periods = []
          
          // 处理筛选数据
          filterData.forEach(item => {
            switch(item.type) {
              case 'dynasty':
                verseFilter.value.dynasties = item.options.map(opt => opt.text)
                break;
              case 'author':
                verseFilter.value.authors = item.options.map(opt => opt.text)
                verseFilter.value.authorDynasty = item.options
                break;
              case 'theme':
                verseFilter.value.themes = item.options
                break;
              case 'period':
                // 处理学段和年级
                const gradeMap = new Map()
                
                // 提取小学和初中
                item.options.forEach(opt => {
                  if (opt.text !== '不限') {
                    periods.push(opt.text)
                    
                    // 提取各学段下的年级
                    if (opt.options) {
                      const grades = opt.options
                        .filter(grade => grade.text !== '不限')
                        .map(grade => grade.text)
                      gradeMap.set(opt.text, grades)
                    }
                  }
                })
                
                verseFilter.value.periods = periods
                verseFilter.value.gradeMap = gradeMap
                break;
              case 'gradeStage':
                verseFilter.value.terms = item.options
                  .filter(opt => opt.text !== '不限')
                  .map(opt => opt.text)
                break;
            }
          })
          
          // 构建 periodList
          verseFilter.value.periodList = periods.map(period => ({
            value: period,
            label: period
          }))
        }
        
        loadingInstance.close()
      } catch (error) {
        console.error('获取筛选条件错误:', error)
      }
    }
    
    // 根据当前选择的学段筛选年级
    const filteredGrades = computed(() => {
      if (formData.value.period === '不限') {
        return []
      }
      return verseFilter.value.gradeMap?.get(formData.value.period) || []
    })

    // 搜索处理函数
    const handleSearch = async () => {
      try {
        loading.value = true
        const loadingInstance = ElLoading.service({
          target: '.result-container',
          text: '搜索诗词中...'
        })
        
        // 构建查询参数
        const params = {
          searchValue: formData.value.keyword
        }
        
        if (formData.value.dynasty !== '不限') {
          params.dynasty = formData.value.dynasty
        }
        
        // 只添加非"不限"的筛选条件
        if (formData.value.author !== '不限') {
          params.author = formData.value.author
        }
        
        if (formData.value.theme !== '不限') {
          params.tags = formData.value.theme
        }
        
        if (formData.value.period !== '不限') {
          params.period = formData.value.period
        }
        
        if (formData.value.grade !== '不限') {
          params.grade = formData.value.grade
        }
        
        if (formData.value.term !== '不限') {
          params.term = formData.value.term
        }
        
        const res = await getVerseList(params)
        console.log('res==', res)

        if (res && res.code === 200) {
          verseList.value = res.rows || []
        }
        
        loadingInstance.close()
      } catch (error) {
        console.error('搜索诗词错误:', error)
      } finally {
        loading.value = false
      }
      trackingService.trackEvent('PoetAI')
    }
    

    
    // 格式化诗词内容（用于复制）
    const formatPoemContent = (verse) => {
      if (verse.poemLine) {
        // 新的简化格式，直接处理字符串
        return verse.poemLine.replace(/\\n/g, '\n').trim()
      }
      
      // 回退到使用body字段，分解诗句
      if (verse.body) {
        const parts = verse.body.split('\\n')
        if (parts.length > 1) {
          // 通常第一部分是标题和作者信息，跳过
          return parts[1] || ''
        }
        return verse.body
      }
      
      return ''
    }
    
    // 复制诗词内容
    const copyVerseContent = (title, author, content) => {
      const fullContent = `《${title}》\n${author}\n${content}`
      
      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = fullContent
      document.body.appendChild(textarea)
      textarea.select()
      
      try {
        // 尝试复制到剪贴板
        document.execCommand('copy')
        ElMessage.success('诗词已复制到剪贴板')
      } catch (err) {
        ElMessage.error('复制失败，请手动复制')
        console.error('复制失败:', err)
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }
    

    
    // 选择筛选选项并触发搜索
    const selectOption = (field, value) => {
      formData.value[field] = value
      
      if (field === 'dynasty') {
        if (value === '不限') {
          verseFilter.value.authors = verseFilter.value.authorDynasty.map(opt => opt.text)
        } else {
          verseFilter.value.authors = verseFilter.value.authorDynasty.filter((a) => a.label === value).map(opt => opt.text)
        }
      }

      // 当切换学段时处理年级选择
      if (field === 'period') {
        if (value === '不限') {
          // 如果选择了"不限"，则清空年级值
          formData.value.grade = '不限'
          // 同时清空学期
          formData.value.term = ''
        } else {
          // 如果选择了特定学段且有对应年级，则默认选中第一个年级
          const grades = verseFilter.value.gradeMap?.get(value) || []
          if (grades.length > 0) {
            formData.value.grade = grades[0]
          } else {
            formData.value.grade = '不限'
          }
          // 清空学期
          formData.value.term = ''
        }
      }
      
      // 当切换年级时重置学期
      if (field === 'grade') {
        formData.value.term = ''
      }
      
      handleSearch()
    }
    
    // 显示古诗详情
    const showVerseDetail = async (verse) => {
      try {
          // 预加载封面图
          if (verse.coverImage) {
          const img = new Image()
          img.src = verse.coverImage
        }
        // 古诗词诗词详情
        const res = await getVerseDetail(verse.id)
        selectedVerseDetail.value = res.data || {}
        // 古诗词数字人详情
        const res2 = await getAiTalkDetail({
          category: 'gsc',
          name: selectedVerseDetail.value.author
        })
        selectedVerseDetail.value = {
          ...selectedVerseDetail.value,
          authorInfo: res2.data || {}
        }
        detailDialogVisible.value = true
      } catch (error) {
        console.error('获取古诗详情错误:', error)
      }
       trackingService.trackEvent('PoetAI')
    }

    // 关闭古诗详情弹窗
    const closeDetailDialog = () => {
      detailDialogVisible.value = false
      selectedVerseDetail.value = null
    }

    // 切换到古诗词出题助手
    const switchToQuestionPage = () => {
      console.log('切换到古诗词出题助手');
      console.log('发送事件: switch-component，参数:', 'verseQuestion');
      emit('switch-component', 'verseQuestion');
      trackingService.trackEvent('VerseIntegrated')
    }
    
    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };
    
    // 页面加载时获取筛选条件
    onMounted(() => {
      fetchFilters()
      handleSearch()
    })
    
    return {
      formData,
      containerRef,
      verseFilter,
      verseList,
      loading,
      handleSearch,
      formatPoemContent,
      copyVerseContent,
      CopyDocument,
      Search,
      selectOption,
      filteredGrades,
      switchToQuestionPage,
      scrollToBottom,
      detailDialogVisible,
      selectedVerseDetail,
      showVerseDetail,
      closeDetailDialog
    }
  }
}
</script>

<style scoped>
.verse-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "📚";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
}

.verse-form {
  width: 100%;
  margin: 0 auto;
}

.tag-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 0.5rem;
}

.tag-buttons-container .el-button {
  margin-left: 0;
  margin-right: 0;
  transition: all 0.3s;
  width: 80px;
}

.tag-buttons-container .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.search-container {
  display: flex;
  width: 90%;
  margin-left: 80px;
  margin-top: 2rem;
}

.search-input {
  flex: 1;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 24px 0 0 24px;
  box-shadow: 0 3px 10px rgba(0, 0, 150, 0.05);
  padding-left: 0.5rem;
  transition: all 0.3s;
}

.search-input :deep(.el-input__inner) {
  font-size: 1rem;
  padding: 0.8rem 1rem;
  height: 3rem;
}

.search-button {
  height: 3rem;
  padding: 0 1.5rem !important;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 0 3px 10px rgba(64, 158, 255, 0.2);
  transition: all 0.3s;
  border-radius: 0 24px 24px 0 !important;
  margin-left: -1px;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.verse-card-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.verse-card {
  background-color: #fff;
  border-radius: 0.8rem;
  padding: 1.2rem;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.verse-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.term-divider {
  width: 1px;
  height: 24px;
  background-color: #dcdfe6;
  margin: 0 0.8rem;
}

.verse-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 0.8rem;
}

.verse-title {
  font-size: 1.4rem;
  margin: 0 0 0.3rem 0;
  color: #333;
  font-weight: 600;
  text-align: center;
}

.card-actions {
  position: absolute;
  right: 0;
  top: 0;
}

.copy-icon {
  cursor: pointer;
  color: #1a1b1c;
  font-size: 1.2rem;
}

.verse-author {
  font-size: 1rem;
  color: #1a1b1c;
  margin-bottom: 1rem;
  text-align: center;
}

.tag-dynasty {
  display: inline-block;
  background-color: #f0f4ff;
  color: #4e7bcc;
  padding: 0.1rem 0.5rem;
  border-radius: 0.3rem;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.verse-info {
  margin-bottom: 0.8rem;
  font-size: 0.9rem;
  color: #606266;
}

.verse-info span {
  margin-right: 1rem;
  background-color: #f5f7fa;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.poem-lines {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.poem-line {
  margin: 0;
  line-height: 1.8;
  text-align: center;
}

.poem-text {
  font-weight: 500;
}

.poem-punc {
  color: #333;
  margin-left: 2px;
}

.verse-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
  border-radius: 0.5rem;
}

.poem-space {
  display: inline-block;
  width: 0.5em;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .verse-card-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .verse-container {
    width: 95vw;
    padding: 1rem;
  }
  
  .title {
    font-size: 2.2rem;
  }
  
  .form-container,
  .verse-card {
    padding: 1.2rem;
  }
  
  .verse-card-container {
    grid-template-columns: 1fr;
  }
}

.empty-result {
  text-align: center;
  padding: 3rem;
  color: #909399;
  font-size: 1.2rem;
}

.page-navigation {
  margin-top: 1.5rem;
}

.navigate-button {
  padding: 0.8rem 1.5rem;
  font-size: 1.1rem;
  transition: all 0.3s;
}

.navigate-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 150, 0.15);
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
  padding: 0 15px;
}

:deep(.el-form-item) {
  margin-bottom: 25px;
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}
</style>

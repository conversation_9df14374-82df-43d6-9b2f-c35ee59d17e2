import { createRouter, createWebHashHistory } from 'vue-router'
import MainLayout from '../components/layout/MainLayout.vue'
import Home from '../views/Home.vue'
import store from '@/store'

// 智能体页面组件
import Article from '@/views/Article.vue'
import CommentsIntegrated from '@/views/CommentsIntegrated.vue'
import Composition from '@/views/Composition.vue'
import DocumentExtraction from '@/views/DocumentExtraction.vue'
import LessonSummary from '@/views/LessonSummary.vue'
import MathGPT from '@/views/MathGPT.vue'
import Message from '@/views/Message.vue'
import RollCall from '@/views/RollCall.vue'
import SpeakText from '@/views/SpeakText.vue'
import Summary from '@/views/Summary.vue'
import AITalk from '@/views/AITalk.vue'
import Translate from '@/views/Translate.vue'
import VerseIntegrated from '@/views/VerseIntegrated.vue'
import Word from '@/views/Word.vue'
import Ask from '@/views/Ask.vue'
import Iframe from '@/views/Iframe.vue'
import Admin from '@/views/my/Admin.vue'

const routes = [
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home,
        meta: { title: 'AI教育工具箱' }
      },
      {
        path: 'article',
        name: 'Article',
        component: Article,
        meta: { title: '文章润色智能体' }
      },
      {
        path: 'comments-integrated',
        name: 'CommentsIntegrated',
        component: CommentsIntegrated,
        meta: { title: '学生评语智能体' }
      },
      {
        path: 'composition',
        name: 'Composition',
        component: Composition,
        meta: { title: 'AI写作文智能体' }
      },
      {
        path: 'document-extraction',
        name: 'DocumentExtraction',
        component: DocumentExtraction,
        meta: { title: '文档提炼智能体' }
      },
      {
        path: 'lesson-summary',
        name: 'LessonSummary',
        component: LessonSummary,
        meta: { title: '教案总结智能体' }
      },
      {
        path: 'math-gpt',
        name: 'MathGPT',
        component: MathGPT,
        meta: { title: 'AI解题智能体' }
      },
      {
        path: 'message',
        name: 'Message',
        component: Message,
        meta: { title: '智能通知智能体' }
      },
      {
        path: 'roll-call',
        name: 'RollCall',
        component: RollCall,
        meta: { title: '随机点名智能体' }
      },
      {
        path: 'speak-text',
        name: 'SpeakText',
        component: SpeakText,
        meta: { title: 'AI演讲稿智能体' }
      },
      {
        path: 'summary',
        name: 'Summary',
        component: Summary,
        meta: { title: '活动总结智能体' }
      },
      {
        path: 'aiTalk',
        name: 'AITalk',
        component: AITalk,
        meta: { title: 'AI数字人智能体' }
      },
      {
        path: 'translate',
        name: 'Translate',
        component: Translate,
        meta: { title: '中英互译智能体' }
      },
      {
        path: 'verse-integrated',
        name: 'VerseIntegrated',
        component: VerseIntegrated,
        meta: { title: '古诗词智能体' }
      },
      {
        path: 'word',
        name: 'Word',
        component: Word,
        meta: { title: '单词智能体' }
      },
      {
        path: 'ask',
        name: 'Ask',
        component: Ask,
        meta: { title: '问一问' }
      },
      {
        path: 'iframe',
        name: 'Iframe',
        component: Iframe,
        meta: { title: '加载中...' }
      },
      {
        path: 'admin',
        name: 'Admin',
        component: Admin,
        meta: { title: '统计数据' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

router.beforeEach(async (to, _from, next) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = `均衡AI - ${to.meta.title}`
  } else {
    document.title = '均衡AI - AI教育工具箱'
  }

  // 检查URL中是否包含token参数
  const token = to.query.token
  const os = to.query.os || ''

  if (token) {
    try {
      const decodedToken = decodeURIComponent(token)
      await store.dispatch('user/loginByThirdToken', { thirdToken: decodedToken, os })
      // 清除URL中的参数，重定向到不带参数的URL
      next({ path: to.path, replace: true })
    } catch (error) {
      console.error('第三方登录失败:', error)
      next()
    }
  } else {
    next()
  }
})

export default router
<template>
  <div v-if="visible" class="ai-loading-container">
    <div class="ai-loading-content">
      <div class="loading-dots">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
      <span class="text">AI思考中</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiLoading',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.ai-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80px;
}

.ai-loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.loading-dots {
  display: flex;
  align-items: center;
  gap: 6px;
}

.dot {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  border-radius: 50%;
  animation: wave 1.4s ease-in-out infinite;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes wave {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.text {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
}
</style> 
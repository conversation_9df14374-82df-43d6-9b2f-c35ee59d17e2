<template>
  <div class="random-container" ref="containerRef">
    <!-- 全屏Loading -->
    <div v-if="uploadLoading" class="fullscreen-loading">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <div class="loading-text">正在导入名单...</div>
        <div class="loading-subtitle">请稍候，正在处理您的文件</div>
      </div>
    </div>

    <div class="header">
      <h1 class="title">随机点名</h1>
    </div>

    <div class="form-container">
      <!-- 功能卡片区域 -->
      <div class="function-cards">

        <!-- 下载模板卡片 -->
        <div class="function-card template-card">
          <div class="card-header">
            <div class="card-icon">
              <Document />
            </div>
            <h3 class="card-title">下载模板</h3>
          </div>
          <div class="card-content">
            <p class="card-description">下载标准Excel模板文件</p>
            <div class="action-button" @click="downloadTemplate('excel')">
              <span>立即下载</span>
            </div>
          </div>
        </div>
        
        <!-- 导入名单卡片 -->
        <div class="function-card upload-card">
          <div class="card-header">
            <div class="card-icon">
              <Upload />
            </div>
            <h3 class="card-title">导入名单</h3>
          </div>
          <div class="card-content">
            <p class="card-description">上传Excel导入学生名单</p>
            <el-upload
              action="#"
              :auto-upload="false"
              :show-file-list="false"
              accept=".xlsx"
              :on-change="loadStudentList"
              class="upload-area"
            >
              <div class="action-button upload-button">
                <span>点击上传文件</span>
              </div>
            </el-upload>
          </div>
        </div>

        <!-- 导入记录卡片 -->
        <div class="function-card history-card">
          <div class="card-header">
            <div class="card-icon">
              <List />
            </div>
            <h3 class="card-title">导入记录</h3>
          </div>
          <div class="card-content">
            <p class="card-description">管理历史导入的名单记录</p>
            <div class="action-button" @click="showHistoryDialog">
              <span>查看记录</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 名单列表展示区域 -->
      <div class="student-list-section">
        <div class="list-header">
          <div class="list-title-container">
            <h3 class="list-title" v-if="students.length === 0">当前名单</h3>
            <div v-else class="list-title-dropdown" @click="toggleNameListDropdown">
              <h3 class="list-title clickable">{{ getCurrentListTitle() }}</h3>
              <el-icon class="dropdown-icon" :class="{ 'rotated': nameListDropdownVisible }">
                <ArrowDown />
              </el-icon>
              <!-- 名单切换下拉菜单 -->
              <div v-if="nameListDropdownVisible" class="name-list-dropdown">
                <div class="dropdown-header">选择名单</div>
                <div class="dropdown-content">
                  <div
                    v-for="item in historyData"
                    :key="item.id"
                    class="dropdown-item"
                    :class="{ 'active': item.id === randomId }"
                    @click.stop="handleSelectNameList(item)"
                  >
                    <div class="item-title">{{ item.title }}</div>
                    <div class="item-time">{{ item.createTime }}</div>
                  </div>
                  <div v-if="historyData.length === 0" class="dropdown-empty">
                    暂无历史名单
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="list-info" v-if="students.length > 0">
            <span class="student-count">共 {{ students.length }} 人</span>
          </div>
          <!-- 开始点名按钮移到这里 -->
          <div v-if="students.length > 0" class="roll-call-section-header">
            <button class="roll-call-button" @click="startRollCall">
              <span class="button-text">开始点名</span>
            </button>
          </div>
        </div>
        <!-- 空状态提示 -->
        <div v-if="students.length === 0" class="empty-state">
          <div class="empty-icon">📋</div>
          <h4 class="empty-title">暂无学生名单</h4>
          <p class="empty-description">请先下载模板文件，填写学生信息后导入名单</p>
        </div>
        
        <!-- 学生名单列表 -->
        <div v-if="students.length > 0" class="student-list">
          <div 
            class="student-item" 
            v-for="(student, index) in students" 
            :key="index"
            :class="{ 
              'male-student': student.sex === '1', 
              'female-student': student.sex === '2' 
            }"
          >
            <span class="student-name">{{ student.name }}</span>
          </div>
        </div>

      </div>


      <!-- 我的导入记录弹窗 -->
      <el-dialog v-model="historyDialogVisible" title="我的导入记录" width="800" :close-on-click-modal="false">
        <el-table :data="historyData" style="width: 100%; max-height: 500px;">
          <el-table-column prop="title" label="文件名称" align="center"/>
          <el-table-column prop="createTime" label="导入时间" width="180" align="center" />
          <el-table-column label="操作" align="center" width="340">
            <template #default="scope">
              <el-link type="primary" :underline="false" @click="handleSelectHistory(scope.row)" style="margin-right: 15px;">
                <el-icon style="margin-right: 5px;"><Position /></el-icon>选择此名单
              </el-link>
              <el-link type="primary" :underline="false" @click="handleEditFileName(scope.row)" style="margin-right: 15px;">
                <el-icon style="margin-right: 5px;"><Edit /></el-icon>编辑
              </el-link>
              <el-link type="danger" :underline="false" @click="handleDeleteHistory(scope.row)" style="margin-right: 15px;">
                <el-icon style="margin-right: 5px;"><Delete /></el-icon>删除
              </el-link>
              <el-link type="success" :underline="false" @click="handleViewHistory(scope.row)">
                <el-icon style="margin-right: 5px;"><Setting /></el-icon>管理名单
              </el-link>
            </template>
          </el-table-column>
        </el-table>
        <template #footer>
          <el-button class="dialog-exit-button" @click="historyDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>

      <!-- 管理名单弹窗 -->
      <el-dialog v-model="detailDialogVisible" title="管理名单" width="820" :close-on-click-modal="false">
        <div class="manage-list-container">
          <div class="manage-list-header" style="margin-bottom: 16px;">
            <div class="header-left">
              <span style="font-size: 14px;">名单列表</span>
              <span style="color: #999; margin-left: 8px;">共 {{ detailListData.length }} 人</span>
            </div>
            <el-button type="primary" @click="handleAddStudent">
              +新增名单
            </el-button>
          </div>
          
          <el-table 
            :data="detailListData" 
            style="width: 100%; height: 500px;"
            size="large"
          >
            <el-table-column prop="name" label="姓名" align="center"/>
            <el-table-column label="性别" align="center" width="80">
              <template #default="scope">
                <el-tag 
                  v-if="scope.row.sex === '1'" 
                  type="info" 
                  effect="light"
                  style="background-color: #dbeafe; border-color: #3b82f6; color: #1e40af;"
                >
                  男
                </el-tag>
                <el-tag 
                  v-else-if="scope.row.sex === '2'" 
                  type="danger" 
                  effect="light"
                  style="background-color: #fdf2f8; border-color: #ec4899; color: #be185d;"
                >
                  女
                </el-tag>
                <span v-else style="color: #9ca3af;">-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-link type="primary" :underline="false" @click="handleEditStudent(scope.row)" style="margin-right: 20px;">
                  <el-icon style="margin-right: 5px;"><Edit /></el-icon>编辑
                </el-link>
                <el-link type="danger" :underline="false" @click="handleDeleteStudent(scope.row)">
                  <el-icon style="margin-right: 5px;"><Delete /></el-icon>删除
                </el-link>
              </template>
            </el-table-column>
          </el-table>

          <el-empty v-if="detailListData.length === 0" description="暂无数据" />
        </div>

        <!-- 新增/编辑名单弹窗 -->
        <el-dialog
          v-model="studentFormVisible"
          :title="isEdit ? '编辑名单' : '新增名单'"
          width="500px"
          append-to-body
          :close-on-click-modal="false"
        >
          <el-form
            ref="studentFormRef"
            :model="studentForm"
            :rules="studentFormRules"
            label-width="80px"
          >
            <el-form-item label="姓名" prop="name">
              <el-input v-model="studentForm.name" placeholder="请输入姓名"/>
            </el-form-item>
            <el-form-item label="性别" prop="sex">
              <el-radio-group v-model="studentForm.sex">
                <el-radio label="1">男</el-radio>
                <el-radio label="2">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="studentFormVisible = false">取消</el-button>
              <el-button type="primary" @click="submitStudentForm">确定</el-button>
            </div>
          </template>
        </el-dialog>

        <template #footer>
          <el-button class="dialog-exit-button" @click="detailDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>

      <!-- 编辑文件名称弹窗 -->
      <el-dialog
        v-model="editFileNameVisible"
        title="编辑文件名称"
        width="500px"
        append-to-body
        :close-on-click-modal="false"
      >
        <el-form
          ref="fileNameFormRef"
          :model="fileNameForm"
          :rules="fileNameFormRules"
          label-width="100px"
        >
          <el-form-item label="文件名称" prop="title">
            <el-input v-model="fileNameForm.title" placeholder="请输入文件名称"/>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="editFileNameVisible = false">取消</el-button>
            <el-button type="primary" @click="submitFileNameForm">确定</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 3D点名弹窗 -->
      <RollCall3D 
        :visible="rollCallVisible" 
        :students="students.map(student => student.name)" 
        :randomId="randomId"
        @close="rollCallVisible = false"
        @studentSelected="handleStudentSelected"
      />

      <!-- 悬浮球 -->
      <FloatingBall />
    </div>
  </div>
</template>

<script setup>
import {inject} from 'vue';
import { Upload, Document, List, Plus, Edit, Delete, Select, View, Position, Setting, ArrowDown } from '@element-plus/icons-vue';
import { ref, watch, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { uploadTxt, getTxtHistory, deleteTxt, getTxtDetail, getTemplate, addTxtName, editTxtName, deleteTxtName, getLastTxt, setDefaultTxt, updateTxtName } from '../api/rollcall';
import { useStore } from 'vuex'
import RollCall3D from '../components/RollCall3D.vue'
import FloatingBall from '../components/common/FloatingBall.vue'

const store = useStore()

const trackingService = inject('trackingService');

// 初始学生名单
const students = ref([]);
const studentsData = ref([]); // 存储完整学生数据
const randomId = ref('');

// 全屏loading状态
const uploadLoading = ref(false);

const historyDialogVisible = ref(false);
const historyData = ref([]);
const detailDialogVisible = ref(false);
const detailListData = ref([]);
const rollCallVisible = ref(false);

// 名单切换下拉菜单相关
const nameListDropdownVisible = ref(false);

const studentFormVisible = ref(false);
const isEdit = ref(false);
const studentForm = ref({
  id: '',
  name: '',
  sex: '',
});
const studentFormRandomId = ref('')

const studentFormRef = ref(null);
const studentFormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
  ]
};

// 编辑文件名称相关
const editFileNameVisible = ref(false);
const fileNameForm = ref({
  id: '',
  title: '',
});
const fileNameFormRef = ref(null);
const fileNameFormRules = {
  title: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { min: 1, max: 50, message: '文件名称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

// 加载学生名单（从文件导入）
function loadStudentList(file) {
  // 显示loading
  uploadLoading.value = true;
  
  const formData = new FormData();
  formData.append('file', file.raw);

  uploadTxt(formData)
    .then((response) => {
      const res = response.data || response;
      if (res.error > 0) {
        ElMessage({
        message: res.msg,
        type: 'error',
        dangerouslyUseHTMLString: true
      })
      } else {
        const nameList = res.data || [];
        if (Array.isArray(nameList)) {
          // 保存完整的学生数据
          studentsData.value = nameList;
          // 构造带性别的学生数据
          students.value = nameList.map(item => ({
            name: item.name ? String(item.name) : '',
            sex: item.sex || ''
          }));
        }
        randomId.value = res.randomId;
        ElMessage.success('导入名单成功');
      }
    })
    .catch(error => {
      console.error('上传文件出错:', error);
    })
    .finally(() => {
      // 隐藏loading
      uploadLoading.value = false;
    });
}

// 显示导入记录弹窗
const showHistoryDialog = async () => {
  try {
    const res = await getTxtHistory({
      userId: store.getters.userId
    });
    historyData.value = res.data.rows || [];
    historyDialogVisible.value = true;
  } catch (error) {
    console.error('获取导入记录失败:', error);
  }
};

// 选择历史名单
const handleSelectHistory = (row) => {
  ElMessageBox.confirm(`确定要使用文件 "${row.title}" 中的名单吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await setDefaultTxt({
        randomId: row.id,
        userId: store.getters.userId
      });
      const res = await getTxtDetail({
        randomId: row.id,
        userId: store.getters.userId
      });
      const nameList = res.data || [];
      if (Array.isArray(nameList) && nameList.length > 0) {
        // 保存完整的学生数据
        studentsData.value = nameList;
        // 构造带性别的学生数据
        students.value = nameList.map(item => ({
          name: item.name ? String(item.name) : '',
          sex: item.sex || ''
        }));
        randomId.value = row.id;
        historyDialogVisible.value = false;
        ElMessage.success(`已选择名单：${row.title}`);
      } else {
         ElMessage.warning('选择的名单为空或格式不正确');
      }
    } catch (error) {
      console.error('选择历史名单失败:', error);
    }
  }).catch(() => {});
};

// 查看历史名单详情
const handleViewHistory = async (row) => {
  studentFormRandomId.value = row.id;
  try {
    const res = await getTxtDetail({
      randomId: row.id,
      userId: store.getters.userId
    });
    const nameList = res.data || [];
     if (Array.isArray(nameList)) {
        detailListData.value = nameList;
        detailDialogVisible.value = true;
     } else {
        detailListData.value = [];
        detailDialogVisible.value = true;
     }
  } catch (error) {
    console.error('查看名单详情失败:', error);
  }
};

// 删除历史记录
const handleDeleteHistory = (row) => {
  ElMessageBox.confirm(`确定要删除文件 "${row.title}" 的导入记录吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteTxt(row.id);
      ElMessage.success('删除成功');
      const res = await getTxtHistory({userId: store.getters.userId});
      historyData.value = res.data.rows || [];
    } catch (error) {
      console.error('删除导入记录失败:', error);
    }
  })
};

// 编辑文件名称
const handleEditFileName = (row) => {
  fileNameForm.value = {
    id: row.id,
    title: row.title
  };
  editFileNameVisible.value = true;
};

// 提交文件名称修改
const submitFileNameForm = async () => {
  if (!fileNameFormRef.value) return;

  await fileNameFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await updateTxtName({
          id: fileNameForm.value.id,
          title: fileNameForm.value.title
        });
        ElMessage.success('文件名称修改成功');

        // 关闭弹窗
        editFileNameVisible.value = false;

        // 重新获取历史数据
        const res = await getTxtHistory({userId: store.getters.userId});
        historyData.value = res.data.rows || [];

        // 如果修改的是当前使用的名单，更新当前名单数据
        if (fileNameForm.value.id === randomId.value) {
          await loadLastRecord();
        }
      } catch (error) {
        console.error('修改文件名称失败:', error);
      }
    }
  });
};

// 下载模板
const downloadTemplate = async (val) => {
  try {
    const response = await getTemplate()
    console.log('response===', response)
    const excel_data = response.data

    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = excel_data.downloadUrl;
    document.body.appendChild(iframe);

    iframe.onload = () => {
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 1000);
    };

    iframe.onerror = () => {
      document.body.removeChild(iframe);
      ElMessage.error('下载模板失败');
    };
  } catch (error) {
    console.error('下载模板失败:', error);
  }
};

// 开始点名
const startRollCall = () => {
  if (students.value.length === 0) {
    ElMessage.warning('请先导入学生名单');
    return;
  }
  rollCallVisible.value = true;
  trackingService.trackEvent('RandCall')
};

// 处理学生被选中
const handleStudentSelected = (studentName) => {
  console.log('被选中的学生:', studentName);
  ElMessage.success(`恭喜 ${studentName} 被选中！`);
};

// 新增名单
const handleAddStudent = () => {
  isEdit.value = false;
  studentForm.value = {
    id: '',
    name: '',
    sex: '',
  };
  studentFormVisible.value = true;
};

// 编辑名单
const handleEditStudent = (row) => {
  isEdit.value = true;
  studentForm.value = {
    id: row.id,
    name: row.name,
    sex: row.sex
  };
  studentFormVisible.value = true;
};

// 删除名单
const handleDeleteStudent = (row) => {
  ElMessageBox.confirm(`确定要删除 ${row.name} 的名单吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteTxtName(row.id);
      ElMessage.success('删除成功');
      // 重新获取名单数据
      const res = await getTxtDetail({
        randomId: studentFormRandomId.value,
        userId: store.getters.userId
      });
      if(studentFormRandomId.value === randomId.value) {
        loadLastRecord()
      }
      detailListData.value = res.data || [];
    } catch (error) {
      console.error('删除名单失败:', error);
    }
  }).catch(() => {});
};

// 提交表单
const submitStudentForm = async () => {
  if (!studentFormRef.value) return;
  
  await studentFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (isEdit.value) {
          // 编辑名单
          await editTxtName({
            id: studentForm.value.id,
            name: studentForm.value.name,
            sex: studentForm.value.sex,
            randomId: studentFormRandomId.value
          });
          ElMessage.success('编辑成功');
        } else {
          // 新增名单
          await addTxtName({
            name: studentForm.value.name,
            sex: studentForm.value.sex,
            randomId: studentFormRandomId.value
          });
          ElMessage.success('新增成功');
        }
        
        // 关闭弹窗
        studentFormVisible.value = false;
        
        // 重新获取名单数据
        const res = await getTxtDetail({
          randomId:studentFormRandomId.value,
          userId: store.getters.userId
        });
        
        if(studentFormRandomId.value === randomId.value) {
          loadLastRecord()
        }

        detailListData.value = res.data || [];
      } catch (error) {
        console.error(isEdit.value ? '编辑名单失败:' : '新增名单失败:', error);
      }
    }
  });
};

// 加载最近的名单记录
const loadLastRecord = async () => {
  if (!store.getters.isLoggedIn) return;
  
  try {
    const res = await getLastTxt({
      userId: store.getters.userId
    });
    
    if (res.data && Array.isArray(res.data) && res.data.length > 0) {
      const nameList = res.data;
      // 保存完整的学生数据
      studentsData.value = nameList;
      // 构造带性别的学生数据，从返回数据结构中提取name和sex字段
      students.value = nameList.map(item => ({
        name: item.name ? String(item.name) : '',
        sex: item.sex || ''
      }));
      // 设置randomId为第一条记录的randomId
      if (nameList[0] && nameList[0].randomId) {
        randomId.value = nameList[0].randomId;
      }
    }
  } catch (error) {
    console.error('加载最近名单记录失败:', error);
  }
};

// 获取当前名单标题
const getCurrentListTitle = () => {
  if (studentsData.value.length > 0 && studentsData.value[0].randomName) {
    return studentsData.value[0].randomName;
  }
  return '当前名单';
};

// 切换名单下拉菜单显示状态
const toggleNameListDropdown = async () => {
  if (!nameListDropdownVisible.value) {
    // 打开下拉菜单时，先获取历史数据
    try {
      const res = await getTxtHistory({
        userId: store.getters.userId
      });
      historyData.value = res.data.rows || [];
    } catch (error) {
      console.error('获取导入记录失败:', error);
    }
  }
  nameListDropdownVisible.value = !nameListDropdownVisible.value;
};

// 选择名单（与handleSelectHistory逻辑相同，但不显示确认弹窗）
const handleSelectNameList = async (row) => {
  try {
    await setDefaultTxt({
      randomId: row.id,
      userId: store.getters.userId
    });
    const res = await getTxtDetail({
      randomId: row.id,
      userId: store.getters.userId
    });
    const nameList = res.data || [];
    if (Array.isArray(nameList) && nameList.length > 0) {
      // 保存完整的学生数据
      studentsData.value = nameList;
      // 构造带性别的学生数据
      students.value = nameList.map(item => ({
        name: item.name ? String(item.name) : '',
        sex: item.sex || ''
      }));
      randomId.value = row.id;
      nameListDropdownVisible.value = false; // 关闭下拉菜单
      ElMessage.success(`已切换到名单：${row.title}`);
    }
  } catch (error) {
    console.error('切换名单失败:', error);
  }
};

// 页面初始化
onMounted(async () => {
  // 加载最近的名单记录
  await loadLastRecord();

  // 点击页面其他地方关闭下拉菜单
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.list-title-dropdown')) {
      nameListDropdownVisible.value = false;
    }
  });
});

// 侧边栏事件处理
</script>

<style lang="scss" scoped>
.random-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 1rem;
  font-family: "Microsoft YaHei", Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 1.5rem;
  
  .title {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    color: #2d3748;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 700;
  }

  .subtitle {
    font-size: 1.1rem;
    color: #4a5568;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

.form-container {
  width: 80vw;
  margin: 0 auto;
}

.function-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.function-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }

  .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
  }

  .card-content {
    .card-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: #2d3748;
      margin: 0;
    }

    .card-description {
      color: #718096;
      font-size: 0.9rem;
      line-height: 1.4;
      margin-bottom: 1rem;
    }
  }
}

.upload-card {
  .card-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .upload-area {
    width: 100%;
    ::v-deep {
      .el-upload {
        width: 100%;
      }
    }
    
    
    .action-button {
      width: 100%;
      padding: 12px 24px;
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      border-radius: 10px;
      text-align: center;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
      }
    }
  }
}

.template-card {
  .card-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .action-button {
    width: 100%;
    padding: 12px 24px;
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    border-radius: 10px;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(250, 112, 154, 0.3);
    }
  }
}

.history-card {
  .card-icon {
    background: linear-gradient(135deg, #48bb78 0%, #68d391 100%);
  }

  .action-button {
    width: 100%;
    padding: 12px 24px;
    background: linear-gradient(135deg, #48bb78 0%, #68d391 100%);
    color: white;
    border-radius: 10px;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
    }
  }
}

/* 名单列表展示区域样式 */
.student-list-section {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  margin-top: 1.5rem;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
  }

  .list-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid #f7fafc;
    gap: 1rem;

    .list-title-container {
      position: relative;
      flex-shrink: 0;
    }

    .list-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: #667eea;
      margin: 0;
      flex-shrink: 0;

      &.clickable {
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #5a67d8;
        }
      }
    }

    .list-title-dropdown {
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;
      cursor: pointer;

      .dropdown-icon {
        font-size: 16px;
        color: #667eea;
        transition: transform 0.3s ease;

        &.rotated {
          transform: rotate(180deg);
        }
      }
    }

    .name-list-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 1000;
      background: white;
      border-radius: 12px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      border: 1px solid #e2e8f0;
      min-width: 280px;
      max-height: 400px;
      overflow: hidden;
      margin-top: 8px;

      .dropdown-header {
        padding: 12px 16px;
        background: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
        font-size: 0.9rem;
        font-weight: 600;
        color: #4a5568;
      }

      .dropdown-content {
        max-height: 360px;
        overflow-y: auto;
      }

      .dropdown-item {
        padding: 12px 16px;
        cursor: pointer;
        transition: all 0.2s ease;
        border-bottom: 1px solid #f1f5f9;
        position: relative;

        &:hover {
          background-color: #f8fafc;
        }

        &:last-child {
          border-bottom: none;
        }

        &.active {
          background-color: #eef2ff;
          border-left: 4px solid #667eea;
          
          .item-title {
            color: #4f46e5;
            font-weight: 600;
          }
          
          &:hover {
            background-color: #e0e7ff;
          }
        }

        .item-title {
          font-size: 0.95rem;
          font-weight: 500;
          color: #2d3748;
          margin-bottom: 4px;
        }

        .item-time {
          font-size: 0.8rem;
          color: #718096;
        }
      }

      .dropdown-empty {
        padding: 20px 16px;
        text-align: center;
        color: #a0aec0;
        font-size: 0.9rem;
      }
    }

    .list-info {
      flex-shrink: 0;

      .student-count {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      }
    }

    .roll-call-section-header {
      margin-left: auto;
      flex-shrink: 0;

      .roll-call-button {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        padding: 16px 32px;
        background: linear-gradient(to right, #00c6fb 0%, #005bea 100%);
        color: white;
        border: none;
        border-radius: 30px;
        font-size: 1.2rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        overflow: hidden;
        min-width: 160px;

        &:hover {
          transform: translateY(-2px) scale(1.03);
          box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        &:active {
          transform: translateY(-1px) scale(1.01);
        }

        .button-text {
          font-weight: 600;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  .student-list {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 1rem;

    .student-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 12px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-radius: 12px;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: inherit;
        opacity: 0.8;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        
        &::before {
          opacity: 1;
        }
      }

      &.male-student {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
        border-color: rgba(59, 130, 246, 0.3);
        
        .student-name {
          color: #1e40af;
          font-weight: 600;
        }
        
        &:hover {
          background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 50%, #60a5fa 100%);
          box-shadow: 0 6px 20px rgba(59, 130, 246, 0.15);
          border-color: rgba(59, 130, 246, 0.5);
        }
      }
      
      &.female-student {
        background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #fbcfe8 100%);
        border-color: rgba(236, 72, 153, 0.3);
        
        .student-name {
          color: #be185d;
          font-weight: 600;
        }
        
        &:hover {
          background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 50%, #f9a8d4 100%);
          box-shadow: 0 6px 20px rgba(236, 72, 153, 0.15);
          border-color: rgba(236, 72, 153, 0.5);
        }
      }

      .student-name {
        font-size: 1.1rem;
        color: #4a5568;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
        position: relative;
        z-index: 1;
        letter-spacing: 0.5px;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      }
    }
  }


}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .random-container {
    padding: 0.8rem;
  }

  .header {
    margin-bottom: 1rem;
    
    .title {
      font-size: 2rem;
      margin-bottom: 0.3rem;
    }

    .subtitle {
      font-size: 1rem;
      line-height: 1.3;
    }
  }

  .function-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .function-card {
    padding: 1.2rem;

    .card-icon {
      width: 40px;
      height: 40px;
      font-size: 18px;
      margin-right: 0.8rem;
    }

    .card-content {
      .card-title {
        font-size: 1.2rem;
      }

      .card-description {
        font-size: 0.85rem;
        margin-bottom: 0.8rem;
      }
    }
  }

  .student-list-section {
    padding: 1.2rem;
    margin-top: 1rem;

    .list-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
      margin-bottom: 0.8rem;
      padding-bottom: 0.8rem;

      .list-title-container {
        width: 100%;
      }

      .list-title {
        font-size: 1.2rem;
      }

      .name-list-dropdown {
        min-width: 260px;

        .dropdown-item {
          padding: 10px 14px;

          .item-title {
            font-size: 0.9rem;
          }

          .item-time {
            font-size: 0.75rem;
          }
        }
      }

      .list-info {
        align-self: flex-start;
        margin-left: 0;

        .student-count {
          font-size: 0.85rem;
          padding: 6px 12px;
        }
      }

      .roll-call-section-header {
        align-self: center;
        margin-left: 0;
        width: 100%;
        display: flex;
        justify-content: center;

        .roll-call-button {
          padding: 14px 24px;
          font-size: 1.1rem;
          min-width: 140px;
          border-radius: 25px;
        }
      }
    }

    .student-list {
      grid-template-columns: repeat(3, 1fr);
      gap: 0.8rem;
      min-height: 220px;

      .student-item {
        padding: 12px 8px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        &.male-student:hover {
          box-shadow: 0 4px 15px rgba(59, 130, 246, 0.12);
        }
        
        &.female-student:hover {
          box-shadow: 0 4px 15px rgba(236, 72, 153, 0.12);
        }

        .student-name {
          font-size: 0.95rem;
          letter-spacing: 0.3px;
        }
      }
    }



    /* 移动端空状态样式 */
    .empty-state {
      padding: 2rem 1rem;

      .empty-icon {
        font-size: 3rem;
      }

      .empty-title {
        font-size: 1.2rem;
      }

      .empty-description {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
      }

      .empty-actions {
        flex-direction: column;
        gap: 0.8rem;

        .action-btn {
          padding: 10px 20px;
          font-size: 0.9rem;
          min-width: 140px;
        }

        .action-divider {
          margin: 0;
        }
      }
    }
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #718096;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
  }

  .empty-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.8rem;
  }

  .empty-description {
    font-size: 1rem;
    line-height: 1.5;
    color: #718096;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  .empty-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;

    .action-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 12px 24px;
      border: none;
      border-radius: 10px;
      font-size: 0.95rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;

      &.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
      }

      &.secondary {
        background: #f7fafc;
        color: #4a5568;
        border: 2px solid #e2e8f0;

        &:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-2px);
        }
      }

      span {
        font-size: 1.1rem;
      }
    }

    .action-divider {
      color: #a0aec0;
      font-size: 0.9rem;
      margin: 0 0.5rem;
    }

    .empty-upload {
      ::v-deep .el-upload {
        display: inline-block;
      }
    }
  }
}

.manage-list-container {
  .manage-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 全屏Loading样式 */
.fullscreen-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease-out;

  .loading-content {
    padding-left: 125px;
    text-align: center;
    color: white;
    
    .loading-spinner {
      position: relative;
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      
      .spinner-ring {
        position: absolute;
        width: 80px;
        height: 80px;
        border: 3px solid transparent;
        border-radius: 50%;
        animation: spin 1.5s linear infinite;
        
        &:nth-child(1) {
          border-top-color: #667eea;
          animation-delay: 0s;
        }
        
        &:nth-child(2) {
          border-right-color: #764ba2;
          animation-delay: -0.5s;
          width: 60px;
          height: 60px;
          top: 10px;
          left: 10px;
        }
        
        &:nth-child(3) {
          border-bottom-color: #f093fb;
          animation-delay: -1s;
          width: 40px;
          height: 40px;
          top: 20px;
          left: 20px;
        }
      }
    }
    
    .loading-text {
      font-size: 1.8rem;
      font-weight: 600;
      margin-bottom: 8px;
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      animation: pulse 2s ease-in-out infinite;
    }
    
    .loading-subtitle {
      font-size: 1rem;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 400;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 移动端loading样式优化 */
@media (max-width: 768px) {
  .fullscreen-loading {
    .loading-content {
      padding: 0 20px;
      
      .loading-spinner {
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
        
        .spinner-ring {
          width: 60px;
          height: 60px;
          border-width: 2px;
          
          &:nth-child(2) {
            width: 45px;
            height: 45px;
            top: 7.5px;
            left: 7.5px;
          }
          
          &:nth-child(3) {
            width: 30px;
            height: 30px;
            top: 15px;
            left: 15px;
          }
        }
      }
      
      .loading-text {
        font-size: 1.4rem;
        margin-bottom: 6px;
      }
      
      .loading-subtitle {
        font-size: 0.9rem;
      }
    }
  }
}
</style>
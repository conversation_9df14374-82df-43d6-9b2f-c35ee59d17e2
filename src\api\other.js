import request from './request'

// 添加意见反馈
export function submitFeedback(data) {
  return request({
    url: '/junhengai/suggestion',
    method: 'post',
    data
  })
}

// 微信传图片-获取scan_id
export function getScanId(data) {
  return request({
    url: `${import.meta.env.VITE_IMAGE_CDN_API}/api/wechat-scan/generate-scan-id`,
    method: 'post',
    data
  })
}


// 微信传图片-通过scan_id获取图片
export function getImageByScanId(scan_id, params) {
  return request({
    url: `${import.meta.env.VITE_IMAGE_CDN_API}/api/wechat-scan/query-image/${scan_id}`,
    method: 'get',
    params
  })
}
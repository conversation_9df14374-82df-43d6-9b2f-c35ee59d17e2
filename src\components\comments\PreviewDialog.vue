<template>
  <el-dialog
    v-model="visible"
    title="预览学生评语"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div v-loading="loading">
      <el-table :data="previewData" border stripe style="width: 100%; min-height: 200px;">
        <el-table-column prop="studentName" label="学生姓名" width="120" align="center" />
        <el-table-column prop="evaluation" label="评语内容" min-width="300" header-align="center" />
        <el-table-column prop="goodsItemOne" label="表扬项1" width="150" align="center" />
        <el-table-column prop="goodsItemTwo" label="表扬项2" width="150" align="center" />
        <el-table-column prop="goodsItemThree" label="表扬项3" width="150" align="center" />
        <el-table-column prop="shortItemOne" label="不足项1" width="150" align="center" />
        <el-table-column prop="shortItemTwo" label="不足项2" width="150" align="center" />
        <el-table-column prop="limitWord" label="限制字数" width="100" align="center" />
      </el-table>
    </div>
    <template #footer>
      <el-button class="dialog-exit-button" @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { defineComponent, toRefs, computed } from 'vue';

export default defineComponent({
  name: 'PreviewDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true,
    },
    previewData: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const { modelValue } = toRefs(props);

    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value),
    });

    const handleClose = () => {
      visible.value = false;
    };

    return {
      visible,
      handleClose,
    };
  },
});
</script> 
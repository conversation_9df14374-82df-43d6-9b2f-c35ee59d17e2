<script setup lang="ts">
import {computed,defineExpose, ref, watch, defineProps, defineEmits, onMounted} from 'vue'
import {Check} from '@element-plus/icons-vue'

const emit = defineEmits<{
  (e: 'update:visible', val: boolean): void,
  (e: 'select', val: any): void
}>()
const props = defineProps<{
  visible: boolean,
  selectedId: string,
  users:Array
}>()

const dialogVisible = ref(props.visible)
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  emit('update:visible', val)
  selectedId.value=props.selectedId
})

const handleClose = () => {
  emit('update:visible', false)
}
const users = computed(() => props.users)
const selectedId = ref(props.selectedId)
const handleSelect = (a, i) => {
  selectedId.value = a.value

}
const confirmSure = async () => {
  emit('select', selectedId.value, users.value.find(a => a.value == selectedId.value))
  emit('update:visible', false)
}


</script>

<template>
  <el-dialog
      v-model="dialogVisible"
      width="900px"
      :close-on-click-modal="false"
      top="25vh"
      title="AI数字人切换"
      @close="handleClose"
  >
    <div class="list" v-if="users&&users.length">
      <div class="item" :key="i" v-for="(a,i) in users" @click="handleSelect(a,i)">
        <el-card class="item-box" shadow="hover">
          <div class="select" v-show="selectedId==a.value">
            <el-icon class="icon" size="24" color="#ffffff">
              <Check/>
            </el-icon>
          </div>
          <el-image class="img" fit="contain" :src="a.pic"></el-image>
          <p class="user-name">{{ a.name }}</p>
        </el-card>

      </div>

    </div>
    <el-empty v-else>
    </el-empty>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSure">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.list {
  min-height: 295px;
  overflow: hidden;

  .item {
    float: left;
    width: 33%;
    cursor: pointer;
    box-sizing: border-box;
    padding: 10px;

    .item-box {
      border-radius: 20px;
      position: relative;
      overflow: hidden;

      .select {
        background-color: #67C23A;
        width: 70px;
        height: 40px;
        position: absolute;
        top: 0;
        right: 0;
        transform: rotateZ(45deg) translateY(-24px) translateX(10px);

        .icon {
          transform: rotateZ(-45deg) translateY(10px) translateX(-10px);
        }
      }
    }

    .img {
      width: 220px;
      height: 220px;
    }

    .user-name {
      font-weight: bold;
      font-size: 16px;
      line-height: 16px;
      padding: 0;
      margin: 0;
      padding-top: 10px;
    }


  }
}

</style>
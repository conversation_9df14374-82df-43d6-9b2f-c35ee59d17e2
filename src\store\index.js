import { createStore } from 'vuex'
import userModule from './modules/user'
import uiModule from './modules/ui'
import appModule from './modules/app'

export default createStore({
  state: {},
  getters: {
    isLoggedIn: (state, getters) => getters['user/isLoggedIn'],
    userId: (state, getters) => getters['user/userId'],
    aiToken: (state, getters) => getters['user/aiToken'],
    roles: (state, getters) => getters['user/roles'],
    userInfo: (state, getters) => getters['user/userInfo'],
    activeCategory: (state, getters) => getters['ui/activeCategory'],
    appList: (state, getters) => getters['app/appList'],
    menuData: (state, getters) => getters['app/menuData'],
    isAppDataLoading: (state, getters) => getters['app/isLoading'],
    isAppDataLoaded: (state, getters) => getters['app/isLoaded'],
  },
  mutations: {},
  actions: {},
  modules: {
    user: userModule,
    ui: uiModule,
    app: appModule
  }
}) 
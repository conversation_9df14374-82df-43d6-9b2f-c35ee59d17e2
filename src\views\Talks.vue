<template>
  <div class="overflow-hidden container " :class="AIUserValue">
    <SoundLine v-show="showVoiceLoading" :text="talkingText"/>
    <!--    &lt;!&ndash;    用户介绍&ndash;&gt;-->
    <!--    <el-button @click="testClick">点击</el-button>-->

    <!--    <el-button @click="getBlance">查询余额</el-button>-->
    <div class="user-des">
      <div class="box">
        <div class="user-name">
          <el-icon>
            <User/>
          </el-icon>
          <p>{{ AIUserInfo.name }}</p>

        </div>
        <div class="user-content" v-show="showUserDesc">
          <div class="box">
            <el-icon class="close" @click="handleCloseUser">
              <CircleClose/>
            </el-icon>
            <p>{{ AIUserInfo.description }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="chat-page" :class="{playing:!isSpeechFinished}">
      <!--            <p> isSpeechFinished:{{ isSpeechFinished }}</p>-->
      <ChatBar class="input-box" @onTalkStart="handleTalkStart" @onTalking="handleTalking"
               @onTalkEnd="handleTalkEnd"/>
      <!--      数字人说话内容框 -->
      <!--      <div class="chat-content" v-if="aiSpeakWords&&aiSpeakWords.length" v-html="renderedMarkdown(aiSpeakWords)">-->
      <!--      </div>-->
      <div class="chat-content" v-if="aiSpeakWords&&aiSpeakWords.length">
        {{ stripMarkdown(aiSpeakWords) }}
      </div>
    </div>
    <el-drawer
        v-model="drawer"
        title="对话记录"
        :direction="'rtl'"
        :append-to-body="false"
        class="drawer"
        size="50%"
        :show-close="false"
        @open="openDrawer"

    >
      <div class="qs-content p-2">
        <QsMsg :currentConversation="currentConversation" ref="qsMsgRef" :isStreaming="isStreaming" @rebuild="rebuild"/>
      </div>
      <template #footer>
        <div style="flex: auto">
          <el-button size="large" round type="danger" @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-drawer>
    <!--    右侧操作栏 -->
    <div class="right-bar">
      <div class="bar-item" @click="drawer=true">
        <el-icon size="24">
          <ChatLineRound/>
        </el-icon>
        <p class="name">对话</p>
      </div>

      <div class="bar-item" @click="handleChangeUser">
        <el-icon size="24">
          <Switch/>
        </el-icon>
        <p class="name">切换</p>
      </div>
      <div class="bar-item" @click="handleKeyboard">
        <svg t="1747133438909" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
             p-id="2815" width="24" height="24">
          <path
              d="M554.666667 640h128v42.666667H384v-42.666667h170.666667z m341.333333-384v554.666667H170.666667V256h725.333333zM213.333333 298.666667v469.333333h640V298.666667H213.333333z m128 85.333333v42.666667H256V384h85.333333z m170.666667 0v42.666667H384V384h128z m170.666667 0v42.666667h-128V384h128z m128 0v42.666667h-85.333334V384h85.333334zM341.333333 512v42.666667H256v-42.666667h85.333333z m170.666667 0v42.666667H384v-42.666667h128z m170.666667 0v42.666667h-128v-42.666667h128z m128 0v42.666667h-85.333334v-42.666667h85.333334zM341.333333 640v42.666667H256v-42.666667h85.333333z m469.333334 0v42.666667h-85.333334v-42.666667h85.333334z"
              fill="#444444" p-id="2816"></path>
        </svg>
        <p class="name">键盘</p>
      </div>
    </div>
    <!--    底部操作栏-->
    <div class="bottom-bar">
      <div class="content">
        <div class="item" v-if="!isSpeechFinished" @click="handleStopAudio">
          <el-icon color="#F56C6C">
            <VideoPause/>
          </el-icon>
        </div>
      </div>
    </div>
    <!--    虚拟人切换 -->
    <VirtualHumanSwitching v-model:visible="visible" @select="handleSelected" :users="users"
                           :selected-id="AIUserValue"/>
    <!--  调用内容输入-->
    <div class="inputbar" v-show="visibleKeyword">
     <div class="wrap">
      <div class="close" @click="visibleKeyword=false;keyword=''">
        <el-icon size="26" color="#F56C6C"><CircleClose /></el-icon>
      </div>
       <el-input
           v-model="keyword"
           type="textarea"
           :min-rows="2"
           placeholder="输入您的问题"
           class="problem-input"
           resize="none"
           @keyup.enter="handleSend"
       />
       <el-button class="btn" @click="handleSend" type="primary" size="large" round ><el-icon color="#ffffff" size="20"><Promotion/></el-icon>发送</el-button>
     </div>
    </div>

  </div>
</template>
<script setup lang="ts">
import {inject} from 'vue';

const trackingService = inject('trackingService');
import {ref, reactive, nextTick, onMounted, onBeforeMount, onBeforeUnmount} from 'vue'
import MarkdownIt from 'markdown-it'
import mk from 'markdown-it-katex'
import {fetchEventSource} from '@microsoft/fetch-event-source'
import ChatBar from './components/ChatBar.vue'
import {getTTSLL, getTTSLLBalance} from '@/api/verse'
import SoundLine from './components/soundLine.vue'
import QsMsg from '@/views/components/QsMsg.vue'
import {
  ChatLineRound,
  Promotion,
  Switch,
  User,
  VideoPause,
  CloseBold,
  CircleClose,
  VideoPlay
} from '@element-plus/icons-vue'
import {stripMarkdown} from '@/utils/tools'
import VirtualHumanSwitching from "@/views/talks/components/VirtualHumanSwitching.vue";

// Refs
const qsMsgRef = ref(null)
const isluxun = ref(false)
const drawer = ref(false)
const talkingText = ref('')
const isPlaying = ref(false)
const isStreaming = ref(false)
const isSpeechFinished = ref(true)
const controller = ref(new AbortController())
const scrollThrottle = ref()
const showVoiceLoading = ref(false)
let speechFullyFinishedLogged = false

// State
const currentConversation = reactive({
  messages: []
})
const aiSpeakWords = ref('')
const audioQueue = ref<string[]>([]) // 存储音频 URL 队列
const audioElement = ref<HTMLAudioElement | null>(null)

// Methods
const onCancel = () => {
  isluxun.value = true
  handleStopAudio()
  currentConversation.messages = []
  aiSpeakWords.value = ''
}
const onConfirm = () => {
  isluxun.value = false
  handleStopAudio()
  currentConversation.messages = []
  aiSpeakWords.value = ''
}
const handleClose = () => (drawer.value = false)
const visible = ref(false)
const handleChangeUser = async () => {
  console.log('dddd')
  visible.value = true
}
const visibleKeyword = ref(false)
const handleKeyboard = () => {
  visibleKeyword.value = !visibleKeyword.value
}

const keyword =ref('')
const handleSend = async ()=>{
  handleStopAudio()
  await handleSearch(keyword.value)
  visibleKeyword.value=false
  keyword.value=''
}


const AIUserValue = ref('liqingzhao')
const AIUserInfo = ref()

const users = ref([
  {
    name: '李清照',
    voice: 861,
    value: 'liqingzhao',
    description: `大家好，我是李清照，宋代女词人，喜书善文，尤擅词作。`,
    pic: new URL('@/assets/aiUser/liqingzhao.png', import.meta.url).href,
    bg: new URL('@/assets/aiUser/bg.png', import.meta.url).href,
    assistantTip: ` ##角色设定
  身份：宋代著名女词人，婉约派代表，号“易安居士”。
  时代背景：北宋末年至南宋初期（1084-1155），经历战乱与漂泊。
##对话内容：
  简洁明了：回答问题时语言要简练清晰，不冗长，不添加多余内容。
  完整准确：确保信息完整，能正面回应用户提出的问题。
  纯文本交流：仅限文字表达、贴合说话断句，不使用动作、表情或格式化描写。
##安全限制
  不讨论暴力、政治敏感内容，例如“靖康之耻”仅提及“战乱影响”。`
  },
  {
    name: '鲁迅',
    value: 'luxun',
    voice: 869,
    description: `大家好，我是鲁迅，中国现代文学家与思想家，我以笔为剑，深剖社会弊病，尤其擅长通过小说和杂文来揭示人性的复杂和社会的不公。`,
    pic: new URL('@/assets/aiUser/luxun.png', import.meta.url).href,
    bg: new URL('@/assets/aiUser/bgl.png', import.meta.url).href,
    assistantTip: `##角色设定
  身份：中国现代著名文学家、思想家，新文化运动的重要参与者。
  时代背景：清朝末年至民国时期（1881-1936），见证了中国社会的巨大变革。
##对话内容：
   简洁明了：回答问题时语言要简练清晰，不冗长，不添加多余内容。
   完整准确：确保信息完整，能正面回应用户提出的问题。
   纯文本交流：仅限文字表达、贴合说话断句，不使用动作、表情或格式化描写。
##安全限制
  不讨论暴力、政治敏感内容，对于可能引发争议的话题，采取客观公正的态度，并引导回到文学创作的主题上。`
  },
])
AIUserInfo.value = users.value[0]
const handleSelected = async (a, obj) => {
  AIUserValue.value = a
  AIUserInfo.value = obj
  handleStopAudio()
  currentConversation.messages = []
  aiSpeakWords.value = ''
}


const props = defineProps<{
  onSpeechAllFinished?: () => void
}>()
// 琅琅语音查询余额
const getBlance = async () => {
  const res = await getTTSLLBalance()
  console.log(res.data)
}

async function handleSearch(val: string) {
  controller.value = new AbortController()
  isStreaming.value = true
  await nextTick()
  qsMsgRef.value?.scrollToBottom(true)

  // const apiUrl = 'https://junheng.chinatiye.cn/ai/api/chat'
  const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v1/modelPureGs`;

  let assistantTip = AIUserInfo.value?.assistantTip
  // const requestBody = {
  //   messages: [
  //      {role: 'system', content: assistantTip},
  //     ...currentConversation.messages,
  //     {role: 'user', content: val}
  //   ],
  //   stream: true,
  //   temperature: 1,
  //   provider: 'siliconflow',
  //
  // }
    const requestBody = {
        messages: [
          ...currentConversation.messages,
          {role: 'user', content: val}
        ],
      // incrementalOutput:false,
      // hasThoughts:false,
      formType:AIUserValue.value=='liqingzhao'?'digital_liqingzhao':'digital_luxun'
    }

  currentConversation.messages.push({role: 'user', content: val})
  currentConversation.messages.push({role: 'assistant', content: '正在思考中...', reasoning: ''})
  const messageIndex = currentConversation.messages.length - 1

  let accumulatedText = ''
  let speechBuffer = ''
  aiSpeakWords.value = ''

  fetchEventSource(apiUrl, {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify(requestBody),
    signal: controller.value.signal,
    openWhenHidden: true,

    async onopen(response) {
      if (!response.ok) throw new Error('网络请求失败')
    },

    async onmessage(msg) {
      if (msg.event === 'FatalError') throw new Error(msg.data)
      const data = msg.data;
      const jsonData = JSON.parse(data)
      console.log(jsonData.output.finishReason)
      if (jsonData.output.finishReason === 'stop') {
        isStreaming.value = false
        segmentAndQueue(speechBuffer)
        speechBuffer = ''
        return
      }
      const content =  jsonData.output.text
      if (content) {
        accumulatedText += content
        currentConversation.messages[messageIndex].content = accumulatedText
        aiSpeakWords.value = stripMarkdown(accumulatedText)
        speechBuffer += content

        // 分段并立即合成音频
        const sentences = splitSentences(speechBuffer)
        speechBuffer = sentences.remaining

        for (const sentence of sentences.list) {
          console.log(sentence)
          await enqueueSpeechSegment(sentence)
        }
      }

      await nextTick()
    },

    async onclose() {
      if (scrollThrottle.value) clearTimeout(scrollThrottle.value)
      segmentAndQueue(speechBuffer)
      checkIfSpeechFullyFinished()
      isStreaming.value = false
    },

    onerror(err) {
      console.error('SSE error:', err)
      currentConversation.messages[messageIndex].content = `抱歉，我遇到了一些问题：${err}`
      isStreaming.value = false
      throw new Error(err)
    }
  })
}

function splitSentences(text: string): { list: string[]; remaining: string } {
  const matches = text.match(/[^。！？]+[。！？]/g)
  if (!matches) return {list: [], remaining: text}
  const last = text.slice(matches.join('').length)
  return {
    list: matches,
    remaining: last
  }
}

let synthQueue: Promise<void> = Promise.resolve() // 合成串行队列

async function enqueueSpeechSegment(sentence: string) {
  // 将任务加入串行队列，按顺序合成音频
  synthQueue = synthQueue.then(async () => {
    try {
      const res = await getTTSLL({
        text: stripMarkdown(sentence),
        speakerId: AIUserInfo.value.voice,
        speed: '1',
        volume: '1.0',
        pitch: '1.0'
      })
      const url = res.data?.fileUrl
      if (url) {
        audioQueue.value.push(url)
        if (!isPlaying.value) {
          await processAudioQueue()
        }
      }
    } catch (e) {
      console.error('音频合成失败:', e)
    }
  })
}


function segmentAndQueue(text: string) {
  if (!text.trim()) return
  const segments = text.match(/[^。！？]+[。！？]?/g) || [text]
  segments.forEach(seg => {
    if (seg.trim()) enqueueSpeechSegment(seg.trim())
  })
}

async function processAudioQueue() {
  if (audioQueue.value.length === 0 || isPlaying.value) return
  isPlaying.value = true

  const url = audioQueue.value.shift()
  if (!url) {
    isPlaying.value = false
    return
  }

  const audio = new Audio(url)
  audioElement.value = audio
  isPlaying.value = true
  isSpeechFinished.value = false

  audio.onended = () => {
    isPlaying.value = false
    isSpeechFinished.value = true
    processAudioQueue()
    checkIfSpeechFullyFinished()
  }

  audio.onerror = () => {
    console.error('音频播放出错')
    isPlaying.value = false
    isSpeechFinished.value = true
    processAudioQueue()
    checkIfSpeechFullyFinished()
  }

  try {
    await audio.play()
  } catch (err) {
    console.error('播放出错', err)
    isPlaying.value = false
    isSpeechFinished.value = true
    processAudioQueue()
    checkIfSpeechFullyFinished()
  }
}

function checkIfSpeechFullyFinished() {
  if (!isStreaming.value && audioQueue.value.length === 0 && !isPlaying.value && !speechFullyFinishedLogged) {
    console.log('✅ 全部流式内容已播报完成')
    isSpeechFinished.value = true
    speechFullyFinishedLogged = true
    props.onSpeechAllFinished?.()
  }
}

const rebuild = async (val: any) => {
  currentConversation.messages.splice(-2)
  await nextTick()
  qsMsgRef.value.scrollToBottom(true)
  handleStopAudio()
  await handleSearch(val.userContent)
}

const openDrawer = async () => {
  await nextTick()
  qsMsgRef.value.scrollToBottom(true)
}

const showUserDesc = ref(true)
const handleCloseUser = () => (showUserDesc.value = false)
const testClick = () => {
  trackingService.trackEvent('Talks')
}
const handleTalkStart = async () => {
  showVoiceLoading.value = true
  handleStopAudio()
  trackingService.trackEvent('Talks')

}
const handleTalking = async (val: string) => (talkingText.value = val)
const handleTalkEnd = async (val: string) => {
  showVoiceLoading.value = false
  talkingText.value = val
  await handleSearch(val)
}

const handleStopAudio = () => {
  if (audioElement.value) {
    audioElement.value.pause()
    audioElement.value.src = ''
    audioElement.value = null
  }
  isSpeechFinished.value = true
  speechFullyFinishedLogged = true
  audioQueue.value = []
  controller.value.abort()
}

// 渲染 Markdown
const renderedMarkdown = (markdownContent: string) => {
  if (!markdownContent) return
  let processedContent = markdownContent
  processedContent = processedContent.replace(/<ref>$(\d+)($$\d+)*$<\/ref>/g, '')
  processedContent = processedContent.replace(/\\$\s*([\s\S]*?)\s*\\$/g, (match, p1) => `$$${p1.trim()}$$`)
  processedContent = processedContent.replace(/\\$\s*([\s\S]*?)\s*\\$/g, (match, p1) => `$${p1.trim()}$`)
  const doubleDollarBlocks = []
  processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, (match, content) => {
    doubleDollarBlocks.push(match)
    return `__DOUBLE_DOLLAR_PLACEHOLDER_${doubleDollarBlocks.length - 1}__`
  })
  processedContent = processedContent.replace(/(\$)\s*([^\$]+?)\s*(\$)/g, (match, start, content, end) => {
    return `${start}${content.trim()}${end}`
  })
  doubleDollarBlocks.forEach((block, index) => {
    processedContent = processedContent.replace(`__DOUBLE_DOLLAR_PLACEHOLDER_${index}__`, block)
  })

  const md = new MarkdownIt({
    breaks: true,
    html: true,
    typographer: true
  })
  md.use(mk)
  return md.render(processedContent)
}

onBeforeUnmount(() => {
  handleStopAudio()
})
onBeforeMount(async () => {


})
</script>

<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
}

.container {
  height: 100%;
  background: url('@/assets/aiUser/bg.jpeg') 0 0 no-repeat;
  background-size: cover;
  box-sizing: border-box;
  overflow: hidden;


}

.chat-page {
  position: relative;
  top: 0vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('@/assets/aiUser/liqingzhao.png');
  background-repeat: no-repeat;
  background-position: left 60px;
  background-size: auto 100%;
  justify-content: center;

  .chat-content {
    background-color: green;
    margin-left: 40%;
    max-height: 80%;
    max-width: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    padding: 20px;
    border-radius: 20px;
    font-size: 28px;
    letter-spacing: 2px;
    overflow-y: scroll;
    text-align: left;

  }

  &.playing {
    background-image: url('@/assets/aiUser/liqingzhao_a.png');
    background-repeat: no-repeat;
    background-position: left 60px;
    background-size: auto 100%;
  }

  .qs-content {
    position: relative;
    flex: auto 1 1;
    min-height: 0;
    //overflow: hidden;
    display: flex;
    align-items: stretch;
    width: 200px;

    height: 100%;

  }
}

.luxun {
  &.container {
    background: url('@/assets/aiUser/bgl.jpg') 0 0 no-repeat;
    background-size: cover;
  }

  .chat-page {
    background-image: url('@/assets/aiUser/luxun.png');

    background-size: auto 100%;

    &.playing {
      background-image: url('@/assets/aiUser/luxun_a.png');
      background-size: auto 100%;

    }
  }
}


.right-bar {
  position: fixed;
  right: 20px;
  top: 50%;
  z-index: 5;

  .bar-item {
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.4);
    padding: 5px 10px 10px;
    height: 60px;
    width: 60px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;

    .name {
      font-size: 18px;
      line-height: 10px;
    }
  }
}

.bottom-bar {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  height: 48px;
  z-index: 9;

  .content {
    display: flex;
    align-items: center;
    justify-content: center;

    .item {
      font-size: 48px;
      margin: 0 10px;
    }
  }
}

.user-des {
  background-color: rgba(255, 255, 255, 0.4);
  position: fixed;
  top: 20px;
  right: 60px;
  padding: 5px 12px;
  border-radius: 10px;
  z-index: 99;

  .user-name {
    font-size: 24px;
    letter-spacing: 1.5px;
    display: flex;
    align-items: center;
  }

  /* 定义动画 */
  @keyframes moveUpDown {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px); /* 根据需要调整移动的距离 */
    }
  }

  .box {
    display: flex;
    align-items: center;
    position: relative;

    .user-content {
      position: absolute;
      text-align: left;
      width: 400px;
      background-color: #ffffff;
      padding: 5px 10px;
      border-radius: 10px;
      left: -350px;
      top: 60px;
      font-size: 18px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      animation: moveUpDown 3s infinite ease-in-out; /* 根据需要调整动画持续时间和缓动函数 */
      .box {
        position: relative;
        width: 100%;

        .close {
          position: absolute;
          left: -20px;
          top: -20px;
          font-size: 1.5rem;
          color: #F56C6C;
          cursor: pointer;
        }
      }

      &:after {
        content: '';
        border-width: 10px;
        border-color: transparent;
        border-bottom-color: #ffffff;
        border-style: solid;
        position: absolute;
        top: -20px;
        z-index: 5;
        right: 20px;
      }
    }

  }

}

.drawer {
  background: transparent !important;

}


// 定义断点变量
$mobile-breakpoint: 768px; // 移动端最大宽度
// 移动端样式
@media (max-width: $mobile-breakpoint) {

}

// PC端样式
@media (min-width: $mobile-breakpoint + 1px) {


}
.inputbar{
  position: fixed;
  left: 60%;
  top:30%;
  z-index: 99;
  border-radius: 1.5rem;
  padding:20px;
  box-shadow: 0 0 25px rgba(0, 0, 255, 0.08);
  background-color: #fff;
  width:480px;
  margin-left: -120px;
  .wrap{
    postion: relative;
    padding-bottom: 40px;
    .close{
      position: absolute;
      top:-15px;
      right:-15px;
      cursor: pointer;
    }
    .btn{
      position: absolute;
      bottom:10px;
      right:20px;
    }
  }

 :deep(.el-textarea__inner) {
    padding: 1.2rem;
    line-height: 1.7;
    border-radius: 1rem;
    border-color: #e0e0ff;
  }

}

</style>

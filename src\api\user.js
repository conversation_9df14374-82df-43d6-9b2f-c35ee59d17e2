import request from './request'

// 验证码登录
export function loginBySms(data) {
  return request({
    url: '/junhengai/auth/smsLogin',
    method: 'post',
    data
  })
}

// 发送验证码
export function sendSms(params) {
  return request({
    url: '/junhengai/resource/sms/code',
    method: 'get',
    params
  })
}

// 获取sseId
export function getSseId(params) {
  return request({
    url: '/junhengai/auth/client/sse/build',
    method: 'get',
    params
  })
}

// sseId状态
export function getSseStatus(params) {
  return request({
    url: '/junhengai/auth/get/token',
    method: 'get',
    params
  })
}

// 账号密码登录
export function loginByPassword(data) {
  return request({
    url: '/junhengai/auth/login',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo(params) {
  return request({
    url: '/junhengai/system/user/getInfo',
    method: 'get',
    params
  })
}

// 修改用户名称
export function editUserName(data) {
  return request({
    url: '/junhengai/system/user/editName',
    method: 'post',
    data
  })
}

// 修改用户头像
export function editUserAvatar(data) {
  return request({
    url: '/junhengai/system/user/edit/avatar',
    method: 'post',
    data
  })
}

// 对接授权登录
export function authThirdLogin(data) {
  return request({
    url: '/junhengai/auth/authThirdLogin',
    method: 'post',
    data
  })
}
<template>
  <div class="admin-container">
    <EchartsCount/>
  </div>
</template>

<script setup>
import {ref} from 'vue';
import EchartsCount from "../components/EchartsCount.vue";
</script>

<style scoped>
.admin-container {
  padding: 20px 30px;
  height: 100%;
  overflow-y: auto;
  margin: 0 auto;
  box-sizing: border-box;
}

.admin-tabs {
  height: 100%;


}
:deep(.el-tabs__header){
  margin-bottom: 0 !important;
}
:deep( .el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: #409eff;
  color:#ffffff;
}

:deep(.el-tabs__content) {
  padding: 10px 0;
  height: calc(100% - 50px);
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-top: none;
}

:deep(.el-tabs__header) {
  margin-bottom: 10px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 20px;
}
</style>
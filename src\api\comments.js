import request from './request'

// 下载批量学生评语模板
export function downloadTemplate() {
  return request({
    url: '/junhengai/evaluation/import/template',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入批量学生评语数据
export function importData(data) {
  return request({
    url: '/junhengai/evaluation/importData',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 获取导入状态
export function getImportStatus(params) {
  return request({
    url: '/junhengai/evaluation/getImportStatus',
    method: 'get',
    params: params
  })
}

// 下载生成结果
export function downloadResult(data) {
  return request({
    url: '/junhengai/evaluation/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
} 

// 查询导入记录列表
export function getImportList(params) {
  return request({
    url: '/junhengai/evaluation/getExcelList',
    method: 'get',
    params: params
  })
}

// 查询评语数据
export function getExcelData(data) {
  return request({
    url: '/junhengai/evaluation/export/data/list',
    method: 'post',
    data
  })
}
# Vue3 前端开发框架

这是一个基于 Vue 3 构建的前端开发框架，使用了现代前端技术栈。

## 技术栈

- **Vue 3**：使用 Composition API 开发风格
- **Vite**：快速的前端构建工具
- **Vue Router**：路由管理
- **Vuex**：状态管理
- **Element Plus**：UI 框架
- **Axios**：HTTP 请求库
- **Sass**：CSS 预处理器

## 项目结构

```
├── public/             # 静态资源目录
├── src/                # 源代码
│   ├── api/            # API 请求
│   ├── assets/         # 资源文件
│   ├── components/     # 组件
│   ├── router/         # 路由配置
│   ├── store/          # Vuex 状态管理
│   ├── styles/         # 全局样式
│   ├── views/          # 页面视图
│   ├── App.vue         # 根组件
│   └── main.js         # 入口文件
├── .env                # 基础环境变量
├── .env.development    # 开发环境变量
├── .env.production     # 生产环境变量
├── vite.config.js      # Vite 配置
└── package.json        # 项目依赖
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建项目

```bash
# 开发环境构建
npm run build:dev

# 生产环境构建
npm run build:prod
```

### 预览构建结果

```bash
npm run serve:dist
```

## 代码风格

项目使用 Vue 3 的 Composition API 风格进行开发，推荐使用 `<script setup>` 语法。

## 环境变量和代理

项目配置了开发代理转发和环境变量，可以在相应的环境文件中进行配置：

- `.env`：所有环境共享的变量
- `.env.development`：开发环境变量
- `.env.production`：生产环境变量

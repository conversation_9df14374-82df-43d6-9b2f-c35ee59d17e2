<template>
  <div class="home-container">
    <div class="top-left-circle left1"/>
    <div class="top-left-circle right1"/>
    <!-- 未登录提醒 -->
    <div v-if="!isLoggedIn" class="login-reminder" @click="loginDialogVisible = true">
      <svg class="reminder-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M21 21V19C21 16.7909 19.2091 15 17 15H7C4.79086 15 3 16.7909 3 19V21" stroke="currentColor"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span>登录体验完整功能</span>
    </div>
    <div class="bg">
      <div class="top-left-circle"/>
      <div class="bg-c">
        <img src="@/assets/index/ai2.png"/>
      </div>
      <div class="bg-b">
        <img src="@/assets/index/books.png"/>
      </div>
      <div class="qa-module ">
        <h2 class="qa-title">
          <span class="brand-text">均衡AI</span>
          <span class="separator"></span>
          <span class="brand-subtitle">AI赋能教育</span>
        </h2>
        <!-- 问答模块 -->
        <div class="unified-input-container-wrapper">
          <div class="history-button" @click="handleHistoryMessage" v-if="isLoggedIn">
            <svg style="width:20px; height: 20px;" class="history-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 8v4l3 3" />
              <circle cx="12" cy="12" r="10" />
            </svg>
            <span>对话历史</span>
          </div>
          <div class="unified-input-container">
            <div class="input-with-icon">
              <div class="search-icon"></div>
              <el-input
                  v-model="chatQuery"
                  placeholder="有问题，尽管问..."
                  class="chat-textarea"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 3 }"
                  @keydown.enter.exact.prevent="handleSendMessage"
              />
            </div>
            <div class="chat-actions-bar">
              <div class="left-actions">
                <el-button
                    :class="['action-btn', { 'selected': selectedAction === 'deepThink' }]"
                    @click="selectAction('deepThink')"
                >
                                 <svg viewBox="0 0 24 24" width="16" height="16" class="react-icon" style="margin-right: 6px;">
                  <path fill="currentColor" d="M12 10.11c1.03 0 1.87.84 1.87 1.89 0 1.04-.84 1.87-1.87 1.87-1.03 0-1.87-.83-1.87-1.87 0-1.05.84-1.89 1.87-1.89M7.37 20c.63.38 2.01-.2 3.6-1.7-.52-.59-1.03-1.23-1.51-1.9-.82-.08-1.63-.2-2.4-.36-.51 2.14-.32 3.61.31 3.96m.71-5.74l-.29-.51c-.11.29-.22.58-.29.86.27.06.57.11.88.16l-.3-.51m6.54-.76l.81-1.5-.81-1.5c-.3-.53-.62-1-.91-1.47C13.17 9.03 12.6 9 12 9c-.6 0-1.17.03-1.71.08-.29.47-.61.94-.91 1.47L8.57 12l.81 1.5c.3.53.62 1 .91 1.47.54.05 1.11.08 1.71.08.6 0 1.17-.03 1.71-.08.29-.47.61-.94.91-1.47M12 6.78c-.19.22-.39.45-.59.72h1.18c-.2-.27-.4-.5-.59-.72m0 10.44c.19-.22.39-.45.59-.72h-1.18c.2.27.4.5.59.72M16.62 4c-.62-.38-2 .2-3.59 1.7.52.59 1.03 1.23 1.51 1.9.82.08 1.63.2 2.4.36.51-2.14.32-3.61-.32-3.96m-.7 5.74l.29.51c.11-.29.22-.58.29-.86-.27-.06-.57-.11-.88-.16l.3.51m1.45-7.05c1.47.84 1.63 3.05 1.01 5.63 2.54.75 4.37 1.99 4.37 3.68 0 1.69-1.83 2.93-4.37 3.68.62 2.58.46 4.79-1.01 5.63-1.46.84-3.45-.12-5.37-1.95-1.92 1.83-3.91 2.79-5.38 1.95-1.46-.84-1.62-3.05-1-5.63-2.54-.75-4.37-1.99-4.37-3.68 0-1.69 1.83-2.93 4.37-3.68-.62-2.58-.46-4.79 1-5.63 1.47-.84 3.46.12 5.38 1.95 1.92-1.83 3.91-2.79 5.37-1.95M17.08 12c.34.75.64 1.5.89 2.26 2.1-.63 3.28-1.53 3.28-2.26 0-.73-1.18-1.63-3.28-2.26-.25.76-.55 1.51-.89 2.26M6.92 12c-.34-.75-.64-1.5-.89-2.26-2.1.63-3.28 1.53-3.28 2.26 0 .73 1.18 1.63 3.28 2.26.25-.76.55-1.51.89-2.26m9 2.26l-.3.51c.31-.05.61-.1.88-.16-.07-.28-.18-.57-.29-.86l-.29.51m-2.89 4.04c1.59 1.5 2.97 2.08 3.59 1.7.64-.35.83-1.82.32-3.96-.77.16-1.58.28-2.4.36-.48.67-.99 1.31-1.51 1.9M8.08 9.74l.3-.51c-.31.05-.61.1-.88.16.07.28.18.57.29.86l.29-.51m2.89-4.04C9.38 4.2 8 3.62 7.37 4c-.63.35-.82 1.82-.31 3.96.77-.16 1.58-.28 2.4-.36.48-.67.99-1.31 1.51-1.9z"/>
                </svg>
                  深度思考 (R1)
                </el-button>
                <el-button
                    :class="['action-btn', { 'selected': selectedAction === 'webSearch' }]"
                    @click="selectAction('webSearch')"
                >
                  <svg style="width: 16px; height: 16px;" class="btn-icon" viewBox="0 0 24 24" width="16" height="16"
                       stroke-width="2.5" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="2" y1="12" x2="22" y2="12"></line>
                    <path
                        d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                  </svg>
                  联网搜索
                </el-button>
              </div>
              <div class="right-actions">
                <el-popover
                  placement="top"
                  width="300"
                  trigger="hover"
                  content="上传文件（仅识别文字），支持pdf、pptx、docx、xlsx、txt、图片，单图片最大20MB，其他文件最大50MB"
                >
                  <template #reference>
                    <el-button class="upload-btn" @click="handleUploadClick">
                      <el-icon size="18px" style="margin-right: 6px;">
                        <Upload />
                      </el-icon>
                      上传附件
                    </el-button>
                  </template>
                </el-popover>
                <el-button
                    class="send-btn-final"
                    type="primary"
                    @click="handleSendMessage"
                    :class="{
                      'disabled-btn': !chatQuery.trim()
                    }"
                    :disabled="!chatQuery.trim()"
                >
                  <el-icon size="24px" style="margin-right: 5px;">
                    <Position />
                  </el-icon>
                  <span>发送</span>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="main-body">
      <template v-for="app in appList" :key="app.id">
        <div :id="app.index" class="category-section">
          <h2 class="category-title">{{app.name}}</h2>
          <div class="tools-grid">
            <template v-for="tool in app.tools" :key="tool.id">
              <!-- 自研工具 -->
              <div
                v-if="!tool.isPlatformTool"
                class="tool-card enhanced-tool-card"
                :class="tool.color"
                @click="openTool(tool)"
              >
                <img :src="tool.icon" alt="" class="tool-icon enhanced-tool-icon"/>
                <h3 class="tool-name enhanced-tool-name">{{ tool.name }}</h3>
                <p class="tool-description enhanced-tool-description">{{ tool.description }}</p>
              </div>
              <!-- 三方工具 -->
              <div
                v-else
                class="tool-card three-tool-card"
                @click="openTool(tool)"
              >
                <img :src="tool.icon" alt="" class="tool-icon"/>
                <h3 class="tool-name">{{ tool.name }}</h3>
                <p class="tool-description">{{ tool.description }}</p>
              </div>
            </template>
          </div>
        </div>
      </template> 
    </div>

    <!-- 登录弹窗 -->
    <LoginDialog
        v-model:visible="loginDialogVisible"
        @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue';
import {useRouter} from 'vue-router';
import {useStore} from 'vuex';
import LoginDialog from '@/components/login/LoginDialog.vue';
import { getUserInfo } from '@/api/user'
import { Position, Upload } from '@element-plus/icons-vue'

const router = useRouter();
const store = useStore();

// 响应式数据
const loginDialogVisible = ref(false);

// 新增：问答模块相关
const chatQuery = ref('');
const selectedAction = ref(null); // 'deepThink' 或 'webSearch'

// 用户信息相关
const isLoggedIn = computed(() => store.getters.isLoggedIn);

// 应用列表 - 从 store 获取
const appList = computed(() => store.getters.appList);

// 确保数据已加载（如果还未加载则触发加载）
const ensureAppDataLoaded = () => {
  if (!store.getters.isAppDataLoaded && !store.getters.isAppDataLoading) {
    store.dispatch('app/loadAppData');
  }
};

// 处理登录成功
const handleLoginSuccess = () => {
  loginDialogVisible.value = false;
};

// 判断登录态是否过期
const checkLoginStatus = async () => {
  try {
    const res = await getUserInfo();
    return true
  } catch (error) {
    console.error('检查登录状态失败:', error);
    return false
  }
}


// 打开工具
const openTool = async (tool) => {
  // 新增：根据工具确定所属分类并更新导航菜单
  let categoryIndex = '';
  
  // 从 store 中查找工具所属的分类
  const allCategories = store.getters.appList;
  for (const category of allCategories) {
    if (category.tools && category.tools.find(t => t.id === tool.id)) {
      categoryIndex = category.index;
      break;
    }
  }
  
  // 如果找到对应分类，更新导航菜单
  if (categoryIndex) {
    store.commit('ui/setActiveCategory', categoryIndex);
  }

  let link = tool.link;
  
  // 1. 内部路由（以/开头且不是http或https）
  if (link.startsWith('/') && !link.startsWith('http://') && !link.startsWith('https://')) {
    if (store.getters.isLoggedIn) {
      // 跳转前检查登录态是否有效
      const loginStatus = await checkLoginStatus();
      if (!loginStatus) {
        loginDialogVisible.value = true;
        return;
      }
      // 移除开头的斜杠，使其成为相对路径
      const relativePath = link.substring(1);
      router.push(relativePath);
       // 重置滚动位置到顶部
       window.scrollTo(0, 0);
    } else {
      loginDialogVisible.value = true;
    }
    return;
  }
  
  // 2. HTTP/HTTPS链接
  if (link.startsWith('http://') || link.startsWith('https://')) {
    // 2.1 内部网站（包含chinatiye.cn）
    if (link.includes('chinatiye.cn')) {
      if (store.getters.isLoggedIn) {
        // 跳转前检查登录态是否有效
        const loginStatus = await checkLoginStatus();
        if (!loginStatus) {
          loginDialogVisible.value = true;
          return;
        }
        if(link.includes('know.chinatiye.cn') || link.includes('junheng.chinatiye.cn/knowledge')){
          // 知识库单独要求外链打开
          link = link + `?token=${store.getters.aiToken}`
          window.open(link, '_blank');
        } else {
          // 使用iframe路由打开内部网站
          router.push({
            path: 'iframe',
            query: {
              url: link,
              title: tool.name
            }
          });
        }
        // 重置滚动位置到顶部
       window.scrollTo(0, 0);
      } else {
        loginDialogVisible.value = true;
      }
    } else {
      // 2.2 外部网站（不包含chinatiye.cn）
      window.open(link, '_blank');
    }
    return;
  }
  
  // 3. 其他情况默认处理
  if (store.getters.isLoggedIn) {
    const relativePath = link.startsWith('/') ? link.substring(1) : link;
    router.push(relativePath);
     // 重置滚动位置到顶部
     window.scrollTo(0, 0);
  } else {
    loginDialogVisible.value = true;
  }
};

// 新增：问答模块相关
const selectAction = (actionName) => {
  if (selectedAction.value === actionName) {
    selectedAction.value = null; // 再次点击则取消选中
  } else {
    selectedAction.value = actionName;
  }
};

// 新增：处理上传附件按钮点击
const handleUploadClick = () => {
  if (!isLoggedIn.value) {
    loginDialogVisible.value = true;
    return;
  }

  // 将当前输入内容和选择状态存储到 localStorage
  const chatParams = {
    message: chatQuery.value.trim(),
    deepThink: false,
    webSearch: false,
    needUpload: true // 标记需要上传附件
  };
  
  localStorage.setItem('chatParams', JSON.stringify(chatParams));

  // 跳转到 ask 页面
  router.push('ask');
  
  // 清空输入框和选择状态
  chatQuery.value = '';
  selectedAction.value = null;
};

// 新增：发送消息
const handleSendMessage = () => {
  if (!chatQuery.value.trim()) return;

  if (!isLoggedIn.value) {
    loginDialogVisible.value = true;
    return;
  }

  // 将参数存储到 localStorage
  const chatParams = {
    message: chatQuery.value.trim(),
    deepThink: selectedAction.value === 'deepThink',
    webSearch: selectedAction.value === 'webSearch'
  };
  
  localStorage.setItem('chatParams', JSON.stringify(chatParams));
  
  // 跳转到 ask 页面（使用相对路径）
  router.push('ask');
  
  // 清空输入框和选择状态
  chatQuery.value = '';
  selectedAction.value = null;
};

// 查看历史记录
const handleHistoryMessage = () => {
  router.push('ask');
};

// 在组件挂载时确保数据已加载
onMounted(() => {
  ensureAppDataLoaded();
});
</script>

<style scoped lang="scss">
.three-tool-card{
  background: linear-gradient(-225deg, #E8E9EA 0%, #f9f9f9 48%, #ffffff 100%) !important;

}
.home-container {
  padding: 20px;
  max-width: none;
  margin: 0;
  position: relative;
  z-index: 10;
  min-height: 100vh;

  .top-left-circle {
    position: absolute;
    left: 3.4vw;
    background-image: linear-gradient(-225deg, #5D9FFF 0%, #B8DCFF 48%, #6BBBFF 100%);
    border-radius: 50%;
    opacity: .4;
    top: 15vh;
    width: 80px;
    height: 80px;
    z-index: 0;
    offset-path: path("M 0 40 Q 85 10 110 40 Z");
    offset-rotate: 0deg;
    animation: move 15s linear infinite;
    &.left1{
      left: 9.4vw;
      top: 60vh;
      opacity: .4;
      width: 120px;
      height: 120px;
      animation: move 12s linear infinite;
    }
    &.right1{
      opacity: .4;
      left: unset;
      right:5.3vw;
      bottom: 20vh;
      top: unset;
      width: 160px;
      height: 160px;
      animation: move 12s linear infinite;
    }
  }
  .main-body{
    position: relative;
    z-index: 10;
  }
}

@keyframes move {
  0% {
    offset-distance: 0%;
  }

  100% {
    offset-distance: 100%;
  }
}

/* 未登录提醒样式 */
.login-reminder {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
  z-index: 10;
}

.login-reminder:hover {
  background: rgba(64, 158, 255, 0.1);
  border-color: #409eff;
  color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.reminder-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.bg {
  background: url('@/assets/index/bg.png') 0 0 no-repeat;
  background-size: 100% 100%;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  position: relative;
  .bg-c{
    position: absolute;
    right: 0px;
    top: 0px;
    width: 9vw;
    height: auto;
    img{
      width: 100%;
      height: 100%;
      display: inline-block;
    }
  }
  .bg-b{
    position: absolute;
    left: 10px;
    bottom: 10px;
    width: 5vw;
    height: auto;

    img{
      width: 100%;
      height: 100%;
      display: inline-block;
    }
  }
}

/* 问答模块样式 */
.qa-module {
  margin-bottom: 30px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

.qa-title {
  font-size: 42px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 24px;
  letter-spacing: -0.8px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
}

.brand-text {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#00c6fb), to(#005bea));
  background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: pre;
}

.brand-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: blur(2px);
  z-index: -1;
}

.separator {
  background: linear-gradient(to top, #00c6fb 0%, #005bea 100%);
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0;
  border-radius: 50%;
}

.brand-subtitle {
  color: #444;
  font-weight: 600;
  font-size: 28px;
  letter-spacing: 0.5px;
}

.unified-input-container-wrapper {
  padding: 3px;
  border-radius: 16px;
  position: relative;
  z-index: 10;
  background-image: linear-gradient(270deg, #ffad71, #ff5aa2, #46aaff, #80d0ff);
}

.history-button {
  position: absolute;
  top: -48px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 500;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  backdrop-filter: blur(4px);
  z-index: 20;
}

.history-button:hover {
  background-color: #fff;
  border-color: #409eff;
  color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-button .history-icon {
  width: 15px;
  height: 15px;
  transition: all 0.3s ease;
}

.history-button:hover .history-icon {
  stroke: #409eff;
}

.unified-input-container {
  background: #FFF;
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
}

.input-with-icon {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.search-icon {
  width: 36px;
  height: 36px;
  flex-shrink: 0;
  display: inline-block;
  background-image: url('@/assets/search-btn.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  margin-top: 4px;
}

.chat-textarea {
  border: none !important;
  padding: 0 !important;
  margin-bottom: 10px;
  flex: 1;
}

::v-deep {
  .chat-textarea .el-textarea__inner {
    border: none !important;
    box-shadow: none !important;
    padding: 8px 2px !important;
    resize: none !important;
    background-color: transparent !important;
    color: #303133;
    font-size: 16px;
    line-height: 1.6;
    min-height: 60px !important;
    &:focus {
      border: none !important;
      box-shadow: none !important;
    }
  }
}

.chat-actions-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  background-color: #fff;
  border: 1px solid #DCDFE6;
  color: #333;
  padding: 0 12px;
  height: 32px;
  border-radius: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
  font-weight: normal;
}

.action-btn .btn-icon {
  margin-right: 6px;
  stroke: #333;
}

.action-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.action-btn:hover .btn-icon {
  stroke: #409EFF;
}

.action-btn.selected {
  background-color: #fff;
  border-color: #409EFF;
  color: #409EFF;
}

.action-btn.selected .btn-icon {
  stroke: #409EFF;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-btn {
  background-color: #fff;
  border: 1px solid #DCDFE6;
  color: #333;
  padding: 0 12px;
  height: 32px;
  border-radius: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
  font-weight: normal;
}

.upload-btn .btn-icon {
  margin-right: 6px;
  stroke: #333;
}

.upload-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.upload-btn:hover .btn-icon {
  stroke: #409EFF;
}

.send-btn-final {
  width: 144px;
  height: 50px;
  background: linear-gradient(to right, #00c6fb 0%, #005bea 100%);
  border-radius: 25px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  border: none;
  color: white;
  font-size: 18px;
  font-weight: 600;

  &.disabled-btn {
    background: linear-gradient(to right, #c0c4cc 0%, #909399 100%);
    cursor: not-allowed;
    opacity: 0.6;
    &:hover{
      background: linear-gradient(to right, #c0c4cc 0%, #909399 100%);
    }
  }
}

.category-section {
  padding: 0 24px;
  padding-bottom: 10px;
}

.category-title {
  font-size: 22px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24px;
  padding-left: 18px;
  position: relative;
  letter-spacing: 0.8px;
  line-height: 1.5;
}

.category-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 22px;
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #93c5fd 100%);
  border-radius: 3px;
  margin-right: 12px;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
}

/* ========== 工具卡片样式 ========== */

/* 基础工具卡片（AI教育神器） */
.tool-card {
  background: #fff;
  border: 1px solid #eef0f2;
  border-radius: 8px;
  padding: 20px;
  height: 120px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  /* 布局 */
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: auto auto auto;
  grid-column-gap: 12px;
  text-align: left;
  
  /* 动画效果 */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: transparent;
}

.tool-card:hover .tool-name,
.tool-card:hover .tool-description {
  transform: translateY(-1px);
}

/* 工具图标 */
.tool-icon {
  grid-row: 1 / 2;
  grid-column: 1 / 2;
  width: 32px;
  height: 32px;
  margin-top: 2px;
  border-radius: 6px;
  object-fit: contain;
  align-self: start;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 工具名称 */
.tool-name {
  grid-row: 1 / 2;
  grid-column: 2 / 3;
  font-size: 20px;
  font-weight: 600;
  color: #434040;
  line-height: 1.3;
  margin: 0;
  margin-top: 3px;
  align-self: start;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 工具描述 */
.tool-description {
  grid-row: 3 / 4;
  grid-column: 1 / -1;
  font-size: 16px;
  color: #5b4949;
  line-height: 1.5;
  margin: 8px 0 0 0;
  
  /* 文本截断 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-card:hover .tool-name {
  color: #434040;
}

.tool-card:hover .tool-description {
  color: #5b4949;
}

/* 平台认证图标 */
.platform-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  z-index: 10;
}

/* ========== 增强工具卡片（课堂/教学/办公工具） ========== */

.enhanced-tool-card {
  height: 120px;
  position: relative;
  overflow: hidden;
  display: block;
  padding: 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-tool-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.enhanced-tool-card:hover .enhanced-tool-name,
.enhanced-tool-card:hover .enhanced-tool-description {
  transform: translateY(-1px);
}

/* 分类渐变背景 */
.enhanced-tool-card.classroom-card {
  background: linear-gradient(to bottom, #aacff3, #FFFFFF);
}

.enhanced-tool-card.teaching-card {
  background: linear-gradient(to bottom, #dbf1de, #FFFFFF);
}

.enhanced-tool-card.office-card {
  background: linear-gradient(to bottom, #d9c6f3, #FFFFFF);
}

/* 增强工具图标 */
.enhanced-tool-icon {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 50px !important;
  height: 50px !important;
  opacity: 0.8;
  z-index: 1;
}

/* 增强工具名称 */
.enhanced-tool-name {
  color: #434040 !important;
  font-size: 22px !important;
  font-weight: 700 !important;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.3;
  margin-bottom: 10px;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 增强工具描述 */
.enhanced-tool-description {
  color: #5b4949 !important;
  font-size: 16px !important;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-weight: 400;
  line-height: 1.8;
  margin: 0;
  letter-spacing: 0.3px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-tool-card:hover .enhanced-tool-name {
  color: #434040 !important;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.enhanced-tool-card:hover .enhanced-tool-description {
  color: #5b4949 !important;
}

/* 响应式设计 */
@media (max-width: 1800px) {  
  .enhanced-tool-icon {
    width: 40px !important;
    height: 40px !important;
    bottom: 4px;
    right: 4px;
  }
  
  .enhanced-tool-description {
    font-size: 16px !important;
  }
}

@media (max-width: 1600px) {
  .tools-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (max-width: 1400px) {
  .tools-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1024px) {
  .tools-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .home-container {
    padding: 0 16px 16px;
  }

  .chat-actions-bar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .left-actions {
    justify-content: center;
  }
  
  .right-actions {
    justify-content: center;
  }

  .tool-card {
    height: 110px;
    padding: 16px;
    grid-column-gap: 10px;
  }
  
  .tool-icon {
    width: 28px;
    height: 28px;
  }
  
  .tool-name {
    font-size: 18px;
  }
  
  .enhanced-tool-card {
    height: 110px;
    padding: 16px;
  }
  
  .enhanced-tool-icon {
    width: 40px !important;
    height: 40px !important;
  }
  
  .enhanced-tool-name {
    font-size: 18px !important;
    letter-spacing: 0.4px;
  }
  
  .enhanced-tool-description {
    font-size: 16px !important;
    max-width: calc(100% - 50px);
    letter-spacing: 0.25px;
  }
}

@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .qa-title{
    margin-bottom: 54px !important;
  }
  .bg .bg-c{
    width: 20vw;
  }
  .bg .bg-b{
    width: 10vw;
  }
  .unified-input-container{
    padding:10px;
  }
  .send-btn-final{
    width: 120px;
    height:40px;
    font-size: 14px;
  }
  .left-actions{
    justify-content: space-around;
  }
  .right-actions{
    justify-content: space-around;
  }
  .chat-actions-bar{
    gap:8px
  }
  .tool-card {
    height: 100px;
    padding: 14px;
    grid-column-gap: 8px;
  }
  
  .tool-icon {
    width: 24px;
    height: 24px;
  }
  
  .tool-name {
    font-size: 16px;
  }
  
  .enhanced-tool-card {
    height: 100px;
    padding: 14px;
  }
  
  .enhanced-tool-icon {
    width: 36px !important;
    height: 36px !important;
    bottom: 6px;
    right: 6px;
  }
  
  .enhanced-tool-name {
    font-size: 16px !important;
    margin-bottom: 6px;
    letter-spacing: 0.3px;
  }
  
  .enhanced-tool-description {
    font-size: 14px !important;
    max-width: calc(100% - 45px);
    letter-spacing: 0.2px;
  }

  .category-title {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .home-container {
    padding: 12px;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .tool-card {
    height: 90px;
    padding: 12px;
    grid-column-gap: 8px;
  }
  
  .tool-icon {
    width: 22px;
    height: 22px;
  }
  
  .tool-name {
    font-size: 15px;
  }
  
  .tool-description {
    font-size: 13px;
  }
  
  .enhanced-tool-card {
    height: 90px;
    padding: 16px;
  }
  
  .enhanced-tool-icon {
    width: 32px !important;
    height: 32px !important;
  }
  
  .enhanced-tool-name {
    font-size: 16px !important;
    letter-spacing: 0.3px;
  }
  
  .enhanced-tool-description {
    font-size: 14px !important;
    max-width: calc(100% - 40px);
    letter-spacing: 0.2px;
  }
}
</style> 
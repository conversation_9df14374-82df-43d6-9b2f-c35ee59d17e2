<template>
  <div class="messages-container" ref="messagesContainer">
    <div style="height: 20px;"></div>
    <div class="message assistant" v-if="guideTip&&guideTip.length">
      <div class="message-avatar">
        <img src="../../assets/logo.png" alt="AI助手" class="ai-avatar"/>
      </div>
      <div class="message-content-wrapper">
        <div class="message-content">
          <div v-html="renderedMarkdown(guideTip)"></div>
        </div>
      </div>
    </div>
    <template v-if="currentConversation?.messages?.length > 0">
      <div
          v-for="(message, index) in currentConversation.messages"
          :key="index"
          :class="['message', message.role]"
      >
        <div class="message-avatar">
          <template v-if="message.role === 'assistant'">
            <img src="../../assets/logo.png" alt="AI助手" class="ai-avatar"/>
          </template>
          <template v-else>
            <el-avatar :src="userAvatar" :size="40" class="user-avatar">
              <img v-if="!userAvatar" src="../../assets/user.png" alt="用户头像"/>
            </el-avatar>
          </template>
        </div>
        <div class="message-content-wrapper">
          <div class="reasoning-process" v-if="message.thoughtsContent">
            <p class="process"><span>思考过程：</span></p>
            <div v-html="renderedMarkdown(message.thoughtsContent)"></div>
          </div>
          <div class="message-content markdown-body" :class="{user: message.role === 'user'}">
            <div v-html="renderedMarkdown(message.content)"></div>
            <div
                v-if="
                message.role === 'assistant' && message.content !== '正在思考中...'
              "
                class="message-actions"
            >
              <div class="column-right">
                <!--                <el-button-->
                <!--                    type="primary"-->
                <!--                    round-->
                <!--                    @click="createPPT(message,index)"-->
                <!--                    v-if=" !isGenerating &&(!showTip||hasNums>0)"-->
                <!--                >-->
                <!--                  <el-icon>-->
                <!--                    <DataBoard/>-->
                <!--                  </el-icon>-->
                <!--                  生成PPT编辑大纲-->
                <!--                </el-button>-->
              </div>
              <div class="column-left">
                <div
                    class="action-button"
                    @click="regenerateMessage(message, index)"
                    v-if="isLastAssistantMessage(index) && !isGenerating"
                >
                  <el-icon>
                    <RefreshRight/>
                  </el-icon>
                  <span>重新生成</span>
                </div>
                <div class="action-button" @click="copyContent(message.content)">
                  <el-icon>
                    <CopyDocument/>
                  </el-icon>
                  <span>复制</span>
                </div>
                <!--                <div class="action-button" @click="DelContent(message,index)">-->
                <!--                  <el-icon>-->
                <!--                    <Delete/>-->
                <!--                  </el-icon>-->
                <!--                  <span>删除</span>-->
                <!--                </div>-->
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <el-empty v-else/>
    <!-- 预览 -->
    <PreView ref="preView"/>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, onBeforeUnmount, watch, nextTick} from "vue";
import MarkdownIt from "markdown-it";
import mk from "markdown-it-katex";
import {ElMessage, ElMessageBox} from 'element-plus'
import {CopyDocument, DataBoard, Delete, Notebook, RefreshRight} from "@element-plus/icons-vue";
import { useStore } from 'vuex';

// 获取store实例
const store = useStore();

const props = defineProps({
  currentConversation: {
    type: Object,
    default: () => ({messages: []}),
  },
  isStreaming: {
    type: Boolean,
    default: false,
  },
});
const guideTip = ``;

// 用户信息相关
const isLoggedIn = computed(() => store.getters.isLoggedIn);
const userInfo = computed(() => store.getters.userInfo);

// 用户头像计算属性
const userAvatar = computed(() => {
  if (isLoggedIn.value && userInfo.value && userInfo.value.avatar) {
    return userInfo.value.avatar;
  }
  return null; // 如果没有用户头像，返回null，让el-avatar使用默认的img标签
});

const messagesContainer = ref<HTMLElement | null>(null);
const timer = ref<number | null>(null);
const isGenerating = computed(() => props.isStreaming);

const showTip = ref(false)


// 渲染 Markdown
const renderedMarkdown = (markdownContent: string) => {
  if (!markdownContent) return;
  let processedContent = markdownContent
  processedContent = processedContent.replace(/<ref>\[(\d+)(\]\[\d+)*\]<\/ref>/g, '')
  processedContent = processedContent.replace(/\\\[\s*([\s\S]*?)\s*\\\]/g, (match, p1) => `$$${p1.trim()}$$`)
  processedContent = processedContent.replace(/\\\(\s*([\s\S]*?)\s*\\\)/g, (match, p1) => `$${p1.trim()}$`)
  const doubleDollarBlocks = [];
  processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, (match, content) => {
    doubleDollarBlocks.push(match);
    return `__DOUBLE_DOLLAR_PLACEHOLDER_${doubleDollarBlocks.length - 1}__`;
  });
  processedContent = processedContent.replace(/(\$)\s*([^\$]+?)\s*(\$)/g, (match, start, content, end) => {
    return `${start}${content.trim()}${end}`;
  });
  doubleDollarBlocks.forEach((block, index) => {
    processedContent = processedContent.replace(`__DOUBLE_DOLLAR_PLACEHOLDER_${index}__`, block);
  });

  const md = new MarkdownIt({
    breaks: true,
    html: true,
    typographer: true,
  });
  md.use(mk);
  return md.render(processedContent);
};

// 判断是否是最后一条助手消息
const isLastAssistantMessage = (index: number) => {
  return (
      index === props.currentConversation.messages.length - 1 &&
      props.currentConversation.messages[index].role === "assistant"
  );
};

// 重新生成消息
const regenerateMessage = (message: any, index: number) => {
  if (isGenerating.value) return;
  emit("rebuild", {
    userContent: props.currentConversation.messages[index - 1].content,
  });
};

//   删除对话
const DelContent = (message, index) => {
  ElMessageBox.confirm('确定要删除该对话吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    console.log(index)
    // currentConversation.messages.splice(index, 1)

  }).catch(() => {
    // 取消操作
  });
}

// 复制内容
const copyContent = (content: string) => {
  const tempElement = document.createElement("div");
  tempElement.innerHTML = content;
  const textContent = tempElement.textContent || tempElement.innerText;
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(textContent).then(() => {
      ElMessage.success("复制成功");
    }).catch(() => fallbackCopyText(textContent));
  } else {
    fallbackCopyText(textContent);
  }
};

// 回退复制方法
const fallbackCopyText = (text: string) => {
  try {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    document.execCommand("copy");
    textArea.remove();
    alert("复制成功");
  } catch (err) {
    alert("复制失败，请手动复制");
  }
};

// 滚动到底部
const scrollToBottom = async (force = false) => {
  // 等待下一个DOM更新周期
  await nextTick();
  
  if (!messagesContainer.value) return;
  const container = messagesContainer.value;
  
  // 确保容器有正确的高度
  if (container.scrollHeight === 0) {
    console.warn('Container height is 0, waiting for content...');
    // 如果容器高度为0，等待一会儿再试
    setTimeout(() => scrollToBottom(force), 100);
    return;
  }

  try {
    container.scrollTo({
      top: container.scrollHeight,
      behavior: force ? "auto" : "smooth",
    });
    console.log('Scrolled to bottom, height:', container.scrollHeight);
  } catch (err) {
    console.error('Failed to scroll:', err);
  }
};

// 强制立即滚动到底部
const forceScrollToBottom = () => {
  scrollToBottom(true);
};

// 监听消息变化
watch(
  () => props.currentConversation.messages,
  async () => {
    await nextTick();
    await scrollToBottom();
  },
  { deep: true }
);

// 监听流式输出状态
watch(
  () => props.isStreaming,
  async (newVal) => {
    if (newVal) {
      await nextTick();
      await scrollToBottom();
    }
  }
);

// 组件挂载时
onMounted(async () => {
  await nextTick();
  await scrollToBottom(true);
  
  // 监听容器大小变化
  if (window.ResizeObserver) {
    const observer = new ResizeObserver(() => {
      scrollToBottom();
    });
    if (messagesContainer.value) {
      observer.observe(messagesContainer.value);
    }
  }
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearTimeout(timer.value);
  }
});

const emit = defineEmits(["rebuild", 'handleCreatePPT']);
defineExpose({
  scrollToBottom,
  forceScrollToBottom
})
</script>

<style lang="scss" scoped>


.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background: transparent;
  display: flex;
  flex-direction: column;
  height: 100%;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  .message {
    display: flex;
    margin-bottom: 24px;
    padding: 0 4px;

    .message-avatar {
      width: 42px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      flex-shrink: 0;
      overflow: hidden;

      .ai-avatar {
        width: 32px;
        height: 32px;
      }

      .user-avatar {
        width: 100%;
        height: 100%;

        ::v-deep .el-avatar__inner {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .message-content-wrapper {
      max-width: calc(100% - 70px);

      .reasoning-process {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(60, 60, 80, 0.9) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.15);
        padding: 16px 20px 12px;
        margin-bottom: 12px;
        border-radius: 12px;
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);

        .process {
          margin-top: 0;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

          .icon {
            font-size: 16px;
            cursor: pointer;
            padding: 2px 5px 10px;
            color: rgba(59, 130, 246, 0.8);
            transition: all 0.2s ease;

            &:hover {
              color: rgba(59, 130, 246, 1);
              transform: scale(1.1);
            }
          }
        }
      }

      .message-content {
        padding: 5px 20px;
        border-radius: 16px;
        letter-spacing: 0.5px;
        line-height: 1.5;
        font-size: 16px;
        display: inline-block;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
          0 4px 16px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);

        &.user {
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.25) 0%, rgba(60, 60, 80, 0.95) 100%) !important;
          color: #ffffff !important;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        > div:first-child {
          margin: -2px 0;
        }
      }
    }

    &.user {
      flex-direction: row-reverse;

      .message-avatar {
        margin-right: 0;
        margin-left: 16px;
      }
    }

    &.assistant {
      .message-avatar {
        color: white;
      }

      .message-content-wrapper {
        display: flex;
        flex-direction: column;
        text-align: left;
      }

      .message-content {
        background: linear-gradient(135deg, rgba(40, 40, 55, 0.8) 0%, rgba(60, 60, 80, 0.9) 100%);
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        border-bottom-left-radius: 8px;
      }
    }
  }

  .empty-state {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.5);
    padding: 40px 20px;

    i {
      font-size: 64px;
      margin-bottom: 24px;
      color: rgba(59, 130, 246, 0.3);
      filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.2));
    }

    p {
      font-size: 16px;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      letter-spacing: 0.5px;
    }
  }
}

.message-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  gap: 8px;

  .action-button {
    margin-left: 20px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    i {
      font-size: 14px;
    }

    &:hover {
      background: rgba(59, 130, 246, 0.2);
      color: #ffffff;
      border-color: rgba(59, 130, 246, 0.3);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }

    &:active {
      transform: translateY(0) scale(0.98);
    }

    &.active {
      background: rgba(59, 130, 246, 0.3);
      color: #ffffff;
      border-color: rgba(59, 130, 246, 0.5);
      font-weight: 600;

      &:hover {
        background: rgba(59, 130, 246, 0.4);
      }
    }
  }
}

//参考来源
.citeSources {
  margin-top: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);

  .citeSources-head {
    font-size: 16px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

    .icon {
      font-size: 18px;
      padding-right: 4px;
      font-weight: bold;
      color: rgba(59, 130, 246, 0.8);
    }
  }

  .citeSources-item {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 8px;

    .citeSources-item-index {
      width: 100%;

      ::v-deep {
        .el-link--inner {
          display: inline-block;
          max-width: 100%;
          color: rgba(59, 130, 246, 0.8) !important;

          &:hover {
            color: rgba(59, 130, 246, 1) !important;
          }
        }
      }
    }

    .citeSources-index {
      padding-right: 4px;
      display: inline-block;
      width: 26px;
      text-align: right;
      color: rgba(59, 130, 246, 0.6);
      font-weight: 600;
    }

    .citeSources-content {
      font-size: 15px;
      line-height: 1.5;
    }
  }
}

/* el-empty 组件样式优化 */
:deep(.el-empty) {
  flex: 1;
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 60px 20px;

  .el-empty__image {
    width: 120px !important;
    height: 120px !important;
    margin-bottom: 24px !important;
    opacity: 0.6;
    filter: brightness(0.8) contrast(1.2);
  }

  .el-empty__description {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    letter-spacing: 0.5px !important;
    margin-top: 0 !important;
  }
}

/* 深色主题下的内容样式优化 */
::v-deep {
  .message-content {
    ref {
      color: rgba(59, 130, 246, 0.8) !important;
    }

    /* Markdown 内容样式 */
    h1, h2, h3, h4, h5, h6 {
      color: #ffffff !important;
      margin: 16px 0 12px 0 !important;
      font-weight: 600 !important;
    }

    p {
      color: rgba(255, 255, 255, 0.9) !important;
      margin: 8px 0 !important;
      line-height: 1.7 !important;
    }

    code {
      background: rgba(59, 130, 246, 0.1) !important;
      color: rgba(59, 130, 246, 0.9) !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
      border: 1px solid rgba(59, 130, 246, 0.2) !important;
    }

    pre {
      background: rgba(0, 0, 0, 0.4) !important;
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      border-radius: 8px !important;
      padding: 16px !important;
      margin: 12px 0 !important;

      code {
        background: transparent !important;
        border: none !important;
        color: rgba(255, 255, 255, 0.9) !important;
      }
    }

    ul, ol {
      color: rgba(255, 255, 255, 0.9) !important;
      margin: 8px 0 !important;
      padding-left: 20px !important;
    }

    li {
      margin: 4px 0 !important;
      line-height: 1.6 !important;
    }

    blockquote {
      border-left: 4px solid rgba(59, 130, 246, 0.5) !important;
      background: rgba(59, 130, 246, 0.05) !important;
      margin: 12px 0 !important;
      padding: 12px 16px !important;
      border-radius: 0 8px 8px 0 !important;
      color: rgba(255, 255, 255, 0.9) !important;
    }

    table {
      border-collapse: collapse !important;
      margin: 12px 0 !important;
      width: 100% !important;

      th, td {
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding: 8px 12px !important;
        color: rgba(255, 255, 255, 0.9) !important;
      }

      th {
        background: rgba(59, 130, 246, 0.2) !important;
        font-weight: 600 !important;
      }

      tr:nth-child(even) {
        background: rgba(255, 255, 255, 0.02) !important;
      }
    }
  }
}

</style>

<template>
  <div class="tool-management">
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" size="large" @click="handleAdd">+ 新增工具</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      :data="toolList"
      border
      style="width: 100%"
      v-loading="loading"
      :cell-style="{fontSize: '14px', padding: '12px 0'}"
      :header-cell-style="{fontSize: '15px', fontWeight: 'bold', background: '#f5f7fa', padding: '12px 0'}"
      row-class-name="table-row"
      highlight-current-row
    >
      <el-table-column prop="name" label="工具名称" min-width="140" align="center" />
      <el-table-column label="图标" min-width="120" align="center">
        <template #default="scope">
           <div style="display: flex; justify-content: center;">
             <el-avatar :src="scope.row.avatar" :size="80"></el-avatar>
           </div>
        </template>
      </el-table-column>
      <el-table-column prop="category" label="应用编码" min-width="120" align="center"  />
      <el-table-column prop="formType" label="接入方式" width="140" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.formType === '0' ? 'primary' : 'info'">
            {{ scope.row.formType === '0' ? '内嵌' : '外链' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="appUrl" label="页面地址" min-width="200" show-overflow-tooltip align="center" />
      <el-table-column prop="seq" label="排序号" min-width="120" align="center" />
      <el-table-column label="启用状态" min-width="120" align="center">
        <template #default="scope">
           <el-check-tag checked type="success" v-if="scope.row.status === '0'">启用</el-check-tag>
           <el-check-tag checked type="info" v-else>禁用</el-check-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180px">
        <template #default="scope">
          <el-button type="primary" link @click="handleEdit(scope.row)" :icon="Edit">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)" :icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑/新增对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增工具' : '编辑工具'"
      width="550px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 500px"
      >
        <el-form-item label="工具名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入工具名称" />
        </el-form-item>
        <el-form-item label="图标" prop="avatar">
           <el-upload
            class="avatar-uploader"
            action="#"
            :http-request="uploadImage"
            :show-file-list="false"
            accept="image/*"
          >
            <img v-if="form.avatar" :src="form.avatar" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="应用编码" prop="category">
          <el-input v-model="form.category" placeholder="请输入应用编码" />
        </el-form-item>
        <el-form-item label="接入方式" prop="formType">
          <el-radio-group v-model="form.formType">
            <el-radio label="0">内嵌</el-radio>
            <el-radio label="1">外链</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="页面地址" prop="appUrl">
          <el-input v-model="form.appUrl" placeholder="请输入页面地址" />
        </el-form-item>
        <el-form-item label="排序号" prop="seq">
          <el-input-number v-model="form.seq" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
           <el-switch v-model="form.status"  active-value="0" inactive-value="1" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';
import { getAppList, addApp, updateApp, deleteApp, uploadOSS } from '@/api/admin';

// 表格数据
const toolList = ref([]);
const loading = ref(false);

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);

// 表单数据
const form = reactive({
  id: '',
  avatar: '',  
  name: '',
  appUrl: '',  
  formType: '0', // 接入方式：0-内嵌 1-外链
  seq: 0,      
  status: '0',   
  category: '', 
  storeType: 'Tool',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入工具名称', trigger: 'blur' }],
  avatar: [{ required: true, message: '请上传图标', trigger: 'change' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  formType: [{ required: true, message: '请选择接入方式', trigger: 'change' }],
  appUrl: [{ required: true, message: '请输入页面地址', trigger: 'blur' }],
};

// 初始化数据
onMounted(() => {
  fetchToolList();
});

// 获取工具列表
const fetchToolList = async () => {
  loading.value = true;
  try {
    const res = await getAppList({
      storeType: 'Tool'
    });
    toolList.value = res.data || [];
  } catch (error) {
  } finally {
    loading.value = false;
  }
};

// 处理新增
const handleAdd = () => {
  dialogType.value = 'add';
  resetForm();
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row) => {
  dialogType.value = 'edit';
  // 直接使用行数据填充表单
  Object.assign(form, JSON.parse(JSON.stringify(row)));
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除「${row.name}」吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const res = await deleteApp(row.id);
    if (res.code === 200) {
      ElMessage.success('删除成功');
      fetchToolList();
    }
  }).catch(() => {});
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.id = '';
  form.name = '';
  form.avatar = '';
  form.category = '';
  form.formType = '0';
  form.appUrl = '';
  form.seq = 0;
  form.status = '0';
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const submitData = {
          storeType: 'Tool',
          name: form.name,
          avatar: form.avatar,
          category: form.category,
          appUrl: form.appUrl,
          seq: form.seq,
          status: form.status,
          formType: form.formType,
        }; 
        let res;
        if (dialogType.value === 'add') {
          res = await addApp(submitData);
        } else {
          res = await updateApp({
            ...submitData,
            id: form.id
          });
        }
        
        if (res.code === 200) {
          ElMessage.success(dialogType.value === 'add' ? '新增成功' : '更新成功');
          dialogVisible.value = false;
          fetchToolList();
        }
      } catch (error) {
        console.error('提交失败:', error);
      }
    }
  });
};

// 处理头像上传成功
const handleAvatarSuccess = (res) => {
  if (res.code === 200) {
    form.avatar = res.data.url; // 使用返回的url
  }
};

// 自定义上传方法
const uploadImage = async (options) => {
  try {
    const formData = new FormData();
    formData.append('file', options.file);
    const res = await uploadOSS(formData);
    if (res.code === 200) {
      handleAvatarSuccess(res);
      ElMessage.success('上传成功');
    }
  } catch (error) {
    console.error('上传失败:', error);
  }
};
</script>

<style scoped>
.tool-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  //box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0 20px 20px;
}

.action-bar {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
}

.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
   border-radius: 50%;
  display: block;
}

:deep(.table-row) {
  height: 60px;
}

:deep(.el-button--primary) {
  font-weight: bold;
}
</style>
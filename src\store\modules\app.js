import { getAppList } from '@/api/home';

export default {
  namespaced: true,
  state: {
    appList: [],
    menuData: [],
    isLoading: false,
    isLoaded: false
  },
  getters: {
    appList: state => state.appList,
    menuData: state => state.menuData,
    isLoading: state => state.isLoading,
    isLoaded: state => state.isLoaded,
    
    // 根据分类获取工具
    getToolsByCategory: (state) => (categoryIndex) => {
      const category = state.appList.find(cat => cat.index === categoryIndex);
      return category ? category.tools : [];
    },
    
    // 获取所有工具（扁平化）
    allTools: state => {
      return state.appList.reduce((tools, category) => {
        return tools.concat(category.tools || []);
      }, []);
    }
  },
  mutations: {
    setLoading(state, loading) {
      state.isLoading = loading;
    },
    setAppList(state, appList) {
      state.appList = appList;
      state.isLoaded = true;
    },
    setMenuData(state, menuData) {
      state.menuData = menuData;
    }
  },
  actions: {
    async loadAppData({ commit, state }) {
      // 如果已经加载过或正在加载中，则不重复加载
      if (state.isLoaded || state.isLoading) {
        return;
      }
      
      commit('setLoading', true);
      
      try {
        const response = await getAppList();
        
        if (response.data && Array.isArray(response.data)) {
          // 处理应用列表数据
          const appList = [];
          const menuData = [];
          
          response.data.forEach(category => {
            if (category.childrenAgent && Array.isArray(category.childrenAgent)) {
              // 应用列表数据
              const categoryData = {
                id: category.id,
                index: category.storeType,
                name: category.name,
                tools: category.childrenAgent.map(app => ({
                  id: app.id,
                  name: app.name,
                  description: app.description,
                  icon: app.avatar,
                  link: app.appUrl,
                  color: app.colour,
                  isPlatformTool: app.type === '1'
                }))
              };
              appList.push(categoryData);
              
              // 菜单数据
              const menuItem = {
                index: category.storeType,
                title: category.name,
                icon: category.avatar
              };
              menuData.push(menuItem);
            }
          });
          
          commit('setAppList', appList);
          commit('setMenuData', menuData);
        }
      } catch (error) {
        console.error('获取应用列表数据失败:', error);
      } finally {
        commit('setLoading', false);
      }
    }
  }
}; 
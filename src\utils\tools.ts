/**
 * @description: 去除markdown格式，只保留文本
 * @param {String} markdown 传入内容
 * @return:
 */
export function stripMarkdown(markdown='') {
  return markdown
      // 去除代码块（```...```）
      .replace(/```\s*[\s\S]*?```/g, '')
      // 去除内联代码（`code`）
      .replace(/`[^`\n]+`/g, '')
      // 去除图片标记 ![alt](url)
      .replace(/!\[.*?\]\(.*?\)/g, '')
      // 去除链接标记 [text](url)
      .replace(/\[(.*?)\]\(.*?\)/g, '$1')
      // 去除标题（#）
      .replace(/^#{1,6}\s+/gm, '')
      // 去除引用符号（>）
      .replace(/^\s*>+\s?/gm, '')
      // 去除无序列表符号（-、*、+）
      .replace(/^\s*([-*+])\s+/gm, '')
      // 去除有序列表符号（1. 2. 等）
      .replace(/^\s*\d+\.\s+/gm, '')
      // 去除加粗（**text** 或 __text__）
      .replace(/(\*\*|__)(.*?)\1/g, '$2')
      // 去除斜体（*text* 或 _text_）
      .replace(/(\*|_)(.*?)\1/g, '$2')
      // 去除删除线（~~text~~）
      .replace(/~~(.*?)~~/g, '$1')
      // 去除表格分隔符（| 和 ---）
      .replace(/^\s*\|.*?\|\s*$/gm, '')
      .replace(/^\s*-{3,}\s*$/gm, '')
      // 替换多个空行为单个换行
      .replace(/\n{2,}/g, '\n')
      // 去除开头结尾空白字符
      .trim();
}

/**
 * 将时间转换为指定格式的字符串
 * @param {Date|number|string} date - Date对象、时间戳或可被new Date()解析的字符串
 * @param {string} formatStr - 格式字符串，默认为 "YYYY-MM-DD HH:mm:ss"
 * @returns {string}
 */
export function formatDate(date, formatStr = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';

  // 如果是数字且长度小于等于10位，认为是秒级时间戳，转成毫秒
  if (typeof date === 'number' && String(date).length <= 10) {
      date = date * 1000;
  }

  const d = new Date(date);

  if (isNaN(d.getTime())) {
      console.warn('无效的日期:', date);
      return '';
  }

  const map = {
      YYYY: d.getFullYear(),
      MM: String(d.getMonth() + 1).padStart(2, '0'),
      DD: String(d.getDate()).padStart(2, '0'),
      HH: String(d.getHours()).padStart(2, '0'),
      mm: String(d.getMinutes()).padStart(2, '0'),
      ss: String(d.getSeconds()).padStart(2, '0')
  };

  // 替换模板中的占位符
  return formatStr.replace(/YYYY|MM|DD|HH|mm|ss/g, matched => map[matched]);
}

/**
 * @description: 文件大小单位换算
 * @param {Number} size 文件大小 (byte)
 * @return:
 */
export function getFileSize(size) {
    if (size <= 0) return '--'
    var num = 1024.00 // byte
    if (size < num) {
        return size + 'B'
    }
    if (size < Math.pow(num, 2)) {
        return (size / num).toFixed(1) + 'K'
    }
    if (size < Math.pow(num, 3)) {
        return (size / Math.pow(num, 2)).toFixed(1) + 'M'
    }
    if (size < Math.pow(num, 4)) {
        return (size / Math.pow(num, 3)).toFixed(1) + 'G'
    }
    return (size / Math.pow(num, 4)).toFixed(1) + 'T'
}

export function formatFile(format) {
    if (format == null) {
        return 'other'
    }
    //   zip: ['tar', 'zip', '7z', 'gz', 'rar']
    const formatType = {
        video: ['mp4'],
        audio: ['mp3', 'wav', 'ogg', 'm4a'],
        image: ['bmp', 'jpg', 'jpeg', 'png', 'tif', 'gif', 'pcx', 'tga', 'exif', 'fpx', 'svg', 'psd', 'cdr', 'pcd', 'dxf', 'ufo', 'eps', 'ai', 'raw', 'wmf', 'webp'],
        doc: ['doc', 'docx'],
        ppt: ['ppt', 'pptx'],
        pdf: ['pdf'],
        xls: ['xls', 'xlsx', 'csv'],
        zip: ['tar', 'zip', '7z', 'gz', 'rar'],
        txt: ['txt']

    }
    for (const key in formatType) {
        if (formatType[key].indexOf(format.toLowerCase()) > -1) {
            return key
        }
    }
    return 'other'
}

/**
 * @description: 分割文本为句子，用于实时语音合成
 * @param {string} text 需要分割的文本
 * @returns {object} 返回包含已完成句子列表和剩余文本的对象
 */
export function splitTextToSentences(text: string): { sentences: string[]; remaining: string } {
  if (!text || typeof text !== 'string') {
    return { sentences: [], remaining: '' }
  }

  // 定义主要分割符（句子结束符）和次要分割符（逗号等）
  const majorDelimiters = /[。！？；…\n]/
  const minorDelimiters = /[，]/

  const sentences: string[] = []
  let remaining = text
  let lastIndex = 0

  // 首先处理主要分割符（句号、感叹号、问号等）
  for (let i = 0; i < text.length; i++) {
    const char = text[i]

    if (majorDelimiters.test(char)) {
      // 找到主要分割符，提取完整句子
      const sentence = text.slice(lastIndex, i + 1).trim()
      if (sentence.length > 0) {
        sentences.push(sentence)
      }
      lastIndex = i + 1
    }
  }

  // 处理逗号分割（只有在没有主要分割符的情况下才处理逗号）
  if (sentences.length === 0) {
    for (let i = 0; i < text.length; i++) {
      const char = text[i]

      if (minorDelimiters.test(char)) {
        // 找到逗号，提取句子片段
        const sentence = text.slice(lastIndex, i + 1).trim()
        if (sentence.length > 0) {
          sentences.push(sentence)
        }
        lastIndex = i + 1
      }
    }
  }

  // 剩余文本是从最后一个分割点到文本结尾
  remaining = text.slice(lastIndex).trim()

  return { sentences, remaining }
}

/**
 * @description: 检查文本是否包含完整句子（以分割符结尾）
 * @param {string} text 需要检查的文本
 * @returns {boolean} 是否包含完整句子
 */
export function hasCompleteSentence(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false
  }

  // 检查是否以分割符结尾
  return /[。！？；…\n，]$/.test(text.trim())
}

/**
 * @description: 强制分割文本（用于流式输出结束时处理剩余文本）
 * @param {string} text 需要分割的文本
 * @returns {string[]} 分割后的句子数组
 */
export function forceSplitText(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return []
  }

  const sentences: string[] = []

  // 首先尝试按主要分割符分割
  if (/[。！？；…\n]/.test(text)) {
    let lastIndex = 0
    for (let i = 0; i < text.length; i++) {
      const char = text[i]
      if (/[。！？；…\n]/.test(char)) {
        const sentence = text.slice(lastIndex, i + 1).trim()
        if (sentence.length > 0) {
          sentences.push(sentence)
        }
        lastIndex = i + 1
      }
    }
    // 处理剩余部分
    const remaining = text.slice(lastIndex).trim()
    if (remaining.length > 0) {
      sentences.push(remaining)
    }
  } else if (/，/.test(text)) {
    // 如果没有主要分割符，按逗号分割
    let lastIndex = 0
    for (let i = 0; i < text.length; i++) {
      const char = text[i]
      if (char === '，') {
        const sentence = text.slice(lastIndex, i + 1).trim()
        if (sentence.length > 0) {
          sentences.push(sentence)
        }
        lastIndex = i + 1
      }
    }
    // 处理剩余部分
    const remaining = text.slice(lastIndex).trim()
    if (remaining.length > 0) {
      sentences.push(remaining)
    }
  } else {
    // 如果都没有，按长度分割（每30个字符一段）
    const maxLength = 30
    for (let i = 0; i < text.length; i += maxLength) {
      const segment = text.slice(i, i + maxLength).trim()
      if (segment.length > 0) {
        sentences.push(segment)
      }
    }
  }

  return sentences
}
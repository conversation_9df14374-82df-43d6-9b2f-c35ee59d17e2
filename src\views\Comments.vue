<template>
  <div class="comments-container" ref="containerRef">
    <agent-history-entry
      category="CommAI"
      @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">学生评语助手</h1>
      <p class="subtitle">基于AI技术，一键生成个性化学生评语，精准描述学生特点</p>
      <div class="page-navigation">
        <el-button type="primary" class="navigate-button" @click="$emit('switch-component', 'batchComments')" size="large">
          批量生成学生评语
        </el-button>
      </div>
    </div>

    <div class="form-container">
      <el-form :model="formData" label-width="100px" class="comment-form" size="large">
        <div class="grade-section">
          <el-form-item label="学段/年级" required>
            <div class="grade-selection-container">
              <div class="stage-buttons">
                <el-button-group>
                  <el-button
                    v-for="item in gradeOptions"
                    :key="item.value"
                    :type="formData.selectedStage === item.value ? 'primary' : ''"
                    @click="selectStage(item.value)"
                    round
                  >
                    {{ item.label }}
                  </el-button>
                </el-button-group>
              </div>

              <div class="grade-buttons-container" v-if="formData.selectedStage">
                <span class="required-star">*</span>
                <div class="grade-buttons">
                  <el-button
                    v-for="item in currentGradeOptions"
                    :key="item.value"
                    :type="formData.selectedGrade === item.value ? 'primary' : 'default'"
                    @click="selectGrade(item.value)"
                    class="option-button"
                    round
                  >
                    {{ item.label }}
                  </el-button>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>

        <p style="margin-left: 60px; color: #909399; font-size: 14px; margin-bottom: 10px;text-align: left;"> 请至少选择一项表扬项或不足项 </p>
        <el-form-item label="表扬项">
          <div class="tag-buttons-container">
            <el-button
              v-for="item in praiseOptions"
              :key="item.value"
              :type="formData.praiseItems.includes(item.value) ? 'success' : 'default'"
              @click="togglePraiseItem(item.value)"
              class="tag-button"
              round
            >
              {{ item.label }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="不足项">
          <div class="tag-buttons-container">
            <el-button
              v-for="item in criticismOptions"
              :key="item.value"
              :type="formData.criticismItems.includes(item.value) ? 'warning' : 'default'"
              @click="toggleCriticismItem(item.value)"
              class="tag-button"
              round
            >
              {{ item.label }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="生成字数" required>
          <div class="stage-buttons">
            <el-button-group>
              <el-button
                v-for="item in wordCountOptions"
                :key="item.value"
                :type="formData.wordCount === item.value ? 'primary' : ''"
                @click="formData.wordCount = item.value"
                round
              >
                {{ item.label }}
              </el-button>
            </el-button-group>
          </div>
        </el-form-item>

        <el-form-item label="学生姓名">
          <el-input style="width: 400px; height: 40px;" v-model="formData.studentName" placeholder="可选，填写后评语中会包含学生姓名" round />
        </el-form-item>

        <div style="display: flex; justify-content: center; width: 100%;" v-if="!loading">
            <el-button
            :type="loading ? 'warning' : 'primary'"
            size="large"
            v-click-throttle="handleGenerate"
            round
          >
            {{ loading ? '中止生成' : '生成评语' }}
          </el-button>
          </div>
      </el-form>
    </div>

    <ai-loading :visible="showAiLoading" />

    <div v-if="commentResult" class="result-container">
      <div class="result-content">
        <div class="section-title-container">
          <div class="section-title">学生评语：</div>
          <div class="action-icons">
            <el-icon style="cursor: pointer;" @click="copyComment"><CopyDocument /></el-icon>
            <div class="button-group">
              <el-button
                v-if="!loading && commentResult"
                type="warning"
                @click="handleRegenerate"
                :disabled="loading"
                round
                class="regenerate-button"
                size="large"
              >
                <el-icon class="regenerate-icon"><Refresh /></el-icon>
                重新生成评语
              </el-button>
              <agent-save-button
                v-if="!loading && commentResult"
                category="CommAI"
                :user-question="savePrompt()"
                :model-answer="commentResult"
                :chat-service="chatService"
                :already-saved="alreadySaved"
                @save-success="alreadySaved = true"
              />
            </div>
          </div>
        </div>
        <div class="comment-text" v-html="renderedMarkdown(commentResult)"></div>
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;" v-if="loading && commentResult">
          <el-button
            type="warning"
            size="large"
            v-click-throttle="handleGenerate"
            round
          >中止生成 </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {ref, computed, nextTick, watch, onMounted, inject} from 'vue'
import { CopyDocument, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import MarkdownIt from "markdown-it"
import {fetchEventSource} from '@microsoft/fetch-event-source'
import { useStore } from 'vuex'
import AgentSaveButton from '@/components/common/AgentSaveButton.vue'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import AiLoading from '@/components/common/AiLoading.vue'

const roleSystem = `
角色设定：你是一名经验丰富的教育工作者，擅长为不同年龄段的学生撰写全面、客观、有针对性的评语。
你的每一条评语都应该积极向上、措辞恰当、表达准确，既能鼓励学生的优点，也能委婉指出需要改进的地方。

## 评语要求
1. 根据学生的年龄段调整语言表达：
   - 小学阶段：使用简单易懂的词语，避免复杂句式，多用具体事例，语气要亲切活泼
   - 初中阶段：语言要通俗易懂，适当使用一些书面语，表达要清晰明确
   - 高中阶段：可以使用更丰富的词汇和句式，表达要更加成熟和理性
2. 字数必须严格控制在指定范围内，误差不超过±10字

## 输出要求
- 优先针对学生的表扬项展开描述，委婉提出需要改进的不足项
- 当没有提供学生姓名时，使用第二人称"你"进行评价，避免使用"小明"等默认名字
- 如果提供了学生姓名，使用学生名字时保持亲切感
- 使用丰富的形容词和具体例子，避免空洞的评价
- 使用适当的转折词和连接词，保持评语的连贯性和逻辑性
- 结尾应给予学生期望和鼓励
- 字数必须严格控制在指定范围内，误差不超过±10字
- 不要在评语结尾或任何位置添加"(200字)"、"（300字）"等字数标记

## 多样性要求
- 即使收到相同的选项和要求，也要确保每次生成的评语在表达方式、用词和句式上有明显差异
- 对于相同的表扬项或不足项，使用不同的形容词和例子进行描述
- 尝试使用多种不同的句式结构，如排比、设问、比喻等修辞手法
- 根据提供的随机种子和风格指导，调整评语的整体风格和结构
- 避免使用模板化、公式化的表达，每次生成都应该是独特的、个性化的

请根据以上要求，生成一份情感丰富、表达生动、有针对性且具有独特性的学生评语，并确保字数严格符合要求。
`

export default {
  name: 'Comments',
  components: {
    CopyDocument,
    Refresh,
    AgentSaveButton,
    AgentHistoryEntry,
    AiLoading
  },
  setup() {
    const trackingService = inject('trackingService');
    const store = useStore()
    const formData = ref({
      selectedStage: '小学',
      selectedGrade: '',
      praiseItems: [],
      criticismItems: [],
      wordCount: 200,
      studentName: ''
    })

    const commentResult = ref(null)
    const loading = ref(false)
    const reader = ref(null)
    const containerRef = ref(null)
    const chatService = ref(null) // 聊天服务实例
    const alreadySaved = ref(false) // 是否已保存当前对话

    // 学段选项
    const gradeOptions = [
      { value: '小学', label: '小学' },
      { value: '初中', label: '初中' },
      { value: '高中', label: '高中' }
    ]

    // 年级选项（按学段分组）
    const gradeMap = {
      '小学': [
        { value: '一年级', label: '一年级' },
        { value: '二年级', label: '二年级' },
        { value: '三年级', label: '三年级' },
        { value: '四年级', label: '四年级' },
        { value: '五年级', label: '五年级' },
        { value: '六年级', label: '六年级' }
      ],
      '初中': [
        { value: '初一', label: '初一' },
        { value: '初二', label: '初二' },
        { value: '初三', label: '初三' }
      ],
      '高中': [
        { value: '高一', label: '高一' },
        { value: '高二', label: '高二' },
        { value: '高三', label: '高三' }
      ]
    }

    // 当前可选的年级选项
    const currentGradeOptions = computed(() => {
      if (!formData.value.selectedStage) return []
      return gradeMap[formData.value.selectedStage] || []
    })

    // 选择学段
    const selectStage = (stage) => {
      formData.value.selectedStage = stage
      formData.value.selectedGrade = ''
    }

    // 选择年级
    const selectGrade = (grade) => {
      formData.value.selectedGrade = grade
    }

    // 表扬项选项
    const praiseOptions = [
      { value: '勤奋好学', label: '勤奋好学' },
      { value: '思维敏捷', label: '思维敏捷' },
      { value: '成绩优异', label: '成绩优异' },
      { value: '进步显著', label: '进步显著' },
      { value: '责任心强', label: '责任心强' },
      { value: '尊敬师长', label: '尊敬师长' },
      { value: '文明礼貌', label: '文明礼貌' },
      { value: '团结友爱', label: '团结友爱' },
      { value: '乐于助人', label: '乐于助人' },
      { value: '乐观开朗', label: '乐观开朗' },
      { value: '有领导力', label: '有领导力' },
      { value: '自律性强', label: '自律性强' },
      { value: '书写整洁', label: '书写整洁' },
      { value: '热爱劳动', label: '热爱劳动' },
      { value: '喜欢运动', label: '喜欢运动' },
      { value: '多才多艺', label: '多才多艺' },
    ]

    // 不足项选项
    const criticismOptions = [
      { value: '缺乏自律', label: '缺乏自律' },
      { value: '容易分心', label: '容易分心' },
      { value: '巩固基础', label: '巩固基础' },
      { value: '仍需努力', label: '仍需努力' },
      { value: '加强表达', label: '加强表达' },
      { value: '偏科明显', label: '偏科明显' },
      { value: '书写不规范', label: '书写不规范' }
    ]

    // 字数选项
    const wordCountOptions = [
      { value: 50,  label: '50字'  },
      { value: 100, label: '100字' },
      { value: 200, label: '200字' },
      { value: 300, label: '300字' },
      { value: 500, label: '500字' }
    ]

    const showAiLoading = computed(() => loading.value && !commentResult.value)

    // 表单验证
    const isFormValid = computed(() => {
      return formData.value.selectedStage &&
             formData.value.selectedGrade &&
             (formData.value.praiseItems.length > 0 || formData.value.criticismItems.length > 0)
    })

    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };

    // 监听评语结果变化，触发滚动
    watch(commentResult, () => {
      scrollToBottom();
    });

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('CommAI', '学生评语助手会话')
          console.log('学生评语助手重新初始化完成')
        }
      } catch (error) {
        console.error('学生评语助手重新初始化错误:', error)
      }
    }

    // 初始化聊天服务
    onMounted(async () => {
      try {
        chatService.value = new AgentChatService(store)
        await chatService.value.initialize('CommAI', '学生评语助手会话')
        console.log('学生评语助手初始化完成')
      } catch (error) {
        console.error('初始化错误:', error)
      }
    })

    // 构建提示词方法
    const buildPrompt = () => {
      // 生成随机种子，确保每次生成的评语都有差异
      const randomSeed = Math.floor(Math.random() * 10000);

      // 随机选择不同的表达风格
      const styles = [
        "温暖鼓励型", "客观分析型", "幽默活泼型", "严谨专业型",
        "生动形象型", "简洁明了型", "细致入微型", "启发思考型"
      ];
      const randomStyle = styles[Math.floor(Math.random() * styles.length)];

      // 随机选择不同的句式结构
      const structures = [
        "先总体评价，再具体分析", "先具体表现，再总结评价",
        "以问题引入，逐步展开", "以故事形式展开", "以比喻开头，逐步深入"
      ];
      const randomStructure = structures[Math.floor(Math.random() * structures.length)];

      let prompt = `请为一名${formData.value.selectedStage}${formData.value.selectedGrade}的学生写一份学生评语，字数严格控制在${formData.value.wordCount}字左右，误差不超过±10字。`;

      if (formData.value.studentName) {
        prompt += `学生名字是"${formData.value.studentName}"，`;
      }

      prompt += `表扬项：${formData.value.praiseItems.join('、')}。`;

      if (formData.value.criticismItems.length > 0) {
        prompt += `需要提醒改进的地方：${formData.value.criticismItems.join('、')}。`;
      }

      prompt += `请确保评语语言亲切自然，针对性强，富有教育意义。不要在评语结尾或任何位置添加"(${formData.value.wordCount}字)"等字数标记。`;

      // 添加随机性指令
      prompt += `请使用"${randomStyle}"的表达风格，采用"${randomStructure}"的结构，并使用随机种子${randomSeed}确保评语的独特性和多样性。即使收到相同的选项，也请生成与之前不同的表达和用词。`;

      return prompt;
    }

    const savePrompt = () => {
      let prompt = ``

      prompt += `年级: ${formData.value.selectedGrade}，`

      // 按照 praiseOptions 的顺序筛选并拼接表扬项
      const sortedPraiseItems = praiseOptions
        .map(option => option.value) // 获取选项的值
        .filter(value => formData.value.praiseItems.includes(value)); // 筛选出用户选中的项

      if (sortedPraiseItems.length > 0) {
        prompt += `表扬项：${sortedPraiseItems.join('、')}，`
      }

      // 按照 criticismOptions 的顺序筛选并拼接不足项
       const sortedCriticismItems = criticismOptions
        .map(option => option.value) // 获取选项的值
        .filter(value => formData.value.criticismItems.includes(value)); // 筛选出用户选中的项

      if (sortedCriticismItems.length > 0) {
        prompt += `不足项：${sortedCriticismItems.join('、')}，`
      }

      if (formData.value.wordCount) {
        prompt += `生成字数：${formData.value.wordCount}字，`
      }

      if (formData.value.studentName) {
        prompt += `学生姓名："${formData.value.studentName}"`
      }

      return prompt
    }

    // 重新生成评语（高随机性）
    const handleRegenerate = async () => {
      // 调用handleGenerate并传入true表示使用高随机性
      await handleGenerate(true);
    }

    // 生成评语
    const handleGenerate = async (highRandomness = false) => {
      if (!formData.value.selectedStage || !formData.value.selectedGrade) {
        ElMessage.warning('请选择学段/年级')
        return
      }
      if (formData.value.praiseItems.length == 0 && formData.value.criticismItems.length == 0) {
        ElMessage.warning('请选择至少一项表扬项或不足项')
        return
      }

      if (loading.value && reader.value) {
        await cancelStream()
        return
      }

      commentResult.value = null
      loading.value = true
      alreadySaved.value = false // 重置保存状态

      // 创建AbortController用于取消请求
      const controller = new AbortController();
      reader.value = controller; // 将 controller 赋值给 reader 用于取消

      try {
        // 构建提示词
        let prompt = buildPrompt(highRandomness)

        // 构建请求体
        let message = []
        message.push({
          "role": "user",
          "content": prompt
        })

        // 根据是否需要高随机性来设置温度值
        // 初次生成时使用较低的随机性，重新生成时使用较高的随机性
        let temperature, top_p;

        if (highRandomness) {
          // 高随机性：重新生成时使用
          temperature = 1.0 + Math.random() * 0.8; // 1.0-1.8之间
          top_p = 0.9;
        } else {
          // 低随机性：初次生成时使用
          temperature = 0.7 + Math.random() * 0.3; // 0.7-1.0之间
          top_p = 0.7;
        }

        let requestBody = {
          messages: message,
          stream: true,
          temperature: temperature,
          modelType: 'open',
          formType: 'comm_aI',
          top_p: top_p
        }

        let accumulatedText = '';
        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`
        fetchEventSource(apiUrl,{
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal, // 传递 signal 用于取消
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
              loading.value = false; // 出错时停止加载状态
              reader.value = null;
              throw new Error(`HTTP error! status: ${response.status}`);
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              loading.value = false;
              reader.value = null;
              throw new Error(msg.data);
            }

            const data = msg.data;

            try {
              const jsonData = JSON.parse(data);

              if (jsonData.error) {
                loading.value = false;
                reader.value = null;
                return;
              }

              if (jsonData.choices && jsonData.choices[0]) {
                // 根据您的API响应结构调整，这里假设与MathGPT类似
                const delta_content = jsonData.choices[0].message?.content;
                if (delta_content) {
                  accumulatedText += delta_content;
                  commentResult.value = accumulatedText;
                  scrollToBottom();
                }
              }
            } catch (error) {
              console.error('JSON parse error:', error);
            }
          },
          async onclose() {
            console.log('连接关闭');
            // onclose 可能在收到所有数据后触发
            if (loading.value) { // 只有在仍在加载时才设置，避免覆盖[DONE]设置的状态
              loading.value = false;
              reader.value = null;
            }
          },
          onerror(err) {
            console.error('Stream error:', err);
            if (err.name !== 'AbortError') {
            } else {
              console.log('Stream aborted by user.');
              // 用户取消，状态已在 cancelStream 中处理
            }
            // 确保状态被重置，即使在AbortError情况下，以防万一
            loading.value = false;
            reader.value = null;
          }
        });

      } catch (error) {
        // 捕获 fetchEventSource 启动前的错误 或 onopen 中的同步错误
        console.error(error);
        loading.value = false;
        reader.value = null; // 确保清理
      }
      trackingService.trackEvent('CommAI')
    }

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          // reader.value 现在是 AbortController
          reader.value.abort('用户取消了操作');
          console.log('Abort signal sent.');
        } catch (error) {
          console.error('取消流式输出错误:', error);
        } finally {
          // 立即重置状态，确保按钮恢复
          loading.value = false;
          reader.value = null; // 清理 controller 引用
        }
      }
    }

    // Markdown渲染
    const renderedMarkdown = (markdownContent) => {
      if (!markdownContent) return

      // 移除字数标记，如"(200字)"、"（200字）"等
      const cleanedContent = markdownContent.replace(/[（(]\d+字[)）]/g, '')

      const md = new MarkdownIt()
      const htmlContent = md.render(cleanedContent)
      return htmlContent
    }

    // 复制评语
    const copyComment = () => {
      if (!commentResult.value) return

      // 移除字数标记，如"(200字)"、"（200字）"等
      const cleanedContent = commentResult.value.replace(/[（(]\d+字[)）]/g, '')

      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div')
      tempElement.innerHTML = renderedMarkdown(cleanedContent)

      // 获取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText

      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = textContent
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 使用现代的Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          // 对于支持Clipboard API的现代浏览器
          navigator.clipboard.writeText(textContent)
            .then(() => {
              ElMessage.success('评语已复制到剪贴板')
            })
            .catch(err => {
              console.error('复制失败:', err)
              // 回退到旧方法
              document.execCommand('copy')
              ElMessage.success('评语已复制到剪贴板')
            })
        } else {
          // 回退到旧方法
          document.execCommand('copy')
          ElMessage.success('评语已复制到剪贴板')
        }
      } catch (err) {
        console.error('复制失败:', err)
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }

    // 切换表扬项选中状态
    const togglePraiseItem = (value) => {
      const index = formData.value.praiseItems.indexOf(value);
      if (index === -1) {
        // 如果不存在则添加
        formData.value.praiseItems.push(value);
      } else {
        // 如果已存在则移除
        formData.value.praiseItems.splice(index, 1);
      }
    }

    // 切换不足项选中状态
    const toggleCriticismItem = (value) => {
      const index = formData.value.criticismItems.indexOf(value);
      if (index === -1) {
        // 如果不存在则添加
        formData.value.criticismItems.push(value);
      } else {
        // 如果已存在则移除
        formData.value.criticismItems.splice(index, 1);
      }
    }

    return {
      formData,
      gradeOptions,
      currentGradeOptions,
      praiseOptions,
      criticismOptions,
      wordCountOptions,
      commentResult,
      loading,
      isFormValid,
      handleGenerate,
      handleRegenerate,
      renderedMarkdown,
      containerRef,
      copyComment,
      CopyDocument,
      Refresh,
      togglePraiseItem,
      toggleCriticismItem,
      selectStage,
      selectGrade,
      chatService,
      buildPrompt,
      savePrompt,
      alreadySaved,
      reinitializeChatSession,
      showAiLoading
    }
  }
}
</script>

<style scoped>
.comments-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "✨";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 255, 0.08);
}

.comment-form {
  width: 100%;
  margin: 0 auto;
}

.full-width {
  width: 100%;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-content {
  text-align: left;
  padding: 2rem;
  position: relative;
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.comment-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.25s ease;
  background-color: rgba(44, 134, 255, 0.1);
  color: #2c86ff;
  width: 38px;
  height: 38px;
  padding: 0;
}

.copy-btn:hover {
  transform: translateY(-2px);
  background-color: rgba(44, 134, 255, 0.2);
  box-shadow: 0 4px 12px rgba(44, 134, 255, 0.2);
}

.copy-btn:active {
  transform: translateY(0);
}

.button-group-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.option-button {
  margin-right: 0;
  transition: all 0.3s;
}

.option-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tag-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-button {
  margin-left: 0 !important;
  margin-right: 0;
  transition: all 0.3s;
  min-width: 90px;
}

.tag-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selected-count {
  font-size: 0.9rem;
  color: #909399;
  margin-top: 12px;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-block;
}

/* 自定义el-radio-button样式，使其更圆润 */
:deep(.el-radio-button__inner) {
  padding: 10px 20px;
  border-radius: 20px !important;
  margin: 0 2px;
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 20px !important;
}

:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 20px !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
  padding: 0 15px;
}

:deep(.el-form-item) {
  margin-bottom: 25px;
}

/* 响应式适配 */
@media (max-width: 768px) {

  .comments-container {
    width: 100vw;
    padding:10px;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0;
  }

  .comment-text {
    font-size: 1rem;
  }

  .tag-buttons-container {
    gap: 8px;
  }

  .tag-button {
    font-size: 0.9rem;
    padding: 6px 10px;
    min-width: 70px;
  }

  :deep(.el-radio-button__inner) {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .button-group-container {
    gap: 8px;
  }

  .selection-hint {
    padding-left: 0;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .form-container {
    margin: 2rem 0;
    padding: 1rem;
  }

  .result-content {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .comment-text {
    padding: 1rem;
  }

  .button-group-container, .tag-buttons-container {
    gap: 6px;
  }

  .tag-button {
    font-size: 0.8rem;
    padding: 4px 8px;
    margin-bottom: 4px;
    min-width: 60px;
  }

  :deep(.el-radio-button__inner) {
    padding: 6px 10px;
    font-size: 0.8rem;
  }
}

.grade-section {
  margin-bottom: 20px;
}

.grade-selection-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.stage-buttons {
  display: flex;
  justify-content: flex-start;
}

.grade-buttons-container {
  position: relative;
  display: flex;
  align-items: center; /* 改为居中对齐 */
}

.grade-buttons {
  display: flex;
  flex-wrap: wrap;
}

.required-star {
  color: #f56c6c;
  font-size: 16px;
  font-weight: bold;
  margin-right: 5px;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}

.selection-hint {
  padding: 0 0 15px 120px;
}

.hint-warning {
  color: #e6a23c;
  font-size: 0.9rem;
  margin: 0;
}

.hint-success {
  color: #67c23a;
  font-size: 0.9rem;
  margin: 0;
}

.selection-count-divider {
  margin: 0 10px;
  color: #909399;
}

:deep(.el-alert__title) {
  font-size: 0.95rem;
}

:deep(.el-alert--warning.is-light) {
  background-color: #fdf6ec;
  border: 1px solid rgba(230, 162, 60, 0.2);
}

:deep(.el-alert--success.is-light) {
  background-color: #f0f9eb;
  border: 1px solid rgba(103, 194, 58, 0.2);
}

.page-navigation {
  margin-top: 1.5rem;
}

.navigate-button {
  padding: 0.8rem 1.5rem;
  font-size: 1.1rem;
  transition: all 0.3s;
}

.navigate-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 150, 0.15);
}

.action-icons {
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.regenerate-button {
  /* 使用 Element UI 的警告色 */
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
  border-radius: 22px;
  transition: all 0.3s;
  margin-right: 12px;
  font-weight: 600;
  padding: 12px 24px;
  height: auto;
}

.regenerate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(230, 162, 60, 0.3);
  background-color: #d48f29; /* 深一点的警告色 */
  border-color: #d48f29;
}

.regenerate-button:active:not(:disabled) {
  transform: translateY(0);
}

.regenerate-icon {
  margin-right: 8px;
  font-size: 16px;
  vertical-align: middle;
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
  }

  .regenerate-button {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 18px;
    margin-right: 0;
  }

  .regenerate-icon {
    margin-right: 6px;
    font-size: 14px;
  }
}
</style>

<template>
  <div class="article-container" ref="containerRef">
    <agent-history-entry
        category="Composition"
        @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">AI写作文</h1>
      <p class="subtitle">AI写作文能够迅速生成高质量的作文范文，有效提升写作效率，同时激发学生的创作灵感。</p>
    </div>

    <div class="form-container">

      <el-form :model="formData" label-width="120px" class="article-form" size="large">
        <el-form-item label="学段/年级" required>
          <div class="tag-buttons-container">

            <el-button
                :type="formData.period === '不限' ? 'primary' : 'default'"
                @click="selectOption('period', '不限')"
                round
            >
              不限
            </el-button>

            <el-button
                v-for="item in verseFilter.periodList "
                :key="item.value"
                :type="formData.period === item.value ? 'primary' : 'default'"
                @click="selectOption('period', item.value)"
                round
            >
              {{ item.label }}
            </el-button>
            <template v-if="formData.period !== '不限'">
              <el-button
                  v-for="grade in filteredGrades"
                  :key="grade"
                  :type="formData.grade === grade ? 'primary' : 'default'"
                  @click="selectOption('grade', grade)"
                  round
              >
                {{ grade }}
              </el-button>
            </template>
          </div>
        </el-form-item>
        <el-form-item label="作文题目" required>
          <el-input
              v-model="formData.title"
              :maxlength="30"
              placeholder="请输入作文题目"
              resize="none"
          />
        </el-form-item>

        <el-form-item label="写作要求" required>
          <el-input
              v-model="formData.briefDescription"
              type="textarea"
              :rows="6"
              placeholder="请输入写作的大致方向，文风要求等"
              resize="none"
          />
        </el-form-item>

        <el-form-item label="字数限制" required>
          <div class="style-buttons">
            <el-button-group>
              <el-button
                  v-for="item in wordCountOptions"
                  :key="item.value"
                  :type="(formData.wordCount === item.value&&type1=='Common') ? 'primary' : ''"
                  @click="formData.wordCount = item.value;type1='Common'"
                  round
              >
                {{ item.label }}
              </el-button>
              <el-button :type="type1==='Customize' ? 'primary' : ''" @click="handleCustomize">自定义</el-button>
            </el-button-group>
          </div>
        </el-form-item>
        <el-form-item label="自定义字数限制" v-if="type1==='Customize'" required label-width="140px" style="margin-left: -20px;">
          <el-input-number controls-position="right" v-model="formData.wordCount" :max="2000"
                           :min="10"></el-input-number> <span class="tips"><el-icon size="18"><InfoFilled /></el-icon>请输入2000以内的数字</span>
        </el-form-item>
        <div style="display: flex; justify-content: center; width: 100%;" v-if="!loading">
          <el-button
              :type="loading ? 'warning' : 'primary'"
              size="large"
              v-click-throttle="handleGenerate"
              :disabled="!isFormValid"
              round
          >
            {{ loading ? '中止生成' : (noticeResult ? '重新生成' : '开始生成') }}
          </el-button>
        </div>
      </el-form>
    </div>

    <ai-loading :visible="showAiLoading" />

    <div v-if="noticeResult" class="result-container">
      <div class="result-content">
        <div class="section-title-container">
          <div class="section-title">作文内容：</div>
          <div class="action-icons">
            <el-icon style="cursor: pointer;" @click="copyResult">
              <CopyDocument/>
            </el-icon>
            <agent-save-button
                v-if="!loading && noticeResult"
                category="Composition"
                :user-question="savePrompt()"
                :model-answer="noticeResult"
                :chat-service="chatService"
                :already-saved="alreadySaved"
                @save-success="alreadySaved = true"
            />
          </div>
        </div>
        <div class="comment-text" v-html="renderedMarkdown(noticeResult)"></div>
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;" v-if="loading && noticeResult">
          <el-button
            type="warning"
            size="large"
            v-click-throttle="handleGenerate"
            round
          >中止总结</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {ref, computed, nextTick, watch, onMounted, inject} from 'vue'
import {CopyDocument, InfoFilled} from '@element-plus/icons-vue'
import {ElLoading, ElMessage} from 'element-plus'
import MarkdownIt from "markdown-it"
import {fetchEventSource} from '@microsoft/fetch-event-source'
import {useStore} from 'vuex'
import AgentSaveButton from '@/components/common/AgentSaveButton.vue'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import {getVerseFilter} from "@/api/verse.js";
import AiLoading from '@/components/common/AiLoading.vue'

const roleSystem = `
# 角色设定
你是一名专业的作文辅导专家，熟悉各学段学生的认知水平与写作能力。

# 任务目标
根据用户指定的学生**学段（如小学、初中）和年级**，撰写一篇符合该阶段学生写作水平的范文。

# 撰写要求

1. **贴合学段**：作文内容应符合用户所选学段（如小学四年级、初中一年级等）的语言表达能力和思维水平。
2. **主题明确**：若用户提供作文主题或题目，则围绕该主题展开；若未提供，则自拟一个贴近学生生活、易于发挥的主题。
3. **积极向上**：内容应健康阳光，富有正能量，能够引导学生思考生活、情感、成长等话题。
4. **语言规范**：用词准确、语句通顺，结构清晰（开头—正文—结尾），能为学生提供良好的写作示范。
5. **激发兴趣**：通过生动的描写、有趣的细节或启发性的结尾，激发学生对写作的兴趣和思考。
# 内容要求
1.不要展示关于字数长度的内容（如：字数:498）

请确保作文内容积极向上，能够鼓励学生思考并促进他们对写作的兴趣。
`

export default {
  name: 'Composition',
  components: {
    InfoFilled,
    CopyDocument,
    AgentSaveButton,
    AgentHistoryEntry,
    AiLoading
  },
  setup() {
    const trackingService = inject('trackingService');
    const store = useStore()
    const formData = ref({
      period: '不限',
      grade: '不限',
      title: '',
      briefDescription: '',
      wordCount: 300
    })
    const type1 = ref('Common')
    // 自定义字数长度
    const handleCustomize = () => {
      type1.value = 'Customize'
      formData.value.wordCount = 100
    }

    const noticeResult = ref(null)
    const loading = ref(false)
    const reader = ref(null)
    const containerRef = ref(null)
    const chatService = ref(null) // 聊天服务实例
    const alreadySaved = ref(false) // 是否已保存当前对话
    // 根据当前选择的学段筛选年级
    const filteredGrades = computed(() => {
      if (formData.value.period === '不限') {
        return []
      }
      return verseFilter.value.gradeMap?.get(formData.value.period) || []
    })
    // 古诗筛选条件
    const verseFilter = ref({
      dynasties: [],
      authors: [],
      authorDynasty: [],
      themes: [],
      periods: [],
      gradeMap: new Map(),
      terms: [],
      periodList: []
    })

    const showAiLoading = computed(() => loading.value && !noticeResult.value)

    // 获取筛选条件
    const fetchFilters = async () => {
      try {
        const loadingInstance = ElLoading.service({
          target: '.form-container',
          text: '加载筛选项...'
        })

        const res = await getVerseFilter()
        console.log('----')
        console.log(res)
        if (res && res.code === 200) {
          const filterData = res.data || []
          let periods = []

          // 处理筛选数据
          filterData.forEach(item => {
            switch (item.type) {
              case 'period':
                // 处理学段和年级
                const gradeMap = new Map()
                // 提取小学和初中
                item.options.forEach(opt => {
                  if (opt.text !== '不限') {
                    periods.push(opt.text)

                    // 提取各学段下的年级
                    if (opt.options) {
                      const grades = opt.options
                          .filter(grade => grade.text !== '不限')
                          .map(grade => grade.text)
                      gradeMap.set(opt.text, grades)
                    }
                  }
                })
                verseFilter.value.periods = periods
                verseFilter.value.gradeMap = gradeMap
                break;
              case 'gradeStage':
                verseFilter.value.terms = item.options
                    .filter(opt => opt.text !== '不限')
                    .map(opt => opt.text)
                break;
            }
          })

          // 构建 periodList
          verseFilter.value.periodList = periods.map(period => ({
            value: period,
            label: period
          }))
          console.log(verseFilter.value.periodList)
        }

        loadingInstance.close()
      } catch (error) {
        console.error('获取筛选条件错误:', error)
      }
    }
    // 选择筛选选项并触发搜索
    const selectOption = (field, value) => {
      formData.value[field] = value

      if (field === 'dynasty') {
        if (value === '不限') {
          verseFilter.value.authors = verseFilter.value.authorDynasty.map(opt => opt.text)
        } else {
          verseFilter.value.authors = verseFilter.value.authorDynasty.filter((a) => a.label === value).map(opt => opt.text)
        }
      }

      // 当切换学段时处理年级选择
      if (field === 'period') {
        if (value === '不限') {
          // 如果选择了"不限"，则清空年级值
          formData.value.grade = '不限'
          // 同时清空学期
          formData.value.term = ''
        } else {
          // 如果选择了特定学段且有对应年级，则默认选中第一个年级
          const grades = verseFilter.value.gradeMap?.get(value) || []
          if (grades.length > 0) {
            formData.value.grade = grades[0]
          } else {
            formData.value.grade = '不限'
          }
          // 清空学期
          formData.value.term = ''
        }
      }

      // 当切换年级时重置学期
      if (field === 'grade') {
        formData.value.term = ''
      }


    }


    // 字数选项
    const wordCountOptions = [

      {value: 300, label: '300字'},
      {value: 500, label: '500字'},
      {value: 800, label: '800字'},
      {value: 1000, label: '1000字'}
    ]

    // 表单验证
    const isFormValid = computed(() => {
      return formData.value.title.trim() !== '' &&
          formData.value.briefDescription.trim() !== '' &&
          formData.value.wordCount
    })

    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };

    // 监听结果变化，触发滚动
    watch(noticeResult, () => {
      scrollToBottom();
    });

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('Composition', 'AI写作文智能体会话')
          console.log('AI写作文智能体重新初始化完成')
        }
      } catch (error) {
        console.error('AI写作文智能体重新初始化错误:', error)
      }
    }

    // 初始化聊天服务
    onMounted(async () => {
      try {
        chatService.value = new AgentChatService(store)

        await chatService.value.initialize('Composition', 'AI写作文智能体会话')
        await fetchFilters()
        console.log('AI写作文智能体初始化完成')
      } catch (error) {
        console.error('初始化错误:', error)
      }
    })

    // 构建提示词方法
    const buildPrompt = () => {
      let prompt = `请根据以下信息生成一份学生作文,学段/年级为${formData.value.period}${formData.value.grade}，作文题目《${formData.value.title}》，字数控制在${formData.value.wordCount}字左右。严格限制生成的内容长度为${formData.value.wordCount}字以内的内容，确保作文内容详细充实。`
      prompt += `\n\n写作要求：${formData.value.briefDescription}`
      return prompt
    }

    const savePrompt = () => {
      let prompt = ``
      if (formData.value.period) {
        prompt += `学段/年级：${formData.value.period}${formData.value.grade}，`
      }
      if (formData.value.title) {
        prompt += `作文题目：${formData.value.title}，`
      }

      if (formData.value.briefDescription) {
        prompt += `写作要求：${formData.value.briefDescription}，`
      }

      if (formData.value.wordCount) {
        prompt += `字数限制：${formData.value.wordCount}字`
      }

      return prompt
    }

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          // reader.value 现在是 AbortController
          reader.value.abort('用户取消了操作');
          console.log('Abort signal sent.');
        } catch (error) {
          console.error('取消流式输出错误:', error);
        } finally {
          // 立即重置状态，确保按钮恢复
          loading.value = false;
          reader.value = null; // 清理 controller 引用
        }
      }
    }

    // 复制通知结果
    const copyResult = () => {
      if (!noticeResult.value) return

      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div')
      tempElement.innerHTML = renderedMarkdown(noticeResult.value)

      // 获取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText

      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = textContent
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 使用现代剪贴板API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(textContent)
              .then(() => {
                ElMessage.success('作文内容已复制到剪贴板')
              })
              .catch((err) => {
                console.error('现代API复制失败:', err)
                // 降级使用旧方法
                document.execCommand('copy')
                ElMessage.success('作文内容已复制到剪贴板')
              })
        } else {
          // 降级使用旧方法
          document.execCommand('copy')
          ElMessage.success('作文内容已复制到剪贴板')
        }
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败')
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }

    // 生成通知
    const handleGenerate = async () => {
      if (!isFormValid.value) {
        ElMessage.error('请填写必填项')
        return
      }

      if (loading.value && reader.value) {
        await cancelStream()
        return
      }

      noticeResult.value = null
      loading.value = true
      alreadySaved.value = false // 重置保存状态

      // 创建AbortController用于取消请求
      const controller = new AbortController();
      reader.value = controller; // 将 controller 赋值给 reader 用于取消

      try {
        // 构建提示词
        let prompt = buildPrompt()

        // 构建请求体
        let message = []
        message.push({
          "role": "user",
          "content": prompt
        })

        let requestBody = {
          messages: message,
          modelType: 'agent',
          formType: 'Composition'
        }

        let accumulatedText = '';
        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`
        fetchEventSource(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal, // 传递 signal 用于取消
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
              loading.value = false; // 出错时停止加载状态
              reader.value = null;
              throw new Error(`HTTP error! status: ${response.status}`);
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              loading.value = false;
              reader.value = null;
              throw new Error(msg.data);
            }

            const data = msg.data;

            try {
              const jsonData = JSON.parse(data);

              if (jsonData.error) {
                loading.value = false;
                reader.value = null;
                return;
              }

              if (jsonData.choices && jsonData.choices[0]) {
                const delta_content = jsonData.choices[0].message?.content;
                if (delta_content) {
                  accumulatedText += delta_content;
                  noticeResult.value = accumulatedText;
                  scrollToBottom();
                }
              }

            } catch (error) {
              console.error('JSON parse error:', error);
            }
          },
          async onclose() {
            console.log('连接关闭');
            // onclose 可能在收到所有数据后触发
            if (loading.value) { // 只有在仍在加载时才设置，避免覆盖[DONE]设置的状态
              loading.value = false;
              reader.value = null;
            }
          },
          onerror(err) {
            console.error('Stream error:', err);
            if (err.name !== 'AbortError') {
            } else {
              console.log('Stream aborted by user.');
              // 用户取消，状态已在 cancelStream 中处理
            }
            // 确保状态被重置，即使在AbortError情况下，以防万一
            loading.value = false;
            reader.value = null;
          }
        });
      } catch (error) {
        console.error('请求错误:', error);
        loading.value = false;
        reader.value = null;
        ElMessage.error('请求出错，请重试');
      }
      trackingService.trackEvent('Composition')
    }

    // Markdown渲染
    const renderedMarkdown = (markdownContent) => {
      if (!markdownContent) return

      const md = new MarkdownIt()
      const htmlContent = md.render(markdownContent)
      return htmlContent
    }

    return {
      selectOption,
      filteredGrades,
      verseFilter,
      type1,
      formData,
      wordCountOptions,
      noticeResult,
      loading,
      isFormValid,
      handleGenerate,
      renderedMarkdown,
      containerRef,
      copyResult,
      CopyDocument,
      chatService,
      buildPrompt,
      savePrompt,
      alreadySaved,
      reinitializeChatSession,
      handleCustomize,
      showAiLoading
    }
  }
}
</script>

<style scoped>
.article-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "📢";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 255, 0.08);
}

.article-form {
  width: 100%;
  margin: 0 auto;
}

.style-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-content {
  text-align: left;
  padding: 2rem;
  position: relative;
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.comment-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

.action-icons {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .article-container {
    width: 100%;
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0;
  }

  .comment-text {
    font-size: 1rem;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .form-container {
    margin: 2rem 0;
    padding: 1rem;
  }

  .result-content {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .comment-text {
    padding: 1rem;
  }
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}
.tips{
  margin-left: 20px;
  color: #E6A23C;
  display: flex;
  align-items: center;
}
</style>

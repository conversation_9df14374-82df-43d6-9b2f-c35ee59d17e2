<template>
  <div
    ref="floatingBallRef"
    class="floating-ball-container"
    :class="{ 'menu-open': menuOpen, 'left-side': isLeftSide }"
    :style="containerStyle"
  >
    <!-- logo按钮 -->
    <div class="ball-content" @click="toggleMenu">
      <transition name="fade" mode="out-in">
        <img key="logo" src="@/assets/miao.png" alt="Logo" />
      </transition>
    </div>

    <!-- 左右切换按钮 -->
    <div 
      v-show="!menuOpen"
      class="switch-button" 
      :class="{ 'left-side': isLeftSide }"
      @click.stop="switchSide"
      :title="isLeftSide ? '移到右侧' : '移到左侧'"
    >
      <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path 
          :d="isLeftSide ? 'M14 7l5 5-5 5V7z' : 'M10 17l-5-5 5-5v10z'"
          fill="currentColor"
        />
      </svg>
    </div>

    <div class="floating-menu" :class="{ open: menuOpen }">
      <div
        v-for="(item, index) in menuItems"
        :key="item.id"
        class="menu-item"
        :style="getMenuItemStyle(index)"
        @click.stop="onMenuClick(item)"
      >
        <img :src="item.icon" :alt="item.name" class="menu-item-icon" />
        <span class="menu-item-text">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useRoute } from 'vue-router';

const store = useStore();
const router = useRouter();
const route = useRoute()

const floatingBallRef = ref(null);
const menuOpen = ref(false);
const isLeftSide = ref(false); // 是否在左侧
const windowWidth = ref(window.innerWidth); // 响应式窗口宽度
const mutationObserver = ref(null); // DOM变化观察器

const appList = computed(() => store.getters.appList);

const currentLink = computed(() => {
  if(route.path === '/iframe'){
    return route.query.url
  }
  return route.path
})

const menuItems = computed(() => {
  if (!appList.value || appList.value.length === 0) {
    return [];
  }
  const classroomTools = appList.value.find(c => c.index === 'ai-classroom')?.tools || [];
  const teachingTools = appList.value.find(c => c.index === 'ai-teaching')?.tools || [];
  const menuTools = [...classroomTools, ...teachingTools].filter(tool => tool.link !== currentLink.value);
  return menuTools.map(tool => {
    return {
      ...tool,
      name: tool.name.replace('AI思维导图', '思维导图'),
    }
  })
});

// 计算容器样式
const containerStyle = computed(() => {
  const topPosition = '50%';
  let leftPosition, transform;
  
  if (menuOpen.value) {
    // 菜单打开时的位置
    if (isLeftSide.value) {
      leftPosition = '105px'; // 向右平移
    } else {
      leftPosition = `${windowWidth.value - 180}px`; // 向左平移
    }
    transform = 'translateY(50%)';
  } else {
    // 菜单关闭时的位置
    if (isLeftSide.value) {
      leftPosition = '0'; // 左侧原始位置
    } else {
      leftPosition = `${windowWidth.value - 80}px`; 
    }
    transform = 'translateY(50%)';
  }
  
  return {
    top: topPosition,
    left: leftPosition,
    transform: transform,
    transition: 'left 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), transform 0.3s ease'
  };
});

const toggleMenu = () => {
  menuOpen.value = !menuOpen.value;
};

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  if (menuOpen.value && floatingBallRef.value && !floatingBallRef.value.contains(event.target)) {
    menuOpen.value = false;
  }
};

// 处理iframe内部点击事件（通过监听window焦点和iframe元素）
const handleIframeClick = () => {
  if (menuOpen.value) {
    menuOpen.value = false;
  }
};

// 监听页面失去焦点（当用户点击iframe时会触发）
const handleWindowBlur = () => {
  // 延迟一点时间，确保不是因为点击悬浮球而失去焦点
  setTimeout(() => {
    if (menuOpen.value) {
      menuOpen.value = false;
    }
  }, 100);
};

// 检测是否在iframe页面并设置监听
const setupIframeListeners = () => {
  // 为现有iframe设置监听
  const addIframeListeners = (iframe) => {
    // 监听iframe的鼠标事件
    iframe.addEventListener('mousedown', handleIframeClick);
    
    // 尝试监听iframe内部点击（需要等待加载完成）
    const handleLoad = () => {
      try {
        if (iframe.contentDocument) {
          iframe.contentDocument.addEventListener('click', handleIframeClick);
        }
      } catch (error) {
        console.log('Cross-origin iframe detected');
      }
    };
    
    if (iframe.complete) {
      handleLoad();
    } else {
      iframe.addEventListener('load', handleLoad);
    }
  };
  
  // 为现有的iframe设置监听
  const existingIframes = document.querySelectorAll('iframe');
  existingIframes.forEach(addIframeListeners);
  
  // 监听window焦点变化
  window.addEventListener('blur', handleWindowBlur);
  
  // 创建MutationObserver来监听新增的iframe
  mutationObserver.value = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // 检查是否是iframe元素
          if (node.tagName === 'IFRAME') {
            addIframeListeners(node);
          }
          // 检查子元素中是否有iframe
          const iframes = node.querySelectorAll && node.querySelectorAll('iframe');
          if (iframes) {
            iframes.forEach(addIframeListeners);
          }
        }
      });
    });
  });
  
  // 开始观察DOM变化
  mutationObserver.value.observe(document.body, {
    childList: true,
    subtree: true
  });
};

// 清理iframe监听器
const cleanupIframeListeners = () => {
  // 停止观察DOM变化
  if (mutationObserver.value) {
    mutationObserver.value.disconnect();
    mutationObserver.value = null;
  }
  
  // 清理现有iframe的监听器
  const iframes = document.querySelectorAll('iframe');
  iframes.forEach(iframe => {
    iframe.removeEventListener('mousedown', handleIframeClick);
    try {
      if (iframe.contentDocument) {
        iframe.contentDocument.removeEventListener('click', handleIframeClick);
      }
    } catch (error) {
      // 忽略跨域错误
    }
  });
  
  window.removeEventListener('blur', handleWindowBlur);
};

const openTool = async (tool) => {
  // 新增：根据工具确定所属分类并更新导航菜单
  let categoryIndex = '';
  
  // 从 store 中查找工具所属的分类
  const allCategories = store.getters.appList;
  for (const category of allCategories) {
    if (category.tools && category.tools.find(t => t.id === tool.id)) {
      categoryIndex = category.index;
      break;
    }
  }
  
  // 如果找到对应分类，更新导航菜单
  if (categoryIndex) {
    store.commit('ui/setActiveCategory', categoryIndex);
  }

  let link = tool.link;
  
  // 1. 内部路由（以/开头且不是http或https）
  if (link.startsWith('/') && !link.startsWith('http://') && !link.startsWith('https://')) {
      // 移除开头的斜杠，使其成为相对路径
      const relativePath = link.substring(1);
      router.push(relativePath);
      // 重置滚动位置到顶部
      window.scrollTo(0, 0);
  }
  
  // 2. HTTP/HTTPS链接
  if (link.startsWith('http://') || link.startsWith('https://')) {
    // 2.1 内部网站（包含chinatiye.cn）
    if (link.includes('chinatiye.cn')) {
      router.push({
        path: 'iframe',
        query: {
          url: link,
          title: tool.name
        }
      });
      window.scrollTo(0, 0);
    } else {
      // 2.2 外部网站（不包含chinatiye.cn）
      window.open(link, '_blank');
    }
  }
};

const getMenuItemStyle = (index) => {
  const angle = (360 / menuItems.value.length) * index - 90; 
  const radius = 90; 
  const x = radius * Math.cos((angle * Math.PI) / 180);
  const y = radius * Math.sin((angle * Math.PI) / 180);
  
  // 色系配置
  const colorConfigs = [
    { bg: 'linear-gradient(to bottom, #aacff3, #FFFFFF)', text: '#434040' }, 
    { bg: 'linear-gradient(to bottom, #dbf1de, #FFFFFF)', text: '#434040' },
    { bg: 'linear-gradient(to bottom, #d9c6f3, #FFFFFF)', text: '#434040' }, 
    { bg: 'linear-gradient(to bottom, #aacff3, #FFFFFF)', text: '#434040' }, 
    { bg: 'linear-gradient(to bottom, #dbf1de, #FFFFFF)', text: '#434040' },
    { bg: 'linear-gradient(to bottom, #d9c6f3, #FFFFFF)', text: '#434040' },
    { bg: 'linear-gradient(to bottom, #dbf1de, #FFFFFF)', text: '#434040' },
  ];

  const config = colorConfigs[index % colorConfigs.length];

  // 计算展开和收起的延迟时间
  const totalItems = menuItems.value.length;
  const expandDelay = index * 0.05; // 展开时从第一个开始，每个延迟50ms
  const collapseDelay = (totalItems - 1 - index) * 0.03; // 收起时从最后一个开始，每个延迟30ms

  return {
    '--tx': `${x}px`,
    '--ty': `${y}px`,
    '--bg-color': config.bg,
    '--text-color': config.text,
    '--expand-delay': `${expandDelay}s`,
    '--collapse-delay': `${collapseDelay}s`,
  };
};

const onMenuClick = (item) => {
  openTool(item);
  menuOpen.value = false;
};

// 左右切换位置
const switchSide = () => {
  // 如果菜单打开，先关闭菜单
  if (menuOpen.value) {
    menuOpen.value = false;
  }
  
  // 延迟一点时间让菜单关闭动画完成，然后切换位置
  setTimeout(() => {
    isLeftSide.value = !isLeftSide.value;
    savePosition(); // 保存新的位置偏好
  }, 150);
};

// 窗口大小改变时重新计算位置
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

// 初始化位置：根据保存的偏好或默认右侧
const initializePosition = () => {
  const savedSide = localStorage.getItem('floatingBallSide');
  isLeftSide.value = savedSide === 'left';
};

// 保存位置偏好
const savePosition = () => {
  localStorage.setItem('floatingBallSide', isLeftSide.value ? 'left' : 'right');
};

onMounted(() => {
  nextTick(() => {
    windowWidth.value = window.innerWidth;
    initializePosition();
  });
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('resize', handleResize);
  setupIframeListeners(); // 在组件挂载时设置iframe监听器
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('resize', handleResize);
  cleanupIframeListeners(); // 在组件卸载时清理iframe监听器
});
</script>

<style scoped>
.floating-ball-container {
  position: fixed;
  width: 80px;
  height: 80px;
  cursor: pointer;
  z-index: 1000;
}

.ball-content {
  position: relative;
  z-index: 20;
  width: 100%;
  height: 100%;
}

.ball-content img {
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.floating-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.menu-item {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70px;
  height: 70px;
  background: var(--bg-color);
  backdrop-filter: blur(8px);
  color: var(--text-color);
  border: none;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  pointer-events: all;
  transform: translate(-50%, -50%) scale(0);
  transition: 
    transform 0.25s cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 0.2s ease;
  flex-direction: column;
  padding: 6px;
  box-sizing: border-box;
}

.menu-item:hover {
  transform: translate(calc(-50% + var(--tx)), calc(-50% + var(--ty))) scale(1.1);
  box-shadow: 0 4px 16px rgba(0,0,0,0.2);
}

/* 展开状态 */
.floating-menu.open .menu-item {
  transform: translate(calc(-50% + var(--tx)), calc(-50% + var(--ty))) scale(1);
  transition-delay: var(--expand-delay);
}

/* 收起状态 */
.floating-menu:not(.open) .menu-item {
  transform: translate(-50%, -50%) scale(0);
  transition-delay: var(--collapse-delay);
}

.menu-item-icon {
    position: absolute;
    bottom: 8px;
    width: 20px;
    height: 20px;
    opacity: 0.5;
}

.menu-item-text {
    font-size: 14px;
    line-height: 1.2;
    text-align: center;
    white-space: normal;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-weight: 600;
    z-index: 2;
}

.switch-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  pointer-events: all;
  color: #495057;
  transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  opacity: 0.95;
}

.switch-button:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
  border-color: #dee2e6;
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 
    0 6px 16px rgba(0,0,0,0.2),
    0 3px 8px rgba(0,0,0,0.15);
  color: #343a40;
}

.switch-button:active {
  transform: translateY(-50%) scale(1.05);
  box-shadow: 
    0 3px 8px rgba(0,0,0,0.15),
    0 1px 4px rgba(0,0,0,0.1);
}

/* 当悬浮按钮在左侧时，切换按钮显示在右边 */
.floating-ball-container.left-side .switch-button {
  right: -25px;
}

/* 当悬浮按钮在右侧时，切换按钮显示在左边 */
.floating-ball-container:not(.left-side) .switch-button {
  left: -25px;
}
</style>
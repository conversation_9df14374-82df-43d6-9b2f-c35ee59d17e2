<template>
  <div v-if="visible" class="roll-call-modal">
    <!-- 星星背景 -->
    <div class="stars-background">
      <div class="star" v-for="n in 200" :key="n" :style="getStarStyle(n)"></div>
    </div>
    
    <!-- 关闭按钮 -->
    <div class="exit-button-container">
      <button class="exit-btn" @click="close">
        <div class="exit-btn-bg"></div>
        <span class="exit-btn-text">关闭</span>
        <div class="exit-btn-glow"></div>
      </button>
    </div>
    
    <!-- 主要内容 -->
    <div class="modal-content" @click.stop>
      <!-- 3D球体容器 -->
      <div ref="threeContainer" class="three-container"></div>
    </div>
    
    <!-- 控制面板区域 -->
    <div class="control-panel">
      <!-- 配置区域 -->
      <div class="config-section">
        <!-- 效果选择配置 -->
        <div class="config-item">
          <label class="config-label">选择效果</label>
          <div class="effect-selector">
            <button 
              class="effect-btn" 
              @click="toggleEffectDropdown"
              :class="{ 'active': showEffectDropdown }"
            >
              <img :src="effects[selectedEffect].preview" alt="效果预览" class="effect-preview">
              <span class="effect-name">{{ effects[selectedEffect].name }}</span>
              <span class="effect-arrow">▼</span>
            </button>
            <div v-if="showEffectDropdown" class="effect-dropdown">
              <div 
                v-for="(effect, key) in effects" 
                :key="key"
                class="effect-option"
                :class="{ 'selected': selectedEffect === key }"
                @click="selectEffect(key)"
              >
                <img :src="effect.preview" alt="效果预览" class="effect-option-preview">
                <span class="effect-option-name">{{ effect.name }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 抽取数量配置 -->
        <div class="config-item">
          <label class="config-label">抽取数量</label>
          <div class="number-input">
            <button class="number-btn" @click="decreaseNum" :disabled="num <= 1">-</button>
            <span class="number-value">{{ num }}</span>
            <button class="number-btn" @click="increaseNum" :disabled="num >= students.length">+</button>
          </div>
        </div>
        
        <!-- 均衡性别配置 -->
        <div class="config-item">
          <label class="config-label">均衡性别</label>
          <div class="switch-container">
            <input 
              type="checkbox" 
              id="genderSwitch" 
              v-model="isAccordingGender"
              class="switch-input"
            >
            <label for="genderSwitch" class="switch-label"></label>
          </div>
        </div>
        
        <!-- 点名记录按钮 -->
      <button 
        v-if="!isFirstRound"
        class="history-btn" 
        @click="showHistory"
      >
          <span>点名记录</span>
      </button>
      </div>
      
      <!-- 点名按钮 -->
      <button 
        class="control-btn" 
        :class="{ 'rolling': isRolling }" 
        @click="handleRollCall"
        :disabled="students.length === 0"
      >
        <span class="btn-text">{{ isRolling ? '确定' : '点名' }}</span>
        <div class="btn-effect"></div>
      </button>
    </div>

    <!-- 点名记录弹窗 -->
    <Transition name="fade">
      <div v-if="showHistoryDialog" class="history-dialog-overlay" @click="showHistoryDialog = false">
        <div class="history-dialog">
          <div class="history-header">
            <h2>点名记录</h2>
            <button class="close-history-btn" @click="showHistoryDialog = false">×</button>
          </div>
          <div class="history-content">
            <template v-if="Object.keys(historyRecords).length > 0">
              <template v-for="round in Object.keys(historyRecords).sort((a, b) => Number(a) - Number(b))">
                <div :key="round" class="history-item" v-if="historyRecords[round] && historyRecords[round].length > 0">
                <div class="history-round">第{{ round }}次</div>
                <div class="history-names-container">
                  <div v-for="student in historyRecords[round]" 
                       :key="student.id" 
                       class="student-tag"
                       :class="{ 
                         'male-student': student.sex === '1', 
                         'female-student': student.sex === '2' 
                       }"
                  >
                    {{ student.name }}
                  </div>
                </div>
              </div>
              </template>  
            </template>
            <div v-else class="no-history">
              暂无点名记录
            </div>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 选中学生展示层 -->
    <Transition name="fade">
      <div v-if="showSelectedStudents" class="selected-students-overlay">
        <div class="selected-students-container">
          <div v-for="student in selectedStudent" :key="student.id" class="selected-student-card">
            <div class="student-avatar">
              <img :src="student.avatarPath || getRandomAvatarPath()" alt="学生头像">
            </div>
            <div class="student-name" :class="{
              'male-student': student.sex === '1', 
              'female-student': student.sex === '2' 
            }">{{ student.name }}</div>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 完成提示对话框 -->
    <Transition name="fade">
      <div v-if="showCompletionDialog" class="completion-dialog-overlay">
        <div class="completion-dialog">
          <h2>名单已全部抽完</h2>
          <p>是否要重新开始抽取？</p>
          <div class="dialog-buttons">
            <button class="dialog-btn confirm-btn" @click="restartRollCall">
              重新开始
              <div class="btn-effect"></div>
            </button>
            <button class="dialog-btn cancel-btn" @click="showCompletionDialog=false">
              关闭
              <div class="btn-effect"></div>
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as THREE from 'three'
// 导入所有效果图片
import kaImage0 from '../assets/rollcall/ka-0.png'
import kaImage1 from '../assets/rollcall/ka-1.png'
import haiImage0 from '../assets/rollcall/hai-0.png'
import haiImage1 from '../assets/rollcall/hai-1.png'
import starImage0 from '../assets/rollcall/star-0.png'
import starImage1 from '../assets/rollcall/star-1.png'
import sunImage0 from '../assets/rollcall/sun-0.png'
import sunImage1 from '../assets/rollcall/sun-1.png'
import startSound from '../assets/rollcall/start.mp3'
import successSound from '../assets/rollcall/success.mp3'
import { randomTxt, getRandomTxt } from '../api/rollcall';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  students: {
    type: Array,
    default: () => []
  },
  randomId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['close', 'studentSelected'])

// 响应式数据
const threeContainer = ref(null)
const isRolling = ref(false)
const selectedStudent = ref([])
const isFirstRound = ref(true) // 添加第一轮标记

// 配置项
const num = ref(1)
const isAccordingGender = ref(false)

// 效果相关配置
const selectedEffect = ref('nezha') // 默认选择哪吒敖丙
const showEffectDropdown = ref(false)

// localStorage 键名
const EFFECT_STORAGE_KEY = 'rollcall_selected_effect'

// 效果列表配置
const effects = {
  nezha: {
    name: '哪吒敖丙',
    images: [kaImage0, kaImage1],
    preview: kaImage0
  },
  spongebob: {
    name: '海绵宝宝',
    images: [haiImage0, haiImage1],
    preview: haiImage0
  },
  star: {
    name: '星星',
    images: [starImage0, starImage1],
    preview: starImage0
  },
  sunflower: {
    name: '向日葵',
    images: [sunImage0, sunImage1],
    preview: sunImage0
  }
}

// Three.js 相关变量
let scene, camera, renderer, sphere, animationId
let sphereObjects = []
let normalRotationSpeed = 0.02  // 从0.01改为0.02，加快旋转速度
let fastRotationSpeed = 0.25  // 适中的快速旋转速度，既有动感又能看清内容
let currentRotationSpeed = normalRotationSpeed

// 音效相关
let startAudio, successAudio

// 星星样式生成
const getStarStyle = (index) => {
  const size = Math.random() * 5 + 2  // 从原来的3+1改为5+2，让星星更大
  const opacity = Math.random() * 0.8 + 0.2
  const duration = Math.random() * 3 + 2
  const delay = Math.random() * 2
  
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    width: size + 'px',
    height: size + 'px',
    opacity: opacity,
    animationDuration: duration + 's',
    animationDelay: delay + 's'
  }
}

// 随机获取头像图片路径
const getRandomAvatarPath = () => {
  const currentEffectImages = effects[selectedEffect.value].images
  return currentEffectImages[Math.floor(Math.random() * currentEffectImages.length)]
}

// 初始化Three.js场景
const initThreeJS = () => {
  if (!threeContainer.value) return

  const container = threeContainer.value
  const width = container.clientWidth
  const height = container.clientHeight

  // 创建场景
  scene = new THREE.Scene()

  // 创建相机
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.z = 8

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ 
    antialias: true, 
    alpha: true,
    preserveDrawingBuffer: true 
  })
  renderer.setSize(width, height)
  renderer.setClearColor(0x000000, 0)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  // 设置正确的色彩空间和色调映射
  renderer.outputColorSpace = THREE.SRGBColorSpace
  renderer.toneMapping = THREE.NoToneMapping
  renderer.toneMappingExposure = 1.0
  container.appendChild(renderer.domElement)

  // 创建组来包含所有的图片
  sphere = new THREE.Group()
  scene.add(sphere)

  // 使用很低强度的环境光，避免影响图片原色
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.3)
  scene.add(ambientLight)

  // 添加背景球体（可选，增加深度感）
  const backgroundGeometry = new THREE.SphereGeometry(5.3, 32, 32)
  const backgroundMaterial = new THREE.MeshBasicMaterial({
    color: 0x0a0a2e,
    transparent: true,
    opacity: 0.1,
    side: THREE.BackSide
  })
  const backgroundSphere = new THREE.Mesh(backgroundGeometry, backgroundMaterial)
  scene.add(backgroundSphere)

  createSphereObjects()
  animate()
}

// 创建球面上的图片对象
const createSphereObjects = () => {
  if (!props.students.length) return

  // 清除旧的对象
  sphereObjects.forEach(obj => {
    sphere.remove(obj)
  })
  sphereObjects = []

  const textureLoader = new THREE.TextureLoader()
  
  // 使用当前选中效果的图片
  const currentEffectImages = effects[selectedEffect.value].images
  
  // 加载所有图片纹理
  const loadTextures = currentEffectImages.map(imageSrc => {
    return new Promise((resolve) => {
      textureLoader.load(imageSrc, resolve, undefined, (error) => {
        console.error('纹理加载失败:', error)
        resolve(null)
      })
    })
  })
  
  Promise.all(loadTextures).then((textures) => {
    // 过滤掉加载失败的纹理
    const validTextures = textures.filter(texture => texture !== null)
    
    // 优化纹理设置，确保显示原色
    validTextures.forEach(texture => {
      texture.colorSpace = THREE.SRGBColorSpace // 设置正确的颜色空间
      texture.minFilter = THREE.LinearFilter
      texture.magFilter = THREE.LinearFilter
      texture.generateMipmaps = false
    })
    
    if (validTextures.length === 0) {
      console.error('所有纹理加载失败，使用文字版本')
      createTextSphereObjects()
      return
    }
    
    // 处理学生数据，确保有60个
    const processedStudents = []
    const targetCount = 60  // 从50改为60
    let currentIndex = 0
    
    // 填充到60个
    while (processedStudents.length < targetCount) {
      processedStudents.push(props.students[currentIndex])
      currentIndex = (currentIndex + 1) % props.students.length
    }

    for (let i = 0; i < targetCount; i++) {
      // 使用改进的黄金螺旋算法分布点，确保更均匀的分布
      const y = 1 - (i / (targetCount - 1)) * 2
      const radiusAtY = Math.sqrt(1 - y * y)
      const theta = (i * 137.5077640844 * Math.PI) / 180 // 更精确的黄金角度

      const x = Math.cos(theta) * radiusAtY
      const z = Math.sin(theta) * radiusAtY

      // 创建组合对象来包含图片和文字
      const studentGroup = new THREE.Group()

      // 随机选择一个纹理
      const randomTexture = validTextures[Math.floor(Math.random() * validTextures.length)]

      // 创建缩小的图片平面（调大尺寸）
      const planeGeometry = new THREE.PlaneGeometry(0.8, 0.8)
      const planeMaterial = new THREE.MeshBasicMaterial({
        map: randomTexture,
        transparent: true,
        side: THREE.DoubleSide,
        alphaTest: 0.1,
        color: 0xffffff,  // 确保材质颜色为纯白，不影响纹理
        toneMapped: false  // 禁用色调映射，保持原色
      })

      const plane = new THREE.Mesh(planeGeometry, planeMaterial)
      plane.position.set(0, 0.2, 0) // 图片稍微上移，为文字留出空间
      
      // 创建文字标签
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = 512
      canvas.height = 128

      // 不绘制背景，保持透明
      context.clearRect(0, 0, canvas.width, canvas.height)
      
      // 绘制文字，增大字体并添加描边效果增强可读性
      context.fillStyle = '#ffffff'
      context.strokeStyle = '#000000'
      context.lineWidth = 5
      context.font = 'bold 56px Microsoft YaHei'
      context.textAlign = 'center'
      context.textBaseline = 'middle'
      
      // 先绘制描边
      context.strokeText(processedStudents[i], canvas.width / 2, canvas.height / 2)
      // 再绘制填充
      context.fillText(processedStudents[i], canvas.width / 2, canvas.height / 2)

      const textTexture = new THREE.CanvasTexture(canvas)
      textTexture.colorSpace = THREE.SRGBColorSpace
      const textMaterial = new THREE.MeshBasicMaterial({
        map: textTexture,
        transparent: true,
        alphaTest: 0.1,
        toneMapped: false  // 禁用色调映射，保持原色
      })
      
      const textGeometry = new THREE.PlaneGeometry(1.4, 0.35)
      const textMesh = new THREE.Mesh(textGeometry, textMaterial)
      textMesh.position.set(0, -0.3, 0) // 文字在图片下方

      // 将图片和文字添加到组中
      studentGroup.add(plane)
      studentGroup.add(textMesh)
      
      // 设置组的位置
      const radius = 3.8
      studentGroup.position.set(x * radius, y * radius, z * radius)
      
      // 让组朝向球心外侧
      const normal = new THREE.Vector3(x, y, z).normalize()
      studentGroup.lookAt(
        studentGroup.position.x + normal.x,
        studentGroup.position.y + normal.y,
        studentGroup.position.z + normal.z
      )
      
      // 添加轻微的随机旋转让效果更自然
      studentGroup.rotation.z += (Math.random() - 0.5) * 0.2
      
      // 添加学生数据
      studentGroup.userData = {
        studentName: processedStudents[i],
        originalPosition: { x: x * radius, y: y * radius, z: z * radius },
        originalRotation: { x: studentGroup.rotation.x, y: studentGroup.rotation.y, z: studentGroup.rotation.z }
      }

      sphere.add(studentGroup)
      sphereObjects.push(studentGroup)
    }
  })
}

// 创建文字版本的球面对象（备用方案）
const createTextSphereObjects = () => {
  const studentCount = Math.min(props.students.length, 60)  // 从50改为60
  const radius = 3.8

  for (let i = 0; i < studentCount; i++) {
    const y = 1 - (i / (studentCount - 1)) * 2
    const radiusAtY = Math.sqrt(1 - y * y)
    const theta = (i * 137.5077640844 * Math.PI) / 180

    const x = Math.cos(theta) * radiusAtY
    const z = Math.sin(theta) * radiusAtY

    // 创建组合对象
    const studentGroup = new THREE.Group()

    // 创建头像背景圆圈（调大尺寸）
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    canvas.width = 128
    canvas.height = 128

    // 绘制圆形背景
    context.beginPath()
    context.arc(64, 64, 56, 0, 2 * Math.PI)
    context.fillStyle = '#4facfe'
    context.fill()
    
    // 添加边框
    context.strokeStyle = '#ffffff'
    context.lineWidth = 3
    context.stroke()
    
    // 绘制头像文字
    context.fillStyle = '#ffffff'
    context.font = 'bold 20px Microsoft YaHei'
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    context.fillText(props.students[i], 64, 64)

    const avatarTexture = new THREE.CanvasTexture(canvas)
    const avatarMaterial = new THREE.SpriteMaterial({ 
      map: avatarTexture,
      transparent: true,
      alphaTest: 0.1
    })
    const avatarSprite = new THREE.Sprite(avatarMaterial)
    avatarSprite.scale.set(0.8, 0.8, 0.8) // 调大头像
    avatarSprite.position.set(0, 0.2, 0) // 头像稍微上移

    // 创建名字标签
    const nameCanvas = document.createElement('canvas')
    const nameContext = nameCanvas.getContext('2d')
    nameCanvas.width = 512
    nameCanvas.height = 128

    // 不绘制背景，保持透明
    nameContext.clearRect(0, 0, nameCanvas.width, nameCanvas.height)
    
    // 绘制文字，增大字体并添加描边效果增强可读性
    nameContext.fillStyle = '#ffffff'
    nameContext.strokeStyle = '#000000'
    nameContext.lineWidth = 5
    nameContext.font = 'bold 56px Microsoft YaHei'
    nameContext.textAlign = 'center'
    nameContext.textBaseline = 'middle'
    
    // 先绘制描边
    nameContext.strokeText(props.students[i], nameCanvas.width / 2, nameCanvas.height / 2)
    // 再绘制填充
    nameContext.fillText(props.students[i], nameCanvas.width / 2, nameCanvas.height / 2)

    const nameTexture = new THREE.CanvasTexture(nameCanvas)
    const nameMaterial = new THREE.SpriteMaterial({
      map: nameTexture,
      transparent: true,
      alphaTest: 0.1
    })
    const nameSprite = new THREE.Sprite(nameMaterial)
    nameSprite.scale.set(1.4, 0.35, 1.4)
    nameSprite.position.set(0, -0.3, 0) // 文字在头像下方

    // 将头像和文字添加到组中
    studentGroup.add(avatarSprite)
    studentGroup.add(nameSprite)
    
    studentGroup.position.set(x * radius, y * radius, z * radius)
    studentGroup.userData = {
      studentName: props.students[i],
      originalPosition: { x: x * radius, y: y * radius, z: z * radius }
    }

    sphere.add(studentGroup)
    sphereObjects.push(studentGroup)
  }
}

// 动画循环
const animate = () => {
  if (!scene || !renderer || !camera) return

  animationId = requestAnimationFrame(animate)

  // 球体旋转 - 只绕Y轴正向旋转，保持人头朝上
  if (sphere) {
    sphere.rotation.y += currentRotationSpeed
    // 移除X轴和Z轴旋转，避免倒转和眩晕感
  }

  // 如果正在点名，添加一些随机的小动画
  if (isRolling.value && sphereObjects.length > 0) {
    sphereObjects.forEach((obj, index) => {
      // 添加轻微的上下浮动
      const time = Date.now() * 0.005
      const floatOffset = Math.sin(time + index * 0.5) * 0.1
      const originalY = obj.userData.originalPosition.y
      obj.position.y = originalY + floatOffset
      
      // 添加轻微的缩放动画
      const scaleOffset = 1 + Math.sin(time * 2 + index) * 0.1
      obj.scale.set(scaleOffset, scaleOffset, scaleOffset)
    })
  }

  renderer.render(scene, camera)
}

// 添加展示控制变量
const showSelectedStudents = ref(false)
const showCompletionDialog = ref(false) // 添加完成提示框的控制变量
const showHistoryDialog = ref(false) // 添加点名记录弹窗的控制变量
const historyRecords = ref([]) // 添加历史记录的响应式数据
let hideTimer = null

// 数量控制方法
const increaseNum = () => {
  if (num.value < props.students.length) {
    num.value++
  }
}

const decreaseNum = () => {
  if (num.value > 1) {
    num.value--
  }
}

// localStorage 相关方法
const loadEffectFromStorage = () => {
  try {
    const savedEffect = localStorage.getItem(EFFECT_STORAGE_KEY)
    if (savedEffect && effects[savedEffect]) {
      selectedEffect.value = savedEffect
    }
  } catch (error) {
    console.log('读取效果设置失败:', error)
  }
}

const saveEffectToStorage = (effectKey) => {
  try {
    localStorage.setItem(EFFECT_STORAGE_KEY, effectKey)
  } catch (error) {
    console.log('保存效果设置失败:', error)
  }
}

// 效果选择器相关方法
const toggleEffectDropdown = () => {
  showEffectDropdown.value = !showEffectDropdown.value
}

const selectEffect = (effectKey) => {
  selectedEffect.value = effectKey
  showEffectDropdown.value = false
  
  // 保存到localStorage
  saveEffectToStorage(effectKey)
  
  // 如果Three.js场景已初始化，重新创建球面对象以应用新效果
  if (props.visible && scene) {
    createSphereObjects()
  }
}

// 监听学生数量变化，自动调整抽取数量
watch(() => props.students.length, (newLength) => {
  if (newLength > 0 && num.value > newLength) {
    num.value = newLength
  }
  if (newLength === 0) {
    num.value = 1
  }
})

// 修改处理抽取结果的逻辑
const handleRollCall = async () => {
  if (props.students.length === 0) return

  if (!isRolling.value) {
    // 播放开始音效
    try {
      if (!startAudio) {
        startAudio = new Audio(startSound)
        startAudio.volume = 1 // 设置音量
      }
      startAudio.currentTime = 0 // 重置播放位置
      startAudio.play().catch(error => {
        console.log('音效播放失败:', error)
      })
    } catch (error) {
      console.log('音效加载失败:', error)
    }

    isRolling.value = true
    currentRotationSpeed = fastRotationSpeed
    selectedStudent.value = []
    showSelectedStudents.value = false // 确保隐藏之前的结果
    showCompletionDialog.value = false // 确保隐藏完成提示框
  } else {
    try {
      const response = await randomTxt({
        randomId: props.randomId,
        num: num.value,
        isAccordingGender: isAccordingGender.value,
        isNewRound: isFirstRound.value
      })
      console.log('response==', response)
      
      if (response && response.data) {
        const results = Array.isArray(response.data) ? response.data : [response.data]
        
        // 为每个选中的学生添加随机头像路径
        const resultsWithAvatars = results.map(student => ({
          ...student,
          avatarPath: getRandomAvatarPath()
        }))
        
        selectedStudent.value = resultsWithAvatars
        
        // 检查抽取结果数量是否为0，表示已经抽取结束
        if (results.length === 0) {
          isRolling.value = false
          currentRotationSpeed = normalRotationSpeed
          showCompletionDialog.value = true // 显示完成提示框
        } else {
          // 播放成功音效
          try {
            if (!successAudio) {
              successAudio = new Audio(successSound)
              successAudio.volume = 1 // 设置音量
            }
            successAudio.currentTime = 0 // 重置播放位置
            successAudio.play().catch(error => {
              console.log('成功音效播放失败:', error)
            })
          } catch (error) {
            console.log('成功音效加载失败:', error)
          }

          isRolling.value = false
          currentRotationSpeed = normalRotationSpeed
          
          // 显示结果并设置定时器
          showSelectedStudents.value = true
          if (hideTimer) clearTimeout(hideTimer)
          hideTimer = setTimeout(() => {
            showSelectedStudents.value = false
          }, 3000)

          if (isFirstRound.value) {
            isFirstRound.value = false
          }

          // 记录点名结果
          const record = results.map(student => ({
            name: student.name,
            id: student.id
          }))
          historyRecords.value.push(record)
        }
      } else {
        console.error('接口返回数据格式错误:', response)
        isRolling.value = false
        currentRotationSpeed = normalRotationSpeed
      }
    } catch (error) {
      console.error('调用抽取接口失败:', error)
      isRolling.value = false
      currentRotationSpeed = normalRotationSpeed
    }
  }
}

// 重置所有状态
const resetAllStates = () => {
  isRolling.value = false
  selectedStudent.value = []
  currentRotationSpeed = normalRotationSpeed
  isFirstRound.value = true
  showSelectedStudents.value = false
  showCompletionDialog.value = false
  showHistoryDialog.value = false
  showEffectDropdown.value = false
  historyRecords.value = []
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
}

// 重新开始抽取
const restartRollCall = () => {
  resetAllStates()
}

// 关闭弹窗
const close = () => {
  if (isRolling.value) return
  resetAllStates()
  emit('close')
}

// 清理Three.js资源
const cleanup = () => {
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
  }

  if (renderer) {
    renderer.dispose()
    if (renderer.domElement && renderer.domElement.parentNode) {
      renderer.domElement.parentNode.removeChild(renderer.domElement)
    }
  }

  if (scene) {
    scene.clear()
  }

  sphereObjects = []
  scene = null
  camera = null
  renderer = null
  sphere = null
}

// 监听可见性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initThreeJS()
    })
  } else {
    cleanup()
    resetAllStates()
  }
})

// 监听学生列表变化
watch(() => props.students, () => {
  if (props.visible && scene) {
    createSphereObjects()
  }
}, { deep: true })

// 组件卸载时清理
onUnmounted(() => {
  cleanup()
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
})

// 处理窗口大小变化
const handleResize = () => {
  if (!threeContainer.value || !camera || !renderer) return

  const container = threeContainer.value
  const width = container.clientWidth
  const height = container.clientHeight

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

// 键盘事件处理
const handleKeydown = (event) => {
  if (!props.visible) return
  
  switch (event.code) {
    case 'Space':
      event.preventDefault()
      handleRollCall()
      break
    case 'Escape':
      event.preventDefault()
      if (!isRolling.value) {
        close()
      }
      break
  }
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
  if (!props.visible) return
  
  const effectSelector = event.target.closest('.effect-selector')
  if (!effectSelector && showEffectDropdown.value) {
    showEffectDropdown.value = false
  }
}

// 显示点名记录弹窗
const showHistory = async () => {
  try {
    const response = await getRandomTxt({
      randomId: props.randomId
    })
    
    if (response && response.data) {
      historyRecords.value = response.data
    }
    showHistoryDialog.value = true
  } catch (error) {
    console.error('获取点名记录失败:', error)
  }
}

onMounted(() => {
  // 加载保存的效果设置
  loadEffectFromStorage()
  
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', handleKeydown)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.roll-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: radial-gradient(ellipse at center, #0a0a2e 0%, #000000 100%);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  animation: fadeIn 0.5s ease-out;
}

.stars-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  
  .star {
    position: absolute;
    background: #ffffff;
    border-radius: 50%;
    animation: twinkle linear infinite;
  }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

.modal-content {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.exit-button-container {
  position: fixed;
  bottom: 30px;
  right: 300px;  /* Adjusted to prevent overlap with control panel */
  z-index: 10001;
}

.exit-btn {
  position: relative;
  padding: 15px 40px; /* 与点名按钮一致的内边距 */
  border: none;
  border-radius: 50px; /* 与点名按钮一致的圆弧 */
  
  /* 使用优雅的灰色渐变背景 */
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  
  color: white; 
  font-size: 1.5rem; /* 与点名按钮一致的字体大小 */
  font-weight: 600; /* 与点名按钮一致的字体粗细 */
  cursor: pointer; 
  
  display: flex; 
  align-items: center; 
  justify-content: center; 
  
  transition: all 0.3s ease; /* 与点名按钮一致的过渡效果 */
  overflow: hidden; 
  min-width: 160px; /* 与点名按钮一致的最小宽度 */
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
  
  /* 添加内部光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }
}

.exit-btn:hover {
  box-shadow: 0 12px 35px rgba(255, 255, 255, 0.2); /* 与点名按钮一致的悬停阴影效果 */
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.08) 100%);
  border-color: rgba(255, 255, 255, 0.3);

  &::before {
    left: 100%;
  }

  .exit-btn-text {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
  }
}

.exit-btn:active {
  box-shadow: 0 6px 20px rgba(255, 255, 255, 0.15);
}

.exit-btn-bg {
  /* This was the main circular background. We are applying bg to .exit-btn now. */
  display: none; /* Easiest way to remove its effect */
}

/* .exit-btn-icon related styles removed as it's not in this button's HTML */

.exit-btn-text {
  position: relative; 
  z-index: 2; 
  color: white;
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Microsoft YaHei', 'Arial', sans-serif;
}

.exit-btn-glow {
  /* This was the circular outer glow. */
  display: none; /* Easiest way to remove its effect */
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
}

.three-container {
  width: 100%;
  height: 100%;
  position: relative;
  
  canvas {
    display: block;
  }
}

.control-panel {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15px;
  z-index: 10001;
}

.config-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 20px;
  padding: 20px;
  border-radius: 20px;
  margin-bottom: 20px;
}

.config-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
  width: 100%;
  justify-content: space-between;
}

.config-label {
  font-size: 16px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  min-width: 80px;
}

.number-input {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.number-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
}

.number-value {
  font-size: 18px;
  font-weight: 600;
  color: white;
  min-width: 24px;
  text-align: center;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.switch-container {
  display: flex;
  align-items: center;
}

.switch-input {
  display: none;
}

.switch-label {
  width: 50px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.switch-input:checked + .switch-label {
  background: linear-gradient(135deg, #00c6fb 0%, #005bea 100%);
  border-color: rgba(255, 255, 255, 0.5);
}

.switch-label::before {
  content: '';
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.switch-input:checked + .switch-label::before {
  transform: translateX(26px);
}

.effect-selector {
  position: relative;
  display: flex;
  flex-direction: column;
}

.effect-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  
  &:hover, &.active {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

.effect-preview {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.effect-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  text-align: left;
}

.effect-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.effect-btn.active .effect-arrow {
  transform: rotate(180deg);
}

.effect-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(26, 26, 62, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  z-index: 1000;
  margin-top: 5px;
  overflow: hidden;
  animation: dropdownSlideDown 0.3s ease-out;
}

@keyframes dropdownSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.effect-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &.selected {
    background: rgba(102, 126, 234, 0.2);
    border-left: 4px solid #667eea;
  }
}

.effect-option-preview {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.effect-option-name {
  font-size: 15px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.history-btn {
  position: relative;
  padding: 15px 25px;
  border: none;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 120px;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
  }
  
  &:hover {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.3) 0%, rgba(0, 242, 254, 0.2) 100%);
    border-color: rgba(79, 172, 254, 0.5);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 
      0 8px 30px rgba(79, 172, 254, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(-1px) scale(1.02);
  }
}

.control-btn {
  position: relative;
  padding: 15px 40px;
  border: none;
  border-radius: 50px;
  background: linear-gradient(to right, #00c6fb 0%, #005bea 100%);
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  min-width: 160px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);

    .btn-effect {
      transform: translateX(100%);
    }
  }

  &.rolling {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    animation: pulse 1s ease-in-out infinite;
  }

  .btn-text {
    position: relative;
    z-index: 2;
  }

  .btn-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: transform 0.6s ease;
    z-index: 1;
  }
}

.selected-students-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.selected-students-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 60px;
  padding: 60px;
  max-width: 95vw;
  margin: 0 auto;
  max-height: 90vh;
  overflow-y: auto;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.selected-student-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  width: 220px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
  transition: all 0.4s ease;
  
  &:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 30px 70px rgba(0, 0, 0, 0.6);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    
    .student-avatar {
      transform: scale(1.05) rotate(0);
      animation: none;
      box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    }
  }
}

.student-avatar {
  width: 180px;
  height: 180px;
  border-radius: 25px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  border: 6px solid #fff;
  transform-origin: center bottom;
  animation: wobble 2s ease-in-out infinite;
  transition: all 0.4s ease;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.student-name {
  font-size: 35px;
  font-weight: 800;
  color: white;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 1px;
  
  &.male-student {
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    background-clip: unset !important;
    color: #3b82f6 !important;
    text-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  }
  
  &.female-student {
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    background-clip: unset !important;
    color: #ec4899 !important;
    text-shadow: 0 2px 4px rgba(236, 72, 153, 0.3);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.1);
  }
  70% {
    opacity: 0.95;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes wobble {
  0%, 100% {
    transform: rotate(-3deg);
  }
  50% {
    transform: rotate(3deg);
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 完成提示对话框样式 */
.completion-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10002;
  display: flex;
  align-items: center;
  justify-content: center;
}

.completion-dialog {
  background: linear-gradient(135deg, #1a1a3e 0%, #0a0a2e 100%);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.1);
  max-width: 90%;
  width: 400px;
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  h2 {
    color: white;
    font-size: 24px;
    margin: 0 0 20px;
    font-weight: 600;
  }
  
  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    margin: 0 0 30px;
  }
}

.dialog-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.dialog-btn {
  position: relative;
  padding: 12px 30px;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  min-width: 120px;
  
  &:hover {
    transform: translateY(-2px);
    
    .btn-effect {
      transform: translateX(100%);
    }
  }
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  
  &:hover {
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
  }
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
}

/* 点名记录弹窗样式 */
.history-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10003;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-dialog {
  background: linear-gradient(135deg, #1a1a3e 0%, #0a0a2e 100%);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.1);
  max-width: 95%;
  width: 800px;
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  h2 {
    color: white;
    font-size: 28px;
    margin: 0 0 20px;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
  }
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 15px;
}

.close-history-btn {
  background: none;
  border: none;
  font-size: 50px;
  color: white;
  cursor: pointer;
}

.history-content {
  text-align: left;
  max-height: 65vh;
  overflow-y: auto;
  padding-right: 15px;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.history-item {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  transition: all 0.3s ease;
  border-left: 4px solid #667eea;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }
}

.history-round {
  font-size: 20px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 15px;
  display: inline-block;
  padding: 5px 15px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20px;
}

.history-names-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.student-tag {
  width: 120px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  border-radius: 15px;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  
  /* 默认样式（无性别） */
  background: #f3f4f6;
  color: #6b7280;
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.2);
  
  &:hover {
    transform: translateY(-2px);
    background: #e5e7eb;
    box-shadow: 0 5px 15px rgba(107, 114, 128, 0.3);
  }
  
  &.male-student {
    background: #dbeafe;
    color: #1e40af;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    
    &:hover {
      background: #bfdbfe;
      box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
    }
  }
  
  &.female-student {
    background: #fdf2f8;
    color: #be185d;
    border-color: #ec4899;
    box-shadow: 0 2px 8px rgba(236, 72, 153, 0.3);
    
    &:hover {
      background: #fce7f3;
      box-shadow: 0 5px 15px rgba(236, 72, 153, 0.4);
    }
  }
}

.no-history {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    bottom: 20px;
    right: 20px;
  }
  
  .exit-button-container {
    bottom: 20px;
    right: 240px;  /* Adjusted for mobile to prevent overlap */
  }
  
  .config-section {
    padding: 15px;
    gap: 15px;
  }
  
  .config-label {
    font-size: 14px;
    min-width: 70px;
  }
  
  .history-btn {
    padding: 12px 20px;
    font-size: 14px;
    min-width: 100px;
    border-radius: 15px;
    
    &:hover {
      transform: translateY(-2px) scale(1.03);
    }
  }
  
  .control-btn {
    padding: 12px 30px;
    font-size: 1.1rem;
    min-width: 100px;
  }
  
  .selected-students-container {
    gap: 30px;
    padding: 30px 20px;
    max-height: 85vh;
  }
  
  .selected-student-card {
    width: calc(50% - 20px); // 移动端一行显示2个
    min-width: 140px;
    padding: 15px;
    gap: 20px;
    
    &:hover {
      transform: translateY(-5px) scale(1.01);
    }
  }
  
  .student-avatar {
    width: 120px;
    height: 120px;
    border-width: 4px;
    border-radius: 20px;
  }
  
  .student-name {
    font-size: 22px;
    font-weight: 700;
  }
  
  .exit-btn {
    padding: 12px 30px; /* 与移动端点名按钮一致 */
    font-size: 1.1rem; /* 与移动端点名按钮一致 */
    min-width: 100px;
  }
  
  .completion-dialog {
    padding: 30px;
    width: 320px;
    
    h2 {
      font-size: 20px;
      margin: 0 0 15px;
    }
    
    p {
      font-size: 16px;
      margin: 0 0 25px;
    }
  }
  
  .dialog-buttons {
    gap: 15px;
  }
  
  .dialog-btn {
    padding: 10px 25px;
    font-size: 14px;
    min-width: 100px;
  }
  
  .effect-btn {
    padding: 6px 10px;
    min-width: 120px;
    
    &:hover, &.active {
      background: rgba(255, 255, 255, 0.12);
    }
  }
  
  .effect-preview {
    width: 20px;
    height: 20px;
  }
  
  .effect-name {
    font-size: 13px;
  }
  
  .effect-arrow {
    font-size: 10px;
  }
  
  .effect-dropdown {
    border-radius: 12px;
  }
  
  .effect-option {
    padding: 10px 12px;
  }
  
  .effect-option-preview {
    width: 28px;
    height: 28px;
    border-radius: 5px;
  }
  
  .effect-option-name {
    font-size: 14px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .selected-students-container {
    gap: 20px;
    padding: 20px 15px;
  }
  
  .selected-student-card {
    width: calc(100% - 20px); // 超小屏幕一行显示1个
    max-width: 280px;
    margin: 0 auto;
  }
  
  .student-avatar {
    width: 140px;
    height: 140px;
  }
  
  .student-name {
    font-size: 24px;
  }
}
</style> 
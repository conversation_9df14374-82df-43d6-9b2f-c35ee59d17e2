<template>
  <el-dialog
    v-model="dialogVisible"
    title="历史对话记录"
    width="80%"
    class="history-dialog"
    :show-close="false"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="dialog-header-with-button">
        <div class="header-left"></div>
        <span class="el-dialog__title">历史对话记录</span>
        <div class="header-right">
          <el-button v-if="chatHistory.length > 0" type="danger" @click="clearAllHistory" class="clear-button">清空历史记录</el-button>
          <el-icon class="custom-close-icon" @click="handleClose"><Close /></el-icon>
        </div>
      </div>
    </template>
    <template #footer>
      <el-button class="dialog-exit-button" @click="handleClose">关闭</el-button>
    </template>
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else class="history-container">
      <div v-for="(chat, chatIndex) in chatHistory" :key="chat.id" class="chat-item">
        <div class="chat-header">
          <span class="chat-title">{{ chat.title }}</span>
        </div>

        <div class="messages-container">
          <div v-for="(message, messageIndex) in chat.messages" :key="message.id"
               :class="['message-item', message.role === 'user' ? 'user-message' : 'assistant-message']">
            <div v-if="isJSON(message.content)" style="display: flex; gap: 10px;">
              <div v-for="(item, index) in JSON.parse(message.content)" :key="index">
                <div v-if="item.type === 'image_url'" style="width: 200px; height: 150px;">
                  <el-image :src="item.content"  :preview-teleported="true" :preview-src-list="[item.content]" />
                </div>
                <div v-if="item.type == 'text'" style="font-size: 16px;">{{item.content}}</div>
              </div>
            </div>
            <div v-else class="message-content" v-html="renderMarkdown(message.content)"></div>
            <div class="message-actions">
              <el-text type="info" size="small" style="margin-right: 8px; font-size: 14px;">{{ message.createTime }}</el-text>
              <el-tooltip content="删除" placement="top">
                <el-icon class="action-icon delete-icon" @click="deleteMessage(message.id, chatIndex, messageIndex)"><Delete /></el-icon>
              </el-tooltip>
              <el-tooltip content="复制" placement="top">
                <el-icon class="action-icon copy-icon" @click="copyMessage(message.content)"><CopyDocument /></el-icon>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, defineComponent, onMounted, watch } from 'vue'
import { getChatList, deleteChatRecord, deleteChat } from '@/api/home'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import MarkdownIt from 'markdown-it'
import mk from "@iktakahiro/markdown-it-katex"; // Import KaTeX plugin
import { CopyDocument, Delete, Close } from '@element-plus/icons-vue' // Import icons

export default defineComponent({
  name: 'AgentHistoryDialog',
  components: {
    CopyDocument,
    Delete,
    Close
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    category: {
      type: String,
      required: true
    }
  },
  emits: ['update:visible', 'history-cleared'],
  setup(props, { emit }) {
    const store = useStore()
    const dialogVisible = ref(props.visible)
    const loading = ref(false)
    const chatHistory = ref([])
    const md = new MarkdownIt()
    md.use(mk)

    // 监听visible属性变化
    const handleVisibleChange = () => {
      dialogVisible.value = props.visible
      if (dialogVisible.value) {
        fetchChatHistory()
      }
    }

    // 获取历史对话记录
    const fetchChatHistory = async () => {
      const userId = store.getters.userId
      if (!userId) {
        ElMessage.warning('请先登录')
        handleClose()
        return
      }

      loading.value = true
      try {
        const response = await getChatList({
          userId,
          category: props.category
        })

        if (response && response.code === 200) {
          chatHistory.value = response.data || []
        }
      } catch (error) {
        console.error('获取历史记录错误:', error)
      } finally {
        loading.value = false
      }
    }

    // 关闭对话框
    const handleClose = () => {
      dialogVisible.value = false
      emit('update:visible', false)
    }

    const isJSON = (content) => {
      try {
        JSON.parse(content)
        return true
      } catch (error) {
        return false
      }
    }

    // 清理markdown代码块标记
    const cleanMarkdownCodeBlocks = (content) => {
      if (!content) return content

      // 移除开头的 ```markdown 或 ```
      content = content.replace(/^```markdown\s*\n?/i, '')
      content = content.replace(/^```\s*\n?/, '')

      // 移除结尾的 ```
      content = content.replace(/\n?```\s*$/, '')

      return content
    }

    // 渲染markdown内容 (Copied from MathGPT.vue)
    const renderMarkdown = (markdownContent) => {
      if (!markdownContent) return ''

      const cleanedContent = cleanMarkdownCodeBlocks(markdownContent)

      // 预处理Markdown内容，将LaTeX格式的公式转换为KaTeX兼容格式
      let processedContent = cleanedContent

      // 转换 \[ \] 格式为 $$ $$，并移除首尾多余空格
      processedContent = processedContent.replace(/\\\[\s*([\s\S]*?)\s*\\\]/g, (match, p1) => `$$${p1.trim()}$$`)

      // 转换 \( \) 格式为 $ $，并移除首尾多余空格
      processedContent = processedContent.replace(/\\\(\s*([\s\S]*?)\s*\\\)/g, (match, p1) => `$${p1.trim()}$`)

      // 保存并移除所有 $$ 包裹的内容，防止处理双美元符号内容
      const doubleDollarBlocks = [];
      processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, (match, content) => {
        doubleDollarBlocks.push(match);
        return `__DOUBLE_DOLLAR_PLACEHOLDER_${doubleDollarBlocks.length - 1}__`;
      });

      // 使用更健壮的正则表达式处理所有单美元符号的情况
      // 捕获成对的$，并处理其中可能存在的各种空格情况
      processedContent = processedContent.replace(/(\$)\s*([^\$]+?)\s*(\$)/g, (match, start, content, end) => {
        // 对内容进行处理，只移除首尾空格，保留内部空格
        return `${start}${content.trim()}${end}`;
      });

      // 恢复所有 $$ 包裹的内容
      doubleDollarBlocks.forEach((block, index) => {
        processedContent = processedContent.replace(`__DOUBLE_DOLLAR_PLACEHOLDER_${index}__`, block);
      });

      const htmlContent = md.render(processedContent);
      return htmlContent;
    }

    // 复制消息
    const copyMessage = (content) => {
      if (content && content.includes('[{"type":"image_url"')) {
        return ElMessage.error('图片不支持复制，请右键另存为保存图片')
      }
      navigator.clipboard.writeText(content).then(() => {
        ElMessage.success('已复制到剪贴板')
      }).catch(err => {
        console.error('复制失败:', err)
      });
    }

    // 删除消息
    const deleteMessage = async (messageId, chatIndex, messageIndex) => {
      try {
        await ElMessageBox.confirm(
          '确定要删除这条对话记录吗？此操作不可恢复。',
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        // Call API to delete the record
        const response = await deleteChatRecord(messageId)
        if (response && response.code === 200) {
          // Remove message from local state
          chatHistory.value[chatIndex].messages.splice(messageIndex, 1)
          // Optional: Remove chat if messages array becomes empty
          if (chatHistory.value[chatIndex].messages.length === 0) {
             chatHistory.value.splice(chatIndex, 1)
          }
          ElMessage.success('删除成功')
        }
      } catch (error) { }
    }

    // 监听props.visible变化
    watch(() => props.visible, handleVisibleChange)

    onMounted(() => {
      handleVisibleChange()
    })

    // 清空所有历史记录
    const clearAllHistory = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要清空历史对话记录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        loading.value = true
        // 遍历所有聊天记录，逐个删除
        for (const chat of chatHistory.value) {
          await deleteChat(chat.id)
        }
        chatHistory.value = []
        ElMessage.success('历史记录已清空')

        // 发送历史记录已清空事件，通知父组件重新初始化会话
        emit('history-cleared')

      } catch (error) {
        console.error('清空历史记录错误:', error)
      } finally {
        loading.value = false
      }
    }

    return {
      dialogVisible,
      loading,
      chatHistory,
      handleClose,
      renderMarkdown,
      isJSON,
      copyMessage,
      deleteMessage,
      clearAllHistory
    }
  }
})
</script>

<style scoped>
.history-dialog {
  border-radius: 12px;
  overflow: hidden;
}

/* 自定义对话框标题区域，包含按钮 */
.dialog-header-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
}

.header-left {
  flex: 1;
}

.el-dialog__title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 22px;
  font-weight: bold;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* 增大对话框标题字体和关闭按钮 */
:deep(.el-dialog__title) {
  font-size: 22px;
  font-weight: bold;
}

:deep(.el-dialog__headerbtn) {
  font-size: 22px;
  width: 40px;
  height: 40px;
  top: 50%;
  transform: translateY(-50%);
  margin-top: 0;
}

:deep(.el-dialog__close) {
  font-size: 22px;
}

.clear-button {
  margin-right: 20px;
}

.custom-close-icon {
  font-size: 28px;
  cursor: pointer;
  color: #909399;
}

.custom-close-icon:hover {
  color: #F56C6C;
}

.loading-container {
  padding: 20px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.history-container {
  max-height: 70vh;
  overflow-y: auto;
  padding: 1.5rem;
  background-color: #f8f9fa;
}

.chat-item {
  margin-bottom: 20px;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

.chat-header {
  display: none;
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-item {
  display: flex;
  max-width: 80%;
  position: relative;
  padding-bottom: 25px;
}

.user-message {
  align-self: flex-end;
}
:deep(.user-message .el-image img) {
  width: 200px;
  height: 150px;
  object-fit: scale-down;
}

.assistant-message {
  align-self: flex-start;
  width: 80% !important;
}

.message-content {
  padding: 0 1rem;
  border-radius: 1rem;
  word-break: break-word;
  line-height: 1.6; /* 增加行高 */
  text-align: left;
  font-size: 16px; /* 增加字体大小 */
}

.user-message .message-content {
  background-color: #2c86ff;
  color: white;
  border-top-right-radius: 0;
}

.assistant-message .message-content {
  width: 100%;
  background-color: white;
  color: #333;
  border-top-left-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-actions {
  position: absolute;
  bottom: 0;
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  min-width: 200px;
}

.user-message .message-actions {
  right: 5px;
  justify-content: flex-end;
}

.assistant-message .message-actions {
  left: 5px;
  justify-content: flex-start;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.action-icon {
  cursor: pointer;
  font-size: 20px;
  color: #606266;
}

.action-icon:hover {
  color: #409EFF;
}

.delete-icon {
  color: #F56C6C;
}

.delete-icon:hover {
  color: #ff3636;
}
</style>
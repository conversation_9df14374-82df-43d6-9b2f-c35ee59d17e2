<template>
  <div class="article-container" ref="containerRef">
    <agent-history-entry
      category="LessonAI"
      @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">教案总结助手</h1>
      <p class="subtitle">基于AI技术，快速将课件转化为教案，帮助教师提高备课效率</p>
    </div>

    <div class="form-container">
      <el-form :model="formData" label-width="120px" class="article-form" size="large">
        <el-form-item label="课件内容" required>
          <!-- 课件上传 -->
          <el-upload
            ref="uploadRef"
            class="file-uploader"
            drag
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            :on-remove="handleFileRemove"
            :file-list="[]"
            :show-file-list="false"
            accept=".ppt,.pptx,.docx,.pdf"
          >
            <!-- 未上传文件时显示上传区域 -->
            <div v-if="fileList.length === 0" class="upload-content">
              <img src="@/assets/upload-icon.svg" class="upload-icon" alt="上传图标" />
              <div class="upload-text">将课件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip">
                支持ppt、pptx、docx、pdf格式，大小不超过150MB
              </div>
            </div>

            <!-- 已上传文件时显示文件信息 -->
            <div v-else class="uploaded-file-content">
              <div class="file-info">
                <div class="file-icon-container">
                  <img :src="getFileIcon(fileList[0])" class="file-icon" alt="文件图标" />
                  <!-- 关闭按钮 -->
                  <div class="close-button" @click.stop="removeFile">
                    <el-icon><CloseBold /></el-icon>
                  </div>
                </div>
                <div class="file-details">
                  <div class="file-name">{{ fileList[0].name }}</div>
                </div>
              </div>

              <!-- 更换文件按钮 -->
              <div class="replace-button-container">
                <el-button type="primary" @click.stop="replaceFile" round>更换文件</el-button>
              </div>
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="其他要求">
          <el-input
            v-model="formData.otherRequirement"
            type="textarea"
            :rows="3"
            placeholder="请输入其他要求，例如：教学目标、重点难点、教学方法等"
            resize="none"
          />
        </el-form-item>

        <div style="display: flex; justify-content: center; width: 100%;" v-if="!loading">
          <el-button
            :type="loading ? 'warning' : 'primary'"
            size="large"
            v-click-throttle="handleGenerate"
            :disabled="!isFormValid"
            round
          >
            {{ loading ? '中止生成' : '开始生成' }}
          </el-button>
        </div>
      </el-form>
    </div>

    <ai-loading :visible="showAiLoading" />

    <div v-if="lessonResult" class="result-container">
      <div class="result-content">
        <div class="section-title-container">
          <div class="section-title">教案结果：</div>
          <div class="action-icons">
            <el-icon style="cursor: pointer;" @click="copyResult"><CopyDocument /></el-icon>
            <agent-save-button
              v-if="!loading && lessonResult"
              category="LessonAI"
              :user-question="savePrompt()"
              :model-answer="lessonResult"
              :chat-service="chatService"
              :already-saved="alreadySaved"
              @save-success="alreadySaved = true"
            />
          </div>
        </div>
        <div class="comment-text" v-html="renderedMarkdown(lessonResult)"></div>
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;" v-if="loading && lessonResult">
          <el-button
            type="warning"
            size="large"
            v-click-throttle="handleGenerate"
            round
          >中止生成 </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick, watch, onMounted, inject } from 'vue'
import { CopyDocument, Upload, CloseBold } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import MarkdownIt from "markdown-it"
import mk from "@iktakahiro/markdown-it-katex";
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { useStore } from 'vuex'
import { getFileIdByUpload, getPPTXText} from '@/api/home'
import AgentSaveButton from '@/components/common/AgentSaveButton.vue'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import AiLoading from '@/components/common/AiLoading.vue'

const roleSystem = `
角色设定：你是一名专业的教育专家，擅长将课件内容转化为结构化、专业的教案。

## 转化要求
1. 快速识别课件的主要内容和教学目标
2. 将课件内容转化为完整的教案格式，包括：
   - 教学目标：明确知识点、能力点和情感态度价值观目标
   - 教学重难点：突出本节课的重点和难点内容
   - 教学过程：包括导入、新课讲授、巩固练习、总结等环节
   - 板书设计：提供清晰的板书结构
   - 教学反思：提供可能的教学反思点
3. 语言表达专业、简洁、准确，符合教育教学规范

## 输出要求
- 保持原课件的核心内容和教学目标不变
- 提炼关键信息，突出教学重点
- 根据用户的其他要求进行针对性调整
- 优化教学结构，使教学流程更加清晰
- 增强教案的实用性和可操作性

## 重要：输出格式
- 各部分使用明确的标题
- 教学过程部分应详细说明各个环节的教学活动和时间分配
- 提供必要的教学提示和建议
- 整体结构清晰，便于教师快速掌握和使用
- 使用markdown格式输出结构化教案

请根据以上要求，对用户提供的课件内容进行专业转化，生成高质量的教案。
`

export default {
  name: 'LessonSummary',
  components: {
    CopyDocument,
    CloseBold,
    AgentSaveButton,
    AgentHistoryEntry,
    AiLoading
  },
  setup() {
    const trackingService = inject('trackingService');
    const store = useStore()
    const formData = ref({
      otherRequirement: ''
    })

    const fileList = ref([])
    const uploadRef = ref(null)
    const fileId = ref(null)
    const pptxContent = ref(null) // 存储PPTX文本内容
    const isPPTXFile = ref(false) // 标识是否为PPTX文件
    const lessonResult = ref(null)
    const loading = ref(false)
    const reader = ref(null)
    const containerRef = ref(null)
    const chatService = ref(null) // 聊天服务实例
    const alreadySaved = ref(false) // 是否已保存当前对话

    const showAiLoading = computed(() => loading.value && !lessonResult.value)

    // 表单验证
    const isFormValid = computed(() => {
      return fileList.value.length > 0 && (fileId.value || pptxContent.value);
    })

    // 处理文件变化
    const handleFileChange = async (file) => {
      if (file.status === 'ready') {
        // 更新文件列表，只保留最新的文件
        fileList.value = [file]

        // 检查是否为PPTX格式
        if (file.raw &&  ['application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.ms-powerpoint'].includes(file.raw.type)) {
          // PPTX格式特殊处理
          isPPTXFile.value = true
          fileId.value = null // 清空fileId

          try {
            const formDataObj = new FormData()
            formDataObj.append('file', file.raw)

            console.log('开始处理PPTX文件...')
            const response = await getPPTXText(formDataObj)
            console.log('PPTX文本提取响应:', response)

            if (response && response.code === 200) {
              pptxContent.value = response.data
              console.log('PPTX文本提取成功:', pptxContent.value)
            } else {
              ElMessage.error('PPTX文件处理失败，请重试')
              fileList.value = []
              isPPTXFile.value = false
              pptxContent.value = null
            }
          } catch (error) {
            console.error('PPTX文件处理错误:', error)
            fileList.value = []
            isPPTXFile.value = false
            pptxContent.value = null
          }
        } else {
          // 非PPTX格式，使用原有逻辑
          isPPTXFile.value = false
          pptxContent.value = null

          try {
            const formDataObj = new FormData()
            formDataObj.append('file', file.raw)

            const response = await getFileIdByUpload(formDataObj)
            if (response && response.code === 200) {
              fileId.value = response.data.id
              console.log('文件上传成功，fileId:', fileId.value)
            } else {
              fileList.value = []
            }
          } catch (error) {
            console.error('文件上传错误:', error)
            fileList.value = []
          }
        }
      }
    }

    // 处理文件超出限制（直接覆盖旧文件）
    const handleExceed = (files) => {
      // 先清除当前文件
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
      }
      fileList.value = []
      fileId.value = null
      pptxContent.value = null
      isPPTXFile.value = false

      // 添加新文件
      const file = files[0]
      uploadRef.value.handleStart(file)

      // 直接处理新文件
      handleFileChange(file)
    }

    // 处理文件移除
    const handleFileRemove = () => {
      fileList.value = []
      fileId.value = null
      pptxContent.value = null
      isPPTXFile.value = false
    }

     // 获取文件图标
    const getFileIcon = (file) => {
      const fileName = file.name.toLowerCase()
      if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) {
        return 'https://www.junhengyun.cn/cdn/wechat/file_icon/doc1.png'
      } else if (fileName.endsWith('.ppt') || fileName.endsWith('.pptx')) {
        return 'https://www.junhengyun.cn/cdn/wechat/file_icon/ppt1.png'
      } else if (fileName.endsWith('.pdf')) {
        return 'https://www.junhengyun.cn/cdn/wechat/file_icon/pdf1.png'
      } else {
        return 'https://www.junhengyun.cn/cdn/wechat/file_icon/doc1.png' // 默认图标
      }
    }

    // 替换文件
    const replaceFile = () => {
      // 触发文件选择
      if (uploadRef.value) {
        const input = uploadRef.value.$el.querySelector('input[type="file"]')
        if (input) {
          input.click()
        }
      }
    }

    // 删除文件
    const removeFile = () => {
      fileList.value = []
      fileId.value = null
      pptxContent.value = null
      isPPTXFile.value = false
    }

    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };

    // 监听结果变化，触发滚动
    watch(lessonResult, () => {
      scrollToBottom();
    });

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('LessonAI', '教案总结助手会话')
          console.log('教案总结助手重新初始化完成')
        }
      } catch (error) {
        console.error('教案总结助手重新初始化错误:', error)
      }
    }

    // 初始化聊天服务
    onMounted(async () => {
      try {
        chatService.value = new AgentChatService(store)
        await chatService.value.initialize('LessonAI', '教案总结助手会话')
        console.log('教案总结助手初始化完成')
      } catch (error) {
        console.error('初始化错误:', error)
      }
    })

    // 构建提示词方法
    const buildPrompt = () => {
      let prompt = `请将以下课件内容转化为完整的教案。`

      if (formData.value.otherRequirement) {
        prompt += `\n\n其他要求：${formData.value.otherRequirement}`
      }

      prompt += `\n\n请根据上传的课件内容进行转化。`

      return prompt
    }

    const savePrompt = () => {
      let prompt = ``
      
      if (fileList.value.length > 0) {
        prompt += `课件：${fileList.value[0].name}`
      }

      if (formData.value.otherRequirement) {
        prompt += `，其他要求：${formData.value.otherRequirement}`
      }

      return prompt
    }

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          // reader.value 现在是 AbortController
          reader.value.abort('用户取消了操作');
          console.log('Abort signal sent.');
        } catch (error) {
          console.error('取消流式输出错误:', error);
        } finally {
          // 立即重置状态，确保按钮恢复
          loading.value = false;
          reader.value = null; // 清理 controller 引用
        }
      }
    }

    // 生成教案结果
    const handleGenerate = async () => {
      if (!isFormValid.value) {
        ElMessage.error('请上传课件')
        return
      }

      if (loading.value && reader.value) {
        await cancelStream()
        return
      }

      // 检查文件是否已处理完成
      if (!isPPTXFile.value && !fileId.value) {
        ElMessage.error('请先上传课件')
        return
      }

      if (isPPTXFile.value && !pptxContent.value) {
        ElMessage.error('PPTX文件处理中，请稍候再试')
        return
      }

      lessonResult.value = null
      loading.value = true
      alreadySaved.value = false // 重置保存状态

      // 创建AbortController用于取消请求
      const controller = new AbortController();
      reader.value = controller; // 将 controller 赋值给 reader 用于取消

      try {
        // 构建提示词
        let prompt = buildPrompt()

        // 构建请求体
        // let message = [{ "role": "system", "content": roleSystem }]
        let message =[]
        if (isPPTXFile.value) {
          // PPTX文件：直接使用文本内容
          message.push({
            role: 'user',
            content: `课件内容：\n${pptxContent.value}\n\n${prompt}`
          })
        } else {
          // 其他文件：使用fileId
          message.push({
            role: 'system',
            content: `fileid://${fileId.value}`
          })

          // 添加用户提示词
          message.push({
            "role": "user",
            "content": prompt
          })
        }

        let requestBody = {
          messages: message,
          stream: true,
          modelType: 'open',
          formType: 'lesson_ai'
        }


        // 发送请求
        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`
        fetchEventSource(apiUrl,{
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal, // 传递 signal 用于取消
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
               loading.value = false; // 出错时停止加载状态
               reader.value = null;
              throw new Error(`HTTP error! status: ${response.status}`)
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              loading.value = false;
              reader.value = null;
              throw new Error(msg.data);
            }

            const data = msg.data;

            try {
              const jsonData = JSON.parse(data);

              if (jsonData.error) {
                loading.value = false;
                reader.value = null;
                return;
              }

              if (jsonData.choices && jsonData.choices[0]) {
                const delta_content = jsonData.choices[0].message?.content;
                if (delta_content) {
                  if (!lessonResult.value) {
                    lessonResult.value = delta_content;
                  } else {
                    lessonResult.value += delta_content;
                  }
                  scrollToBottom();
                }
              }
            } catch (error) {
              console.error('JSON parse error:', error);
            }
          },
          onclose() {
            console.log('连接已关闭');
            loading.value = false;
            reader.value = null;
          },
          onerror(err) {
            console.error('发生错误:', err);
            ElMessage.error('生成教案结果时发生错误，请重试');
            loading.value = false;
            reader.value = null;
          }
        });
      } catch (error) {
        console.error('生成教案结果错误:', error);
        ElMessage.error('生成教案结果失败，请重试');
        loading.value = false;
        reader.value = null;
      }
      trackingService.trackEvent('LessonAI')
    }

    // 复制结果
    const copyResult = () => {
      if (!lessonResult.value) return

      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div')
      tempElement.innerHTML = renderedMarkdown(lessonResult.value)

      // 获取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText

      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = textContent
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 尝试复制到剪贴板
        document.execCommand('copy')
        ElMessage.success('教案结果已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败')
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }

    // 清理markdown代码块标记
    const cleanMarkdownCodeBlocks = (content) => {
      if (!content) return content

      // 移除开头的 ```markdown 或 ```
      content = content.replace(/^```markdown\s*\n?/i, '')
      content = content.replace(/^```\s*\n?/, '')

      // 移除结尾的 ```
      content = content.replace(/\n?```\s*$/, '')

      return content
    }

    // Markdown渲染
    const renderedMarkdown = (markdownContent) => {
      if (!markdownContent) return

      // 预处理Markdown内容，将LaTeX格式的公式转换为KaTeX兼容格式
      let processedContent = markdownContent

      // 转换 \[ \] 格式为 $$ $$，并移除首尾多余空格
      processedContent = processedContent.replace(/\\\[\s*([\s\S]*?)\s*\\\]/g, (match, p1) => `$$${p1.trim()}$$`)

      // 转换 \( \) 格式为 $ $，并移除首尾多余空格
      processedContent = processedContent.replace(/\\\(\s*([\s\S]*?)\s*\\\)/g, (match, p1) => `$${p1.trim()}$`)

      // 保存并移除所有 $$ 包裹的内容，防止处理双美元符号内容
      const doubleDollarBlocks = [];
      processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, (match, content) => {
        doubleDollarBlocks.push(match);
        return `__DOUBLE_DOLLAR_PLACEHOLDER_${doubleDollarBlocks.length - 1}__`;
      });

      // 使用更健壮的正则表达式处理所有单美元符号的情况
      // 捕获成对的$，并处理其中可能存在的各种空格情况
      processedContent = processedContent.replace(/(\$)\s*([^\$]+?)\s*(\$)/g, (match, start, content, end) => {
        // 对内容进行处理，只移除首尾空格，保留内部空格
        return `${start}${content.trim()}${end}`;
      });

      // 恢复所有 $$ 包裹的内容
      doubleDollarBlocks.forEach((block, index) => {
        processedContent = processedContent.replace(`__DOUBLE_DOLLAR_PLACEHOLDER_${index}__`, block);
      });

      const md = new MarkdownIt();
      md.use(mk);
      const htmlContent = md.render(processedContent);
      return htmlContent;
    }

    return {
      formData,
      fileList,
      uploadRef,
      lessonResult,
      loading,
      isFormValid,
      handleFileChange,
      handleExceed,
      handleFileRemove,
      handleGenerate,
      renderedMarkdown,
      cleanMarkdownCodeBlocks,
      containerRef,
      copyResult,
      CopyDocument,
      Upload,
      CloseBold,
      chatService,
      buildPrompt,
      savePrompt,
      alreadySaved,
      reinitializeChatSession,
      pptxContent,
      isPPTXFile,
      showAiLoading,
      getFileIcon,
      replaceFile,
      removeFile
    }
  }
}
</script>

<style scope lang="scss">
.article-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 255, 0.08);
}

.article-form {
  width: 100%;
  margin: 0 auto;
}

.input-method-selector {
  margin-bottom: 1rem;
}

.upload-form-item {
  margin-top: -10px;
}

.file-uploader {
  width: 100%;
}

.el-upload-dragger {
  height: unset !important;
  padding: 0 !important;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.upload-icon {
  font-size: 48px;
  color: #909399;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.upload-text em {
  color: #409EFF;
  font-style: normal;
  font-weight: 600;
}

.el-upload__tip {
  margin-top: 0 !important;
  font-size: 14px !important;
  color: #909399;
  line-height: 1.5;
}

/* 已上传文件样式 */
.uploaded-file-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

/* 文件信息 */
.file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  text-align: center;
}

/* 文件图标容器 */
.file-icon-container {
  position: relative;
  display: inline-block;
}

.file-icon {
  width: 80px;
  height: 80px;
  object-fit: contain;
  flex-shrink: 0;
  display: block;
}

/* 关闭按钮 */
.close-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 14px;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.close-button:hover {
  background-color: #ff3742;
  transform: scale(1.1);
}

.file-details {
  text-align: center;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

/* 更换文件按钮容器 */
.replace-button-container {
  margin-top: 10px;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-content {
  text-align: left;
  padding: 2rem;
  position: relative;
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.action-icons {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comment-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .article-container {
    width: 100%;
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0;
  }

  .comment-text {
    font-size: 1rem;
    padding: 1rem;
  }

  /* 移动端文件信息样式 */
  .uploaded-file-content {
    padding: 15px 10px;
  }

  .file-icon {
    width: 60px;
    height: 60px;
  }

  .file-name {
    font-size: 14px;
    max-width: 250px;
  }

  .close-button {
    width: 20px;
    height: 20px;
    font-size: 12px;
    top: -6px;
    right: -6px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .form-container {
    margin: 2rem 0;
    padding: 1rem;
  }

  .result-content {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .comment-text {
    padding: 1rem;
  }
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}
</style>

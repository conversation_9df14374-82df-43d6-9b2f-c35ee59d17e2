<template>
  <div class="chat-controls">
    <!-- 语音输入按钮 - 主要按钮 -->
    <div
        class="voice-button"
        :class="{ active: startVoice, disabled: disabled }"
        @click="handleVoiceClick"
    >
      <el-icon class="voice-icon">
        <Microphone />
      </el-icon>
      <div class="voice-status" v-if="startVoice">
        <span class="status-text">正在倾听...</span>
        <div class="pulse-ring"></div>
      </div>
    </div>

    <!-- 键盘输入按钮 - 辅助按钮 -->
    <div class="keyboard-button" @click="handleKeyboardClick">
      <el-icon class="keyboard-icon">
        <Edit />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Microphone, Edit } from '@element-plus/icons-vue';
import { XfVoiceDictation } from '@muguilin/xf-voice-dictation';
import { ElMessage } from 'element-plus'

// 移除拖拽功能相关变量

// 讯飞语音识别相关
const talks = ref('');
const xfVoiceStatus = ref('');
const times = ref<any>(null);
const xfVoiceRef = ref<any>(null);

// 开始录音状态
const startVoice = ref(false);

// 定义props
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['onTalkStart', 'onTalking', 'onTalkEnd', 'onKeyboardClick']);

// 移除拖拽逻辑

// 语音按钮点击逻辑
const handleVoiceClick = () => {
  // 如果被禁用，不执行任何操作
  if (props.disabled) {
    console.log('语音识别已被禁用')
    return
  }

  if (startVoice.value) {
    stopRecording();
  } else {
    checkAndTestMicrophone();
  }
};

// 键盘按钮点击逻辑
const handleKeyboardClick = () => {
  // 如果正在进行语音识别，先停止语音识别
  // 这样可以确保用户在语音输入时点击键盘输入不会产生冲突
  if (startVoice.value) {
    stopRecording();
  }
  
  // 双重保险：强制确保语音按钮状态为初始状态
  setTimeout(() => {
    if (startVoice.value) {
      console.warn('检测到语音状态未正确重置，强制重置');
      startVoice.value = false;
      talks.value = '';
      xfVoiceStatus.value = '';
      if (xfVoiceRef.value) {
        try {
          xfVoiceRef.value.stop();
        } catch (e) {
          // 忽略错误
        }
        xfVoiceRef.value = null;
      }
    }
  }, 100);
  
  // 触发键盘输入事件
  emit('onKeyboardClick');
};

// 检查麦克风并开始录音
const checkAndTestMicrophone = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const hasMic = devices.some(device => device.kind === 'audioinput');

    if (!hasMic) {
      ElMessage.warning('未检测到麦克风，请连接或开启麦克风设备。');
      return;
    }

    const xfVoice = new XfVoiceDictation({
      APPID: '7de8fde2',
      APISecret: 'MjY3Y2YzZGRmZGIxNjAzY2ZkZTBmZTg5',
      APIKey: '127bc12ed8ca5e8a98664804359fcf76',
      onWillStatusChange: function (oldStatus, newStatus) {
        // 如果xfVoiceRef已被清空（手动停止），则忽略状态变化
        if (!xfVoiceRef.value) {
          console.log('语音识别实例已清空，忽略状态变化:', newStatus);
          return;
        }
        
        xfVoiceStatus.value = newStatus;
        if (newStatus === 'end') {
          emit('onTalkEnd', talks.value);
          startVoice.value = false;
        }
        if (newStatus === 'ing') {
          emit('onTalking', talks.value);
          startVoice.value = true;
        }
      },
      onTextChange: function (text) {
        // 如果xfVoiceRef已被清空（手动停止），则忽略文本变化
        if (!xfVoiceRef.value) {
          console.log('语音识别实例已清空，忽略文本变化:', text);
          return;
        }
        
        talks.value = text;
        startVoice.value = true;
        emit('onTalking', talks.value);
        if (text) {
          clearTimeout(times.value);
          times.value = setTimeout(() => {
            stopRecording();
          }, 3000);
        }
      },
      onError: function (error) {
        console.error('识别错误：', error);
        startVoice.value = false;
      }
    });

    xfVoiceRef.value = xfVoice;
    xfVoice.start();
    emit('onTalkStart', '开始录音');
  } catch (e) {
    console.error('麦克风检测失败：', e);
  }
};

// **终止录音函数**
const stopRecording = () => {
  if (xfVoiceRef.value) {
    try {
      xfVoiceRef.value.stop();
    } catch (error) {
      console.warn('停止语音识别时出现错误:', error);
    }
    // 强制重置状态，防止异步回调干扰
    xfVoiceRef.value = null;
  }
  
  // 强制重置所有相关状态
  startVoice.value = false;
  talks.value = '';
  xfVoiceStatus.value = '';
  clearTimeout(times.value);
  times.value = null;
  
  console.log('语音识别已停止，状态已重置');
};

// 暴露方法，供外部调用
defineExpose({
  stopRecording
});
</script>

<style scoped lang="scss">
.chat-controls {
  position: fixed;
  bottom: 50px;
  right: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  z-index: 99;
}

/* 语音按钮 - 主要圆形按钮 */
.voice-button {
  position: relative;
  cursor: pointer;
  user-select: none;
  touch-action: none;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 0 0 4px rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);

  &:hover {
    transform: translateY(-3px) scale(1.05);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow:
      0 12px 35px rgba(102, 126, 234, 0.6),
      0 0 0 6px rgba(255, 255, 255, 0.15);

    .voice-icon {
      transform: scale(1.2);
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
      0 6px 20px rgba(102, 126, 234, 0.5),
      0 0 0 4px rgba(255, 255, 255, 0.1);
  }

  .voice-icon {
    font-size: 32px;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }

  .voice-status {
    position: absolute;
    top: -45px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 20px;
    white-space: nowrap;
    border: 1px solid rgba(255, 255, 255, 0.2);

    .status-text {
      font-size: 12px;
      font-weight: 600;
      color: #ffffff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    }
  }

  .pulse-ring {
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border: 3px solid rgba(79, 172, 254, 0.6);
    border-radius: 50%;
    animation: pulse-ring 2s infinite;
  }
}

/* 键盘按钮 - 辅助小圆形按钮 */
.keyboard-button {
  cursor: pointer;
  user-select: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.3),
    0 0 0 2px rgba(255, 255, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.5);

  &:hover {
    transform: translateY(-2px) scale(1.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 0.95) 100%);
    color: #1a1a1a;
    box-shadow:
      0 6px 25px rgba(0, 0, 0, 0.4),
      0 0 0 3px rgba(255, 255, 255, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 1);

    .keyboard-icon {
      transform: scale(1.2);
    }
  }

  &:active {
    transform: translateY(0) scale(1.05);
    background: linear-gradient(135deg, rgba(240, 245, 251, 0.9) 0%, rgba(220, 230, 240, 0.8) 100%);
    box-shadow:
      0 2px 10px rgba(0, 0, 0, 0.25),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .keyboard-icon {
    font-size: 20px;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  }
}

/* 语音按钮激活状态 */
.voice-button.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  animation: voice-active-pulse 2s infinite;
  box-shadow:
    0 8px 30px rgba(79, 172, 254, 0.6),
    0 0 0 6px rgba(79, 172, 254, 0.3);

  .voice-icon {
    animation: voice-icon-pulse 1.5s infinite;
  }
}

/* 语音按钮禁用状态 */
.voice-button.disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: not-allowed;
  opacity: 0.6;

  &:hover {
    transform: none;
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    box-shadow:
      0 8px 25px rgba(102, 126, 234, 0.4),
      0 0 0 4px rgba(255, 255, 255, 0.1);

    .voice-icon {
      transform: none;
    }
  }

  &:active {
    transform: none;
    box-shadow:
      0 8px 25px rgba(102, 126, 234, 0.4),
      0 0 0 4px rgba(255, 255, 255, 0.1);
  }
}

/* 动画效果 */
@keyframes voice-active-pulse {
  0%, 100% {
    box-shadow:
      0 8px 30px rgba(79, 172, 254, 0.6),
      0 0 0 6px rgba(79, 172, 254, 0.3);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(79, 172, 254, 0.8),
      0 0 0 8px rgba(79, 172, 254, 0.5);
  }
}

@keyframes voice-icon-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}
</style>

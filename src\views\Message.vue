<template>
  <div class="article-container" ref="containerRef">
    <agent-history-entry
        category="MessageAI"
        @history-cleared="reinitializeChatSession"
    />

    <div class="header">
      <h1 class="title">智能通知助手</h1>
      <p class="subtitle">基于AI技术，快速生成各类校园通知内容，适用于各级学校的日常通知管理</p>
    </div>

    <div class="form-container">
      <el-form :model="formData" label-width="120px" class="article-form" size="large">
        <el-form-item label="通知类型" required>
          <div class="style-buttons">
            <el-button-group>
              <el-button
                  v-for="item in noticeTypeOptions"
                  :key="item.value"
                  :type="formData.noticeType === item.value ? 'primary' : ''"
                  @click="formData.noticeType = item.value"
                  round
              >
                {{ item.label }}
              </el-button>
            </el-button-group>
          </div>
        </el-form-item>

        <el-form-item label="通知简述" required>
          <el-input
              v-model="formData.briefDescription"
              type="textarea"
              :rows="6"
              placeholder="请输入通知的主要内容简述，例如：防溺水安全教育"
              resize="none"
          />
        </el-form-item>

        <el-form-item label="字数限制" required>
          <div class="style-buttons">
            <el-button-group>
              <el-button
                  v-for="item in wordCountOptions"
                  :key="item.value"
                  :type="formData.wordCount === item.value ? 'primary' : ''"
                  @click="formData.wordCount = item.value"
                  round
              >
                {{ item.label }}
              </el-button>
            </el-button-group>
          </div>
        </el-form-item>

        <div style="display: flex; justify-content: center; width: 100%;" v-if="!loading">
          <el-button
              :type="loading ? 'warning' : 'primary'"
              size="large"
              v-click-throttle="handleGenerate"
              :disabled="!isFormValid"
              round
          >
            {{ loading ? '中止生成' : (noticeResult ? '重新生成' : '开始生成') }}
          </el-button>
        </div>
      </el-form>
    </div>

    <ai-loading :visible="showAiLoading"/>

    <div v-if="noticeResult" class="result-container">
      <div class="result-content">
        <div class="section-title-container">
          <div class="section-title">通知内容：</div>
          <div class="action-icons">
            <el-icon style="cursor: pointer;" @click="copyResult">
              <CopyDocument/>
            </el-icon>
            <agent-save-button
                v-if="!loading && noticeResult"
                category="MessageAI"
                :user-question="savePrompt()"
                :model-answer="noticeResult"
                :chat-service="chatService"
                :already-saved="alreadySaved"
                @save-success="alreadySaved = true"
            />
          </div>
        </div>
        <div class="comment-text" v-html="renderedMarkdown(noticeResult)"></div>
        <div style="display: flex; justify-content: center; width: 100%; margin-top: 1rem;"
             v-if="loading && noticeResult">
          <el-button
              type="warning"
              size="large"
              v-click-throttle="handleGenerate"
              round
          >中止生成
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {ref, computed, nextTick, watch, onMounted, inject} from 'vue'
import {CopyDocument} from '@element-plus/icons-vue'
import {ElMessage} from 'element-plus'
import MarkdownIt from "markdown-it"
import {fetchEventSource} from '@microsoft/fetch-event-source'
import {useStore} from 'vuex'
import AgentSaveButton from '@/components/common/AgentSaveButton.vue'
import AgentChatService from '@/components/common/AgentChatService.js'
import AgentHistoryEntry from '@/components/common/AgentHistoryEntry.vue'
import AiLoading from '@/components/common/AiLoading.vue'

const roleSystem = `
角色设定：你是一名专业的学校通知撰写专家，擅长撰写各类校园通知，包括假期通知、安全公告、活动通知、紧急通知和日常提醒等。你的通知风格符合教育机构的正式性和专业性，同时根据不同场景调整语气和内容。

## 通知撰写要求
1. 根据用户选择的通知类型调整语言表达，始终保持学校通知的特点：
   - 假期通知：使用温馨、祝福的语言，包含假期安排、学生假期安全注意事项、假期作业安排、返校时间等教育相关内容
   - 安全公告：使用严肃、规范的语言，强调校园安全重要性和具体措施，包括消防安全、交通安全、食品安全、防欺凌等校园常见安全议题
   - 活动通知：使用活泼、吸引人的语言，包含校园活动时间、地点、参与方式、活动意义等，如运动会、艺术节、科技展、主题班会等
   - 紧急通知：使用简洁、明确的语言，突出校园紧急情况和应对措施，如极端天气停课、疫情防控、突发事件处理等
   - 日常提醒：使用友好、提示性的语言，清晰传达学校日常事务，如作息时间调整、考试安排、卫生检查、校服要求等
2. 使用符合教育行业特点的表达方式，体现教书育人的理念
3. 使用符合当前日期的时间信息，确保通知内容的时间逻辑合理
4. 非常重要：内容要详细充实，尽可能使用完整的段落和丰富的表达，确保通知内容全面、专业

## 特定假期信息
- 劳动节放假时间：5月1日至5月5日（共5天）
- 如果通知涉及劳动节假期，必须使用上述准确的放假时间

## 输出要求
- 通知内容应包含标题、正文和落款
- 标题应简洁明了，能够概括通知主要内容，使用"关于..."、"...的通知"等学校通知常见格式
- 正文应包含通知的具体内容，语言表达应符合通知类型的特点，并体现教育机构的特性
- 正文应分为多个段落，每个段落详细阐述一个方面，包含具体的措施、要求或安排
- 正文应包含适当的细节和例子，使通知内容更加具体和实用
- 落款应包含发布日期和发布单位（使用"XX学校"或相关部门如"教务处"、"学生处"等作为发布单位）
- 落款日期应符合实际情况
- 通知中的日期应与当前日期保持逻辑一致
- 整体格式规范，层次分明，符合学校公文的规范
- 尽量使用接近用户指定字数上限的内容，确保通知内容丰富详实

## 重要：输出格式
- 直接输出通知内容，不要添加任何前言、分析或说明
- 不要使用"通知内容如下："等引导语
- 不要解释你做了哪些修改或为什么做这些修改
- 不要在通知后添加字数统计或其他注释
- 只输出通知内容，没有其他任何内容
- 确保通知内容的时间信息合理，符合实际情况
- 再次强调：生成的内容应该详细充实，接近用户指定的字数上限

请根据以上要求，根据用户提供的通知类型、简述和当前日期，生成专业、规范、内容丰富的学校通知内容。
`

export default {
  name: 'Message',
  components: {
    CopyDocument,
    AgentSaveButton,
    AgentHistoryEntry,
    AiLoading
  },
  setup() {
    const trackingService = inject('trackingService');
    const store = useStore()
    const formData = ref({
      noticeType: '安全公告',
      briefDescription: '',
      wordCount: 200
    })

    const noticeResult = ref(null)
    const loading = ref(false)
    const reader = ref(null)
    const containerRef = ref(null)
    const chatService = ref(null) // 聊天服务实例
    const alreadySaved = ref(false) // 是否已保存当前对话

    // 通知类型选项
    const noticeTypeOptions = [
      {value: '安全公告', label: '安全公告'},
      {value: '假期通知', label: '假期通知'},
      {value: '活动通知', label: '活动通知'},
      {value: '紧急通知', label: '紧急通知'},
      {value: '日常提醒', label: '日常提醒'}
    ]

    // 字数选项
    const wordCountOptions = [
      {value: 50, label: '50字'},
      {value: 100, label: '100字'},
      {value: 200, label: '200字'},
      {value: 300, label: '300字'},
      {value: 500, label: '500字'}
    ]

    const showAiLoading = computed(() => loading.value && !noticeResult.value)

    // 表单验证
    const isFormValid = computed(() => {
      return formData.value.noticeType &&
          formData.value.briefDescription.trim() !== '' &&
          formData.value.wordCount
    })

    // 添加自动滚动到底部的函数
    const scrollToBottom = () => {
      nextTick(() => {
        if (containerRef.value) {
          const container = document.querySelector('.function-content');
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
          } else {
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      });
    };

    // 监听结果变化，触发滚动
    watch(noticeResult, () => {
      scrollToBottom();
    });

    // 重新初始化聊天会话
    const reinitializeChatSession = async () => {
      try {
        console.log('重新初始化聊天会话')
        if (chatService.value) {
          await chatService.value.initialize('MessageAI', '发通知智能体会话')
          console.log('发通知智能体重新初始化完成')
        }
      } catch (error) {
        console.error('发通知智能体重新初始化错误:', error)
      }
    }

    // 初始化聊天服务
    onMounted(async () => {
      try {
        chatService.value = new AgentChatService(store)
        await chatService.value.initialize('MessageAI', '发通知智能体会话')
        console.log('发通知智能体初始化完成')
      } catch (error) {
        console.error('初始化错误:', error)
      }
    })

    // 构建提示词方法
    const buildPrompt = () => {
      let prompt = `请根据以下信息生成一份学校${formData.value.noticeType}，字数控制在${formData.value.wordCount}字左右。请尽量使用接近${formData.value.wordCount}字的内容，确保通知内容详细充实。`
      prompt += `\n\n通知简述：${formData.value.briefDescription}`
      return prompt
    }

    const savePrompt = () => {
      let prompt = ``

      if (formData.value.noticeType) {
        prompt += `学校通知类型：${formData.value.noticeType}，`
      }

      if (formData.value.briefDescription) {
        prompt += `通知简述：${formData.value.briefDescription}，`
      }

      if (formData.value.wordCount) {
        prompt += `字数限制：${formData.value.wordCount}字`
      }

      return prompt
    }

    // 取消流式输出
    const cancelStream = async () => {
      if (reader.value) {
        try {
          // reader.value 现在是 AbortController
          reader.value.abort('用户取消了操作');
          console.log('Abort signal sent.');
        } catch (error) {
          console.error('取消流式输出错误:', error);
        } finally {
          // 立即重置状态，确保按钮恢复
          loading.value = false;
          reader.value = null; // 清理 controller 引用
        }
      }
    }

    // 复制通知结果
    const copyResult = () => {
      if (!noticeResult.value) return

      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div')
      tempElement.innerHTML = renderedMarkdown(noticeResult.value)

      // 获取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText

      // 创建一个textarea元素用于复制
      const textarea = document.createElement('textarea')
      textarea.value = textContent
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 使用现代剪贴板API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(textContent)
              .then(() => {
                ElMessage.success('学校通知内容已复制到剪贴板')
              })
              .catch((err) => {
                console.error('现代API复制失败:', err)
                // 降级使用旧方法
                document.execCommand('copy')
                ElMessage.success('学校通知内容已复制到剪贴板')
              })
        } else {
          // 降级使用旧方法
          document.execCommand('copy')
          ElMessage.success('学校通知内容已复制到剪贴板')
        }
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败')
      } finally {
        // 清理
        document.body.removeChild(textarea)
      }
    }

    // 生成通知
    const handleGenerate = async () => {
      if (!isFormValid.value) {
        ElMessage.error('请填写必填项')
        return
      }

      if (loading.value && reader.value) {
        await cancelStream()
        return
      }

      noticeResult.value = null
      loading.value = true
      alreadySaved.value = false // 重置保存状态

      // 创建AbortController用于取消请求
      const controller = new AbortController();
      reader.value = controller; // 将 controller 赋值给 reader 用于取消

      try {
        // 构建提示词
        let prompt = buildPrompt()

        // 构建请求体
        // let message = [{ "role": "system", "content": roleSystem }]
        let message = []
        message.push({
          "role": "user",
          "content": prompt
        })

        let requestBody = {
          messages: message,
          stream: true,
          modelType: 'agent',
          formType: 'notice'
        }

        let accumulatedText = '';
        // let baseUrl = import.meta.env.VITE_BASE_URL;
        const apiUrl = `${import.meta.env.VITE_BASE_URL}/junhengai/yxAgent/v2/ssePure`

        // 使用fetchEventSource代替fetch
        fetchEventSource(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Authorization': `Bearer ${store.getters.aiToken}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal, // 传递 signal 用于取消
          openWhenHidden: true,
          async onopen(response) {
            if (!response.ok) {
              loading.value = false; // 出错时停止加载状态
              reader.value = null;
              throw new Error(`HTTP error! status: ${response.status}`);
            }
          },
          onmessage(msg) {
            if (msg.event === 'FatalError') {
              loading.value = false;
              reader.value = null;
              throw new Error(msg.data);
            }

            const data = msg.data;

            try {
              const jsonData = JSON.parse(data);

              if (jsonData.error) {
                loading.value = false;
                reader.value = null;
                return;
              }

              if (jsonData.choices && jsonData.choices[0]) {
                const delta_content = jsonData.choices[0].message.content
                if (delta_content) {
                  accumulatedText += delta_content;
                  noticeResult.value = accumulatedText;
                  scrollToBottom();
                }
              }
              // 处理可能的输出格式差异
              if (jsonData.output && jsonData.output.text) {
                accumulatedText += jsonData.output.text;
                noticeResult.value = accumulatedText;
                scrollToBottom();
              }
            } catch (error) {
              console.error('JSON parse error:', error);
            }
          },
          async onclose() {
            console.log('连接关闭');
            // onclose 可能在收到所有数据后触发
            if (loading.value) { // 只有在仍在加载时才设置，避免覆盖[DONE]设置的状态
              loading.value = false;
              reader.value = null;
            }
          },
          onerror(err) {
            console.error('Stream error:', err);
            if (err.name !== 'AbortError') {
            } else {
              console.log('Stream aborted by user.');
              // 用户取消，状态已在 cancelStream 中处理
            }
            // 确保状态被重置，即使在AbortError情况下，以防万一
            loading.value = false;
            reader.value = null;
          }
        });
      } catch (error) {
        console.error('请求错误:', error);
        loading.value = false;
        reader.value = null;
        ElMessage.error('请求出错，请重试');
      }
      trackingService.trackEvent('MessageAI')
    }

    // Markdown渲染
    const renderedMarkdown = (markdownContent) => {
      if (!markdownContent) return

      const md = new MarkdownIt()
      const htmlContent = md.render(markdownContent)
      return htmlContent
    }

    return {
      formData,
      noticeTypeOptions,
      wordCountOptions,
      noticeResult,
      loading,
      isFormValid,
      handleGenerate,
      renderedMarkdown,
      containerRef,
      copyResult,
      CopyDocument,
      chatService,
      buildPrompt,
      savePrompt,
      alreadySaved,
      reinitializeChatSession,
      showAiLoading
    }
  }
}
</script>

<style scoped>
.article-container {
  width: 75vw;
  margin: 0 auto;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #333, #000);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.title::after {
  content: "📢";
  position: absolute;
  top: 0;
  right: -1.5rem;
  font-size: 2rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.form-container {
  margin: 2rem 0;
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 255, 0.08);
}

.article-form {
  width: 100%;
  margin: 0 auto;
}

.style-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.result-container {
  margin-top: 3rem;
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 150, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-content {
  text-align: left;
  padding: 2rem;
  position: relative;
  background-color: #f0f9ff;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px dashed #c0d8ff;
  padding-bottom: 0.8rem;
}

.section-title {
  font-weight: 600;
  color: #2c86ff;
  font-size: 1.4rem;
  letter-spacing: 0.05em;
  position: relative;
  margin-bottom: 0;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.8rem;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #2c86ff, transparent);
  border-radius: 3px;
}

.comment-text {
  color: #000 !important;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.8;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 150, 0.03);
}

.action-icons {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .article-container {
    width: 100%;
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .title::after {
    right: -1rem;
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem 1rem;
    border-radius: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin-bottom: 0;
  }

  .comment-text {
    font-size: 1rem;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }

  .form-container {
    margin: 2rem 0;
    padding: 1rem;
  }

  .result-content {
    padding: 1rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .comment-text {
    padding: 1rem;
  }
}

:deep(.el-button-group > .el-button) {
  border-radius: 20px !important;
  margin: 0 -1px;
}

:deep(.el-button-group > .el-button:first-child) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

:deep(.el-button-group > .el-button:last-child) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

:deep(.el-button-group > .el-button:not(:first-child):not(:last-child)) {
  border-radius: 0 !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}
</style>